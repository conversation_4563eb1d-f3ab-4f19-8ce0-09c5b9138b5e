servicePorts:
  - name: http
    port: 80
    protocol: TCP
    targetPort: 8082

envVariables:
  DATASOURCE_URL: #{DATASOURCE_URL}
  DATASOURCE_USER_NAME: #{DATASOURCE_USER_NAME}
  CJ_ETL_HOST: #{CJ_ETL_HOST}
  CJ_COMPANY_ID: #{CJ_COMPANY_ID}
  CJ_PROMOTIONAL_ID: #{CJ_PROMOTIONAL_ID}
  PROJECT_KEY: #{PROJECT_KEY}
  LEASIBILITY_S3_BUCKET_NAME: #{LEASIBILITY_S3_BUCKET_NAME}
  API_URL: #{API_URL}
  AUTH_URL: #{AUTH_URL}
  IMPORT_API_URL: #{IMPORT_API_URL}
  AWS_REGION: #{AWS_REGION}
  JAVA_TOOL_OPTIONS: "-Xms1400m -Xmx1400m -XX:+PrintCommandLineFlags"
  PL_SOVRN_S3_BUCKET_NAME: #{PL_SOVRN_S3_BUCKET_NAME}

#CyberArk
cyberark:
  enabled: true
  refreshIntervalSeconds: 30
  hotRefreshSecrets: false
  hostId: REPLACED-BY-OCTOPUS
  safe: REPLACED-BY-OCTOPUS
  sidecarImage: cyberark/conjur-authn-k8s-client
  applicationWorkingDirectory: /app/
  secrets:
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: DATASOURCE_PASSWORD
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: CLIENT_ID
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: CLIENT_SECRET
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: AWS_ETL_AMAZON_PAP_ACCESS_KEY
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: AWS_ETL_AMAZON_PAP_SECRET_KEY
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: AWS_ETL_AMAZON_PAP_PARTNER_TAG
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: IMPACT_ETL_USERNAME
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: IMPACT_ETL_PASSWORD
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: IMPACT_ETL_ACCOUNT_SID
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: CJ_QUERY_TOKEN
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: SERP_API_KEY
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: SOVRN_ACCESS_KEY
    - accountName: "REPLACED-BY-OCTOPUS"
      key: password
      envName: SOVRN_SECRET_KEY

apiVersion: v1
kind: ServiceAccount
metadata:
  name: marketplace-feeds-processor
  namespace: #{SERVICEACCOUNT_NAMESPACE}
  annotations:
    eks.amazonaws.com/role-arn: #{AWS_ARN}

appsettings:
  name: "marketplace-feeds-processor"
  filename: "application.yml"
  appsettingsUseNameValues: false  # Use this to toggle between values and pretty-printed JSON
  appsettingsValues:
    spring:
      application:
        name: "marketplace-feed-processor"
    logging:
      level:
        org:
          springframework:
            web: DEBUG
      loggers:
        org.hibernate: TRACE

# Kubernetes specific settings for deployment
resources:
  limits:
    cpu: 1000m
    memory: 2048Mi
  requests:
    cpu: 500m
    memory: 1024Mi

# Liveness and Readiness Probes for Kubernetes
livenessProbe:
  httpGet:
    path: /marketplace-feeds-processor/actuator/health/liveness
    port: 8082
  initialDelaySeconds: 30
  periodSeconds: 60
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /marketplace-feeds-processor/actuator/health/readiness
    port: 8082
  initialDelaySeconds: 30
  periodSeconds: 60
  failureThreshold: 3


autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

virtualServicePaths:
  - #{UriPrefix}
  - /api-docs
  - /v1

certificateFiles:
  domains:
    stormwind: true
    proginternal: true
  mountPath: /etc/ssl/certs/

awsParameterPrefix: ""
revisionHistoryLimit: 3
#authorizationPolicy:
#  namespace:
#    sourceSystems: [lsablty]
