plugins {
    id("org.springframework.boot") apply false
    id("io.spring.dependency-management")
    id("java")
}

group = "com.progleasing.marketplace"
version = "1.0.1"
description = "ETL process to fetch products using Google SERP"

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
}
val springBootVersion = "3.4.4"
val lombokVersion = "1.18.32"

dependencies {
    // Spring Boot dependencies
    implementation("org.springframework.boot:spring-boot-starter:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-json:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}")

    implementation("io.awspring.cloud:spring-cloud-aws-starter-sqs:3.0.4")
    implementation("software.amazon.awssdk:sqs:2.31.38")
    implementation("software.amazon.awssdk:sts:2.31.38")

    // PostgreSQL driver
    implementation("org.postgresql:postgresql:42.7.3")

    // JSON processing
    implementation("org.json:json:20240303")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.18.3")
    implementation(project(":marketplace-shared-model"))
    // Lombok
    compileOnly("org.projectlombok:lombok:$lombokVersion")
    annotationProcessor("org.projectlombok:lombok:$lombokVersion")
    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
}

tasks.test {
    useJUnitPlatform()
}