package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.SerpApiEtlConfig;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.exceptions.SerpApiFetchException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;

import java.util.*;

@Slf4j
@Service
public class GoogleSerpApiService {


    @Autowired
    private SerpApiEtlConfig serpApiEtlConfig;

    @Autowired
    private SerpProductPersistenceService serpProductPersistenceService;

    @Autowired
    private RestTemplate restTemplate;


    public void fetchAndPersistSerpData(String searchTerm, List<String> retailerNames, String categoryKey, String l1Category, String l2Category, EtlQueueStatusData status, Double minPrice, Double maxPrice){
        List<Map<String, Object>> productData;
        try {
            productData = fetchProductData(searchTerm, retailerNames, minPrice, maxPrice);
            log.info("Fetched {} products for search term: {}", productData.size(), searchTerm);
            serpProductPersistenceService.saveProducts(productData, searchTerm, categoryKey, l1Category, l2Category, status);
        } catch (SerpApiFetchException e) {
            productData = e.getPartialResults();
            log.warn("Partial fetch completed with {} products due to exception.", productData.size(), e);
            serpProductPersistenceService.saveProducts(productData, searchTerm, categoryKey, l1Category, l2Category, status);
            throw e;
        }
    }

    private List<Map<String, Object>> fetchProductData(String searchTerm, List<String> retailerNames, Double minPrice, Double maxPrice) {

        String baseUrl = String.format(
                "%s?q=%s&engine=%s&hl=%s&gl=%s&tbm=%s&direct_link=%s&location=%s&google_domain=%s",
                serpApiEtlConfig.getBaseUrl(),
                searchTerm.trim(),
                serpApiEtlConfig.getEngine(),
                serpApiEtlConfig.getHl(),
                serpApiEtlConfig.getGl(),
                serpApiEtlConfig.getTbm(),
                serpApiEtlConfig.getDirectLink(),
                serpApiEtlConfig.getLocation(),
                serpApiEtlConfig.getGoogleDomain()
        );

        Map<String, Map<String, Object>> productMap = new LinkedHashMap<>();
        Map<String, Integer> retailerCountMap = new HashMap<>();
        String nextPageUrl = baseUrl;

        while (nextPageUrl != null) {
            try {
                String urlWithKey = appendApiKey(nextPageUrl, serpApiEtlConfig.getApiKey());
                log.debug("Fetching: {}", urlWithKey);
                ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                        urlWithKey, HttpMethod.GET, HttpEntity.EMPTY, new ParameterizedTypeReference<>() {}
                );

                Map<String, Object> responseBody = response.getBody();
                if (responseBody == null || !responseBody.containsKey("shopping_results")) break;

                List<Map<String, Object>> results = (List<Map<String, Object>>) responseBody.get("shopping_results");

                results.stream()
                        .filter(product -> {
                            Object sourceObj = product.get("source");
                            Object extractedPriceObj = product.get("extracted_price");
                            boolean matchesRetailer = sourceObj instanceof String source &&
                                    retailerNames.stream()
                                            .map(String::toLowerCase)
                                            .anyMatch(retailer -> source.toLowerCase().contains(retailer));
                            if (extractedPriceObj instanceof Number priceNum) {
                                double price = priceNum.doubleValue();
                                if (minPrice != null && price < minPrice) return false;
                                if (maxPrice != null && price > maxPrice) return false;
                            }
                            return matchesRetailer;
                        })
                        .forEach(product -> {
                            String source = (String) product.get("source");
                            retailerNames.stream()
                                    .filter(retailer -> source.toLowerCase().contains(retailer.toLowerCase()))
                                    .findFirst()
                                    .ifPresent(retailer -> product.put("source", retailer));

                            String retailer = (String) product.get("source");
                            int retailerCount = retailerCountMap.getOrDefault(retailer, 0);

                            if (retailerCount < serpApiEtlConfig.getMaxResults()) {
                                String productId = (String) product.get("product_id");
                                if (productId != null) {
                                    List<String> thumbnails = new ArrayList<>();
                                    if (product.containsKey("thumbnail")) {
                                        thumbnails.add((String) product.get("thumbnail"));
                                    }
                                    if (product.containsKey("thumbnails")) {
                                        List<String> productThumbnails = (List<String>) product.get("thumbnails");
                                        if (productThumbnails != null) {
                                            thumbnails.addAll(productThumbnails);
                                        }
                                    }
                                    if (!thumbnails.isEmpty()) {
                                        product.put("thumbnails", thumbnails);
                                    }
                                    productMap.putIfAbsent(productId, product);
                                    retailerCountMap.put(retailer, retailerCount + 1);
                                }
                            }
                        });

                Map<String, Object> pagination = (Map<String, Object>) responseBody.get("serpapi_pagination");
                nextPageUrl = pagination != null ? (String) pagination.get("next") : null;

            } catch (Exception e) {
                log.error("Error calling SerpAPI", e);
                throw new SerpApiFetchException("Failed to fetch data from SerpAPI", e, new ArrayList<>(productMap.values()));
            }
        }

        return  new ArrayList<>(productMap.values());
    }

    private String appendApiKey(String url, String apiKey) {
        if (url.contains("api_key=")) return url;
        return url.contains("?") ? url + "&api_key=" + apiKey : url + "?api_key=" + apiKey;
    }
}
