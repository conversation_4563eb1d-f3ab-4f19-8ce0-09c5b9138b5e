package com.progleasing.marketplace.etl.processor;

import com.progleasing.marketplace.etl.service.GoogleSerpCatalogSyncService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;


@Service
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.serp.listener.enabled", havingValue = "true", matchIfMissing = true)
public class GoogleSerpMessageProcessor {

    private final GoogleSerpCatalogSyncService syncService;
    private final Executor executor;

    public GoogleSerpMessageProcessor(
            GoogleSerpCatalogSyncService syncService,
            @Qualifier("googleSerpTaskExecutor") Executor executor) {
        this.syncService = syncService;
        this.executor = executor;
    }

    @SqsListener(
            value = "${aws.sqs.serp.queue-name}",
            maxConcurrentMessages = "${aws.sqs.concurrent-message}",
            acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL)
    public void handle(List<Message<String>> messages) {
        log.info("Messages received: {}", messages.size());

        List<CompletableFuture<Void>> futures = messages.stream().map(message ->
                CompletableFuture.runAsync(() -> {
                    try {
                        UUID messageId = (UUID) message.getHeaders().get("id");
                        String payload = message.getPayload();
                        syncService.syncCatalogItems(payload, messageId.toString());
                        Acknowledgement.acknowledge(message);
                    } catch (Exception e) {
                        log.error("Error processing message", e);
                    }
                }, executor)
        ).toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
}
