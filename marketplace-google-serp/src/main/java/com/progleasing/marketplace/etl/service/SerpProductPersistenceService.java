package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.SerpApiEtlConfig;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.repositories.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SerpProductPersistenceService {

    private final ProductDataRepository productRepo;
    private final PriceDataRepository priceRepo;
    private final ImageDataRepository imageRepo;
    private final EtlQueueStatusDataRepository statusRepo;
    private final SerpApiEtlConfig serpApiEtlConfig ;
    private final AdditionalProductDataRepository additionalProductDataRepo;
    private final ProductDataDisabledRepository productDataDisabledRepository;



    public SerpProductPersistenceService(
            ProductDataRepository productRepo,
            PriceDataRepository priceRepo,
            ImageDataRepository imageRepo,
            EtlQueueStatusDataRepository statusRepo, SerpApiEtlConfig serpApiEtlConfig,
            AdditionalProductDataRepository additionalProductDataRepo, ProductDataDisabledRepository productDataDisabledRepository
    ) {
        this.productRepo = productRepo;
        this.priceRepo = priceRepo;
        this.imageRepo = imageRepo;
        this.statusRepo = statusRepo;
        this.serpApiEtlConfig = serpApiEtlConfig;
        this.additionalProductDataRepo = additionalProductDataRepo;
        this.productDataDisabledRepository = productDataDisabledRepository;
    }

    public void saveProducts(List<Map<String, Object>> products, String searchTerm, String categoryKey, String l1Category,
                             String l2Category, EtlQueueStatusData status) {
        List<ProductData> searchTermProductDataList = productRepo.findProductsBySearchTerm(searchTerm, ProductData.FeedSource.google);
        List<String> searchTermProductIds = searchTermProductDataList.stream().map(ProductData::getId).collect(Collectors.toList());
        List<String> disabledProductIds = new ArrayList<String>();
        List<ProductDataDisabled> productDataDisabledList = productDataDisabledRepository.findProductsByFeedSource(searchTerm, l1Category, l2Category,ProductData.FeedSource.google);
        if(!productDataDisabledList.isEmpty()){
            disabledProductIds = productDataDisabledList.stream().map(ProductDataDisabled :: getId).collect(Collectors.toList());
        }
        for(Map<String, Object> product : products){
            if(disabledProductIds.contains(product.getOrDefault("product_id", ""))){
                products.remove(product);
            }
        }

        Timestamp now = Timestamp.from(Instant.now());

        List<String> productIds = products.stream()
                .map(p -> (String) p.getOrDefault("product_id", ""))
                .filter(id -> !id.isEmpty())
                .toList();
        try {
            Map<String, ProductData> existingProducts = productRepo.findAllById(productIds).stream()
                    .collect(Collectors.toMap(ProductData::getId, Function.identity()));

            Map<String, PriceData> existingPrices = priceRepo.findAllById(productIds).stream()
                    .collect(Collectors.toMap(PriceData::getId, Function.identity()));

            List<ImageDataId> imageIds = products.stream()
                    .flatMap(p -> {
                        String productId = (String) p.getOrDefault("product_id", "");
                        List<String> thumbnails = (List<String>) p.get("thumbnails");
                        List<ImageDataId> ids = new ArrayList<>();

                        if (thumbnails != null) {
                            thumbnails.forEach(url -> {
                                if (url != null && !url.isBlank()) {
                                    ids.add(new ImageDataId(productId, url));
                                }
                            });
                        } else {
                            String singleThumbnail = (String) p.get("thumbnail");
                            if (singleThumbnail != null && !singleThumbnail.isBlank()) {
                                ids.add(new ImageDataId(productId, singleThumbnail));
                            }
                        }
                        return ids.stream();
                    })
                    .toList();

            List<ImageData> allExistingImages = imageRepo.findAllById(imageIds);

            Map<String, List<ImageData>> existingImagesByProductId = allExistingImages.stream()
                    .collect(Collectors.groupingBy(ImageData::getId));

            Map<String, AdditionalProductData> existingExtraData = additionalProductDataRepo.findAllById(productIds).stream()
                    .collect(Collectors.toMap(AdditionalProductData::getId, Function.identity()));

            List<ProductData> productsToSave = products.stream()
                    .map(p -> createProductEntity(p, searchTerm, categoryKey, l1Category, l2Category, now))
                    .filter(p -> {
                        ProductData existing = existingProducts.get(p.getId());
                        return existing == null || !areProductsEqualIgnoringAuditFields(p, existing);
                    })
                    .toList();

            List<PriceData> pricesToSave = products.stream()
                    .map(p -> createPriceEntity(p, now))
                    .filter(price -> {
                        PriceData existing = existingPrices.get(price.getId());
                        return existing == null || !(compareBigDecimal(price.getPrice(), existing.getPrice()) && compareBigDecimal(price.getSalePrice(), existing.getSalePrice()));
                    })
                    .toList();


            List<ImageData> allImages = products.stream()
                    .flatMap(p -> createImageEntities(p, now).stream())
                    .toList();

            List<ImageData> imagesToSave = allImages.stream()
                    .filter(image -> {
                        List<ImageData> existingForProduct = existingImagesByProductId.getOrDefault(image.getId(), List.of());
                        return existingForProduct.stream()
                                .noneMatch(existing ->
                                        existing.getImageUrl().equals(image.getImageUrl())
                                );
                    })
                    .toList();

            List<AdditionalProductData> extraDataToSave = products.stream()
                    .map(p -> createAdditionalProductData(p, now))
                    .filter(Objects::nonNull)
                    .filter(newData -> {
                        AdditionalProductData existing = existingExtraData.get(newData.getId());
                        return existing == null || !Objects.equals(existing.getOtherInfo(), newData.getOtherInfo());
                    })
                    .toList();

            log.info("Products to save: {}, Prices to save: {}, Images to save: {}, Additional Data to save: {}",
                    productsToSave.size(), pricesToSave.size(), imagesToSave.size(), extraDataToSave.size());
            if (!productsToSave.isEmpty()) productRepo.saveAll(productsToSave);
            if (!pricesToSave.isEmpty()) priceRepo.saveAll(pricesToSave);
            if (!imagesToSave.isEmpty()) imageRepo.saveAll(imagesToSave);
            if (!extraDataToSave.isEmpty()) additionalProductDataRepo.saveAll(extraDataToSave);

            searchTermProductIds.removeAll(productsToSave.stream().map(ProductData::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(searchTermProductIds) && serpApiEtlConfig.isDeleteProducts()) {
                productRepo.markForDeletion(searchTermProductIds, Timestamp.valueOf(LocalDateTime.now().plusDays(serpApiEtlConfig.getGracePeriodDays())));
                log.info("Product marked for deletion, productIds :{}", searchTermProductIds);
            }else{
                log.info("No products there marked for deletion");
            }

            log.info("Batch save via JPA completed.");
            updateStatus(status);
        } catch (Exception e) {
            log.error("JPA save failed", e);
            throw new RuntimeException("Failed to save products, prices, and images", e);
        }
    }

    private void updateStatus(EtlQueueStatusData status) {
        try {
            status.setStatus(EtlQueueStatusData.Status.COMPLETED);
            status.setLastUpdate(Timestamp.from(Instant.now()));
            statusRepo.save(status);
            log.info("Successfully updated status to COMPLETED for {}", status.getEtlID());
        } catch (Exception e) {
            log.error("Failed to update status to COMPLETED for {}: {}", status.getEtlID(), e.getMessage(), e);
        }
    }

    private boolean areProductsEqualIgnoringAuditFields(ProductData a, ProductData b) {
        if (a == b) return true;
        if (a == null || b == null) return false;

        return Objects.equals(a.getName(), b.getName()) &&
                Objects.equals(a.getDescription(), b.getDescription()) &&
                Objects.equals(a.getLongDescription(), b.getLongDescription()) &&
                Objects.equals(a.getRetailerName(), b.getRetailerName()) &&
                Objects.equals(a.getRetailerKey(), b.getRetailerKey()) &&
                Objects.equals(a.getBrand(), b.getBrand()) &&
                compareBigDecimal(a.getRatingAverage(), b.getRatingAverage()) &&
                Objects.equals(a.getRatingCount(), b.getRatingCount()) &&
                Objects.equals(a.getProductTrackingUrl(), b.getProductTrackingUrl()) &&
                Objects.equals(a.getAffiliateAddToCartUrl(), b.getAffiliateAddToCartUrl()) &&
                Objects.equals(a.getSearchTerm(), b.getSearchTerm()) &&
                Objects.equals(a.getCategoryKey(), b.getCategoryKey()) &&
                Objects.equals(a.getL1CategoryName(), b.getL1CategoryName()) &&
                Objects.equals(a.getL2CategoryName(), b.getL2CategoryName()) &&
                Objects.equals(a.getFeedSource(), b.getFeedSource());
    }

    private ProductData createProductEntity(Map<String, Object> p, String searchTerm, String categoryKey,
                                            String l1Category, String l2Category, Timestamp now) {
        String productId = (String) p.getOrDefault("product_id", "");
        String retailerName = (String) p.getOrDefault("source", "");
        String title = (String) p.getOrDefault("title", "");
        String description = (String) p.get("description");
        String longDescription = (String) p.get("long_description");
        ProductData entity = new ProductData();
        entity.setId(productId);
        entity.setName(title);
        entity.setDescription(description != null && !description.isBlank() ? description : title);
        entity.setLongDescription(longDescription != null && !longDescription.isBlank() ? longDescription : "");
        entity.setLastModified(now);
        entity.setRetailerName(retailerName);
        entity.setRetailerKey(retailerName.toLowerCase().replaceAll("[^a-z0-9]", ""));
        entity.setBrand((String) p.getOrDefault("brand", ""));
        entity.setRatingAverage(parseBigDecimal(p.get("rating")));
        entity.setRatingCount(parseIntOrDefault(p.get("reviews")));
        entity.setProductTrackingUrl((String) p.getOrDefault("product_link", ""));
        entity.setAffiliateAddToCartUrl((String) p.getOrDefault("link", ""));
        entity.setSearchTerm(searchTerm);
        entity.setCategoryKey(categoryKey);
        entity.setL1CategoryName(l1Category);
        entity.setL2CategoryName(l2Category);
        entity.setFeedSource(ProductData.FeedSource.google);
        return entity;
    }

    private PriceData createPriceEntity(Map<String, Object> p, Timestamp now) {
        BigDecimal extractedPrice = parseBigDecimalForPrice(p.get("extracted_price"));
        BigDecimal extractedOldPrice = parseBigDecimalForPrice(p.get("extracted_old_price"));
        PriceData price = new PriceData();
        price.setId((String) p.getOrDefault("product_id", ""));
        price.setPrice(extractedOldPrice != null ? extractedOldPrice : extractedPrice);
        price.setSalePrice(extractedOldPrice != null ? extractedPrice : null);
        price.setLastModified(now);
        return price;
    }

    private List<ImageData> createImageEntities(Map<String, Object> p, Timestamp now) {
        String productId = (String) p.getOrDefault("product_id", "");
        List<ImageData> images = new ArrayList<>();

        List<String> thumbnails = (List<String>) p.get("thumbnails");
        if (thumbnails != null && !thumbnails.isEmpty()) {
            for (String imageUrl : thumbnails) {
                if (imageUrl != null && !imageUrl.isBlank()) {
                    ImageData image = new ImageData();
                    image.setId(productId);
                    image.setImageUrl(imageUrl);
                    image.setLastModified(now);
                    images.add(image);
                }
            }
        } else {
            String singleThumbnail = (String) p.get("thumbnail");
            if (singleThumbnail != null && !singleThumbnail.isBlank()) {
                ImageData image = new ImageData();
                image.setId(productId);
                image.setImageUrl(singleThumbnail);
                image.setLastModified(now);
                images.add(image);
            }
        }
        return images;
    }

    private AdditionalProductData createAdditionalProductData(Map<String, Object> p, Timestamp now) {
        String productId = (String) p.getOrDefault("product_id", "");
        if (productId.isBlank()) return null;

        Map<String, Object> otherInfoMap = new HashMap<>();
        if (p.get("source_icon") != null) {
            otherInfoMap.put("source_icon", p.get("source_icon"));
        }
        if (p.get("snippet") != null) {
            otherInfoMap.put("snippet", p.get("snippet"));
        }

        if (otherInfoMap.isEmpty()) return null;

        AdditionalProductData data = new AdditionalProductData();
        data.setId(productId);
        data.setLastModified(now);

        try {
            String otherInfoJson = new ObjectMapper()
                    .writeValueAsString(otherInfoMap);
            data.setOtherInfo(otherInfoJson);
        } catch (Exception e) {
            log.warn("Failed to serialize otherInfo for product {}: {}", productId, e.getMessage());
        }

        return data;
    }

    private BigDecimal parseBigDecimal(Object obj) {
        try {
            return obj == null ? BigDecimal.ZERO : new BigDecimal(obj.toString());
        } catch (Exception e) {
            return null;
        }
    }

    private BigDecimal parseBigDecimalForPrice(Object obj) {
        try {
            return obj == null ? null : new BigDecimal(obj.toString());
        } catch (Exception e) {
            return null;
        }
    }

    private int parseIntOrDefault(Object obj) {
        try {
            return obj == null ? 0 : Integer.parseInt(obj.toString());
        } catch (Exception e) {
            return 0;
        }
    }

    private boolean compareBigDecimal(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) return true;
        if (a == null || b == null) return false;
        return a.compareTo(b) == 0;
    }
}
