package com.progleasing.marketplace.etl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "serp.etl")
public class SerpApiEtlConfig {

    private String baseUrl;
    private String apiKey;
    private String engine;
    private String hl;
    private String gl;
    private String tbm;
    private Boolean directLink;
    private String location;
    private String googleDomain;
    private int maxResults;
    private int gracePeriodDays;
    private boolean deleteProducts;
}
