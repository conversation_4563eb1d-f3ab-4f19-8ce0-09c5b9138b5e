package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;

@Service
@Slf4j
@AllArgsConstructor
public class GoogleSerpCatalogSyncService {


    private ObjectMapper objectMapper;
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private GoogleSerpApiService googleSerpApiService;

    public void syncCatalogItems(String messagePayload, String messageId) throws Exception {
        ETLMessageDTO messageDTO = objectMapper.readValue(messagePayload, ETLMessageDTO.class);
        log.info("Processing Google serp Query for l1 category {} l2 category {} searchTerm {} retailerName {}", messageDTO.getL1CategoryName(), messageDTO.getL2CategoryName(), messageDTO.getSearchTerm(), messageDTO.getRetailerName());
        EtlQueueStatusData queueStatusData = updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.STARTED);
        try{
            List<String> retailers = Arrays.asList(messageDTO.getRetailerName().split("\\s*,\\s*", -1));
            Double[] priceRange = validateAndExtractPriceRange(messageDTO.getMinPrice(), messageDTO.getMaxPrice());
            Double minPrice = priceRange[0];
            Double maxPrice = priceRange[1];
            googleSerpApiService.fetchAndPersistSerpData(messageDTO.getSearchTerm(),retailers,messageDTO.getCategoryKey(),messageDTO.getL1CategoryName(),messageDTO.getL2CategoryName(),queueStatusData,minPrice,maxPrice);
            updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.COMPLETED);
        }catch (Exception e) {
            log.error("Catalog item sync failed for category ID {}: {}", "messageDTO.getCategory()", e.getMessage(), e);
            updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING);
            throw e;
        }
    }

    private Double[] validateAndExtractPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        if (minPrice != null && maxPrice != null && minPrice.compareTo(maxPrice) >= 0) {
            log.warn("Invalid price range ignored: minPrice {} >= maxPrice {}", minPrice, maxPrice);
            return new Double[]{null, null};
        }

        Double min = minPrice != null ? minPrice.doubleValue() : null;
        Double max = maxPrice != null ? maxPrice.doubleValue() : null;

        return new Double[]{min, max};
    }

    @Transactional
    private EtlQueueStatusData updateStatusQueue(ETLMessageDTO messageDTO, String messageId, EtlQueueStatusData.Status status) {
        try {
            String messageData = String.format(
                    "%s|%s|%s|%s|%s|%s|%s|%s|%s",
                    messageId,
                    messageDTO.getSearchTerm(),
                    messageDTO.getCategoryKey(),
                    messageDTO.getRetailerName(),
                    messageDTO.getRetailerKey(),
                    messageDTO.getMinPrice(),
                    messageDTO.getMaxPrice(),
                    messageDTO.getL1CategoryName(),
                    messageDTO.getL2CategoryName()
            );
            EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_SEARCH;
            Optional<EtlQueueStatusData> existing =
                    etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(
                            messageDTO.getReferenceId(),
                            ProductData.FeedSource.google.name(),
                            etlStep,
                            messageId
                    );
            EtlQueueStatusData etlQueueStatusData;
            if (existing.isPresent()) {
                etlQueueStatusData = existing.get();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
            } else {
                etlQueueStatusData = new EtlQueueStatusData();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setEtlStep(etlStep);
                etlQueueStatusData.setEtlID(messageDTO.getReferenceId());
                etlQueueStatusData.setMessageId(messageId);
                etlQueueStatusData.setSqsMessageData(messageData);
                etlQueueStatusData.setRetailerSearch(ProductData.FeedSource.google.name());
                if (status == EtlQueueStatusData.Status.STARTED) {
                    etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
                }
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
            }
            etlQueueStatusDataRepository.save(etlQueueStatusData);
            etlQueueStatusDataRepository.flush();
            log.info("Queue status saved successfully for message ID {} import id {} to {}", messageId,messageDTO.getReferenceId(),status);
            return etlQueueStatusData;
        }catch (Exception e) {
            log.error("Error updating queue status for {} referenceId {}",messageId,messageDTO.getReferenceId());
            throw e;
        }
    }

}
