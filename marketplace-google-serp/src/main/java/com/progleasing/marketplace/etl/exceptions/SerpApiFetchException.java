package com.progleasing.marketplace.etl.exceptions;

import java.util.List;
import java.util.Map;

public class SerpApiFetchException extends RuntimeException {
    private final List<Map<String, Object>> partialResults;

    public SerpApiFetchException(String message, Throwable cause, List<Map<String, Object>> partialResults) {
        super(message, cause);
        this.partialResults = partialResults;
    }

    public List<Map<String, Object>> getPartialResults() {
        return partialResults;
    }
}

