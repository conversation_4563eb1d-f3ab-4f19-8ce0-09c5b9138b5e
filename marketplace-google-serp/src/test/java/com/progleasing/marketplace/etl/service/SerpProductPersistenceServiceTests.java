package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.repositories.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.StreamSupport;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static java.util.stream.StreamSupport.stream;

@ExtendWith(MockitoExtension.class)
class SerpProductPersistenceServiceTests {

    @Mock private ProductDataRepository productRepo;
    @Mock private PriceDataRepository priceRepo;
    @Mock private ImageDataRepository imageRepo;
    @Mock private EtlQueueStatusDataRepository statusRepo;

    @Mock
    private AdditionalProductDataRepository additionalProductDataRepo;

    @InjectMocks
    private SerpProductPersistenceService service;

    private EtlQueueStatusData status;

    @Mock
    private ProductDataDisabledRepository productDataDisabledRepository;

    private final String l1CategoryName = "l1Cat";
    private final String l2CategoryName = "l2Cat";

    @BeforeEach
    void setup() {
        status = new EtlQueueStatusData();
        status.setEtlID("etl_001");
        status.setStatus(EtlQueueStatusData.Status.STARTED);
    }

    @Test
    void testSaveProducts_savesNewData() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Test Product");
        product.put("source", "RetailerX");
        product.put("extracted_price", "99.99");
        product.put("thumbnail", "http://image.com/img.jpg");

        List<Map<String, Object>> products = List.of(product);

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        service.saveProducts(products, "laptop", "electronics", l1CategoryName, l2CategoryName, status);

        verify(productRepo).saveAll(argThat(it ->
                stream(it.spliterator(), false).count() == 1));
        verify(priceRepo).saveAll(argThat(it ->
                stream(it.spliterator(), false).count() == 1));
        verify(imageRepo).saveAll(argThat(it ->
                stream(it.spliterator(), false).count() == 1));
        verify(statusRepo).save(status);
        assertEquals(EtlQueueStatusData.Status.COMPLETED, status.getStatus());
    }

    @Test
    void testSaveProducts_savesAdditionalInfo() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Product with additional info");
        product.put("source", "RetailerX");
        product.put("source_icon", "icon.png");
        product.put("snippet", "some snippet");

        List<Map<String, Object>> products = List.of(product);

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(additionalProductDataRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        service.saveProducts(products, "search", "category", l1CategoryName, l2CategoryName, status);

        verify(additionalProductDataRepo).saveAll(argThat(iterable -> {
            List<AdditionalProductData> list = StreamSupport.stream(iterable.spliterator(), false)
                    .toList();
            return list.size() == 1 &&
                    list.get(0).getId().equals("p1") &&
                    list.get(0).getOtherInfo() != null && !list.get(0).getOtherInfo().isBlank();
        }));
        verify(statusRepo).save(status);
        assertEquals(EtlQueueStatusData.Status.COMPLETED, status.getStatus());
    }

    @Test
    void testSaveProducts_skipsUnchangedProduct() {
        ProductData existing = createExistingProduct();

        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Same Product");
        product.put("description", "Same Product");
        product.put("long_description", "");
        product.put("source", "RetailerX");
        product.put("brand", "");
        product.put("rating", 4.7);
        product.put("reviews", 440);
        product.put("product_link", "https://sample.com");
        product.put("link", "");
        product.put("searchTerm", "search");
        product.put("categoryKey", "category");
        product.put("l1CategoryName", "l1Cat");
        product.put("l2CategoryName", "l2Cat");

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of(existing));
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of(createExistingPrice()));
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        service.saveProducts(List.of(product), "search", "category", l1CategoryName, l2CategoryName, status);

        verify(productRepo, never()).saveAll(any());
        verify(priceRepo, never()).saveAll(any());
        verify(imageRepo, never()).saveAll(any());
        verify(additionalProductDataRepo, never()).saveAll(any());
    }

    private PriceData createExistingPrice() {
        PriceData price = new PriceData();
        price.setId("p1");
        price.setPrice(null);
        price.setSalePrice(null);
        return price;
    }

    private ProductData createExistingProduct() {
        ProductData product = new ProductData();
        product.setId("p1");
        product.setName("Same Product");
        product.setDescription("Same Product");
        product.setRetailerName("RetailerX");
        product.setRetailerKey("RetailerX".toLowerCase().replaceAll("[^a-z0-9]", ""));
        product.setBrand("");
        product.setRatingAverage(BigDecimal.valueOf(4.7));
        product.setRatingCount(440);
        product.setAffiliateAddToCartUrl("");
        product.setProductTrackingUrl("https://sample.com");
        product.setSearchTerm("search");
        product.setCategoryKey("category");
        product.setL1CategoryName("l1Cat");
        product.setL2CategoryName("l2Cat");
        product.setFeedSource(ProductData.FeedSource.google);
        product.setLongDescription("");
        return product;
    }

    @Test
    void testSaveProducts_handlesInvalidPriceGracefully() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Invalid Price Product");
        product.put("source", "RetailerX");
        product.put("extracted_price", "not_a_price");

        when(productRepo.findAllById(any())).thenReturn(List.of());
        when(priceRepo.findAllById(any())).thenReturn(List.of());
        when(imageRepo.findAllById(any())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        assertDoesNotThrow(() ->
                service.saveProducts(List.of(product), "search", "category", l1CategoryName, l2CategoryName, status)
        );
    }

    @Test
    void testSaveProducts_throwsExceptionOnFailure() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Test Product");
        product.put("source", "RetailerX");

        when(productRepo.findAllById(any())).thenThrow(new RuntimeException("DB error"));
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        RuntimeException ex = assertThrows(RuntimeException.class, () ->
                service.saveProducts(List.of(product), "search", "cat", l1CategoryName, l2CategoryName, status)
        );

        assertTrue(ex.getMessage().contains("Failed to save products"));
    }

    @Test
    void testSaveProducts_handlesMultipleImages() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Product with Multiple Images");
        product.put("source", "RetailerX");
        product.put("extracted_price", "79.99");
        product.put("thumbnails", List.of("http://image.com/img1.jpg", "http://image.com/img2.jpg", "http://image.com/img3.jpg"));

        List<Map<String, Object>> products = List.of(product);

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        service.saveProducts(products, "laptop", "electronics", l1CategoryName, l2CategoryName, status);

        verify(imageRepo).saveAll(argThat(images -> {
            return stream(images.spliterator(), false)
                    .filter(img -> "p1".equals(img.getId()))
                    .count() == 3;
        }));

        assertEquals(EtlQueueStatusData.Status.COMPLETED, status.getStatus());
    }

    @Test
    void testSaveProducts_skipsExistingImage() {
        String productId = "p1";
        String imageUrl = "http://image.com/img1.jpg";

        Map<String, Object> product = new HashMap<>();
        product.put("product_id", productId);
        product.put("title", "Test Product");
        product.put("source", "RetailerX");
        product.put("thumbnails", List.of(imageUrl));

        List<Map<String, Object>> products = List.of(product);

        ImageData existingImage = new ImageData();
        existingImage.setId(productId);
        existingImage.setImageUrl(imageUrl);
        existingImage.setLastModified(new java.sql.Timestamp(System.currentTimeMillis()));

        when(productRepo.findAllById(List.of(productId))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of(productId))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of(existingImage));
        when(additionalProductDataRepo.findAllById(List.of(productId))).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        service.saveProducts(products, "search", "category", "l1Cat", "l2Cat", status);

        verify(imageRepo, never()).saveAll(any());
    }
}
