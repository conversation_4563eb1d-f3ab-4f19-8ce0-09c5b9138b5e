package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GoogleSerpCatalogSyncServiceTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @Mock
    private GoogleSerpApiService googleSerpApiService;

    @InjectMocks
    private GoogleSerpCatalogSyncService service;

    @Test
    void syncCatalogItems_successfulFlow_shouldCallFetchAndPersist() throws Exception {
        String messagePayload = "{\"searchTerm\":\"shoes\",\"category\":\"footwear\",\"retailerName\":\"retailer1,retailer2\",\"referenceId\":\"ref123\",\"retailerKey\":\"key1\",\"l1CategoryName\":\"l1Cat\",\"l2CategoryName\":\"l2Cat\"}";
        String messageId = "msg-001";

        ETLMessageDTO dto = new ETLMessageDTO();
        dto.setSearchTerm("shoes");
        dto.setCategoryKey("footwear");
        dto.setRetailerName("retailer1,retailer2");
        dto.setReferenceId("ref123");
        dto.setRetailerKey("key1");
        dto.setL1CategoryName("l1Cat");
        dto.setL2CategoryName("l2Cat");

        when(objectMapper.readValue(eq(messagePayload), eq(ETLMessageDTO.class))).thenReturn(dto);
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(anyString(), anyString(), any(),anyString()))
                .thenReturn(Optional.empty());
        when(etlQueueStatusDataRepository.save(any(EtlQueueStatusData.class))).thenAnswer(invocation -> invocation.getArgument(0));

        service.syncCatalogItems(messagePayload, messageId);

        verify(etlQueueStatusDataRepository, times(2)).save(any(EtlQueueStatusData.class));
        verify(googleSerpApiService).fetchAndPersistSerpData(
                eq(dto.getSearchTerm()),
                eq(java.util.Arrays.asList(dto.getRetailerName().split("\\s*,\\s*"))),
                eq(dto.getCategoryKey()),
                eq(dto.getL1CategoryName()),
                eq(dto.getL2CategoryName()),
                any(EtlQueueStatusData.class),
                nullable(Double.class),
                nullable(Double.class));
    }

    @Test
    void syncCatalogItems_apiServiceThrows_shouldUpdateStatusAndRethrow() throws Exception {
        String messagePayload = "{\"searchTerm\":\"shoes\",\"category\":\"footwear\",\"retailerName\":\"retailer1\",\"referenceId\":\"ref123\",\"l1CategoryName\":\"l1Cat\",\"l2CategoryName\":\"l2Cat\"}";
        String messageId = "msg-002";

        ETLMessageDTO dto = new ETLMessageDTO();
        dto.setSearchTerm("shoes");
        dto.setCategoryKey("footwear");
        dto.setRetailerName("retailer1");
        dto.setReferenceId("ref123");
        dto.setL1CategoryName("l1Cat");
        dto.setL2CategoryName("l2Cat");

        when(objectMapper.readValue(eq(messagePayload), eq(ETLMessageDTO.class))).thenReturn(dto);
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(anyString(), anyString(), any(),anyString()))
                .thenReturn(Optional.empty());
        when(etlQueueStatusDataRepository.save(any(EtlQueueStatusData.class))).thenAnswer(invocation -> invocation.getArgument(0));

        doThrow(new RuntimeException("API failure"))
                .when(googleSerpApiService)
                .fetchAndPersistSerpData(anyString(), anyList(), anyString(), anyString(), anyString(), any(EtlQueueStatusData.class), any(), any());

        assertThrows(RuntimeException.class, () -> service.syncCatalogItems(messagePayload, messageId));

        verify(etlQueueStatusDataRepository, times(2)).save(any(EtlQueueStatusData.class));
    }

    @Test
    void syncCatalogItems_invalidPriceRange_shouldIgnoreAndPassNullsToApiService() throws Exception {
        String messagePayload = "{\"searchTerm\":\"laptop\",\"category\":\"electronics\",\"retailerName\":\"bestbuy\",\"referenceId\":\"ref001\",\"retailerKey\":\"bb\",\"minPrice\":500,\"maxPrice\":400,\"l1CategoryName\":\"l1Cat\",\"l2CategoryName\":\"l2Cat\"}";
        String messageId = "msg-003";

        ETLMessageDTO dto = new ETLMessageDTO();
        dto.setSearchTerm("laptop");
        dto.setCategoryKey("electronics");
        dto.setRetailerName("bestbuy");
        dto.setReferenceId("ref001");
        dto.setRetailerKey("bb");
        dto.setMinPrice(new BigDecimal(500));
        dto.setMaxPrice(new BigDecimal(400));
        dto.setL1CategoryName("l1Cat");
        dto.setL2CategoryName("l2Cat");

        when(objectMapper.readValue(eq(messagePayload), eq(ETLMessageDTO.class))).thenReturn(dto);
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(anyString(), anyString(), any(),anyString()))
                .thenReturn(Optional.empty());
        when(etlQueueStatusDataRepository.save(any(EtlQueueStatusData.class))).thenAnswer(invocation -> invocation.getArgument(0));

        service.syncCatalogItems(messagePayload, messageId);

        verify(googleSerpApiService).fetchAndPersistSerpData(
                eq(dto.getSearchTerm()),
                eq(List.of("bestbuy")),
                eq(dto.getCategoryKey()),
                eq(dto.getL1CategoryName()),
                eq(dto.getL2CategoryName()),
                any(EtlQueueStatusData.class),
                isNull(),
                isNull()
        );
    }
}
