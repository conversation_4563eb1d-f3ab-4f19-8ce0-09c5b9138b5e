package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.SerpApiEtlConfig;
import com.progleasing.marketplace.etl.exceptions.SerpApiFetchException;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.*;

import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
class GoogleSerpApiServiceTests {

    @InjectMocks
    private GoogleSerpApiService googleSerpApiService;

    @Mock
    private SerpApiEtlConfig serpApiEtlConfig;

    @Mock
    private SerpProductPersistenceService serpProductPersistenceService;

    @Mock
    private RestTemplate restTemplate;

    private EtlQueueStatusData statusData;

    @BeforeEach
    void setup() {
        statusData = new EtlQueueStatusData();

        when(serpApiEtlConfig.getBaseUrl()).thenReturn("http://fake-serpapi.com/search");
        when(serpApiEtlConfig.getEngine()).thenReturn("google");
        when(serpApiEtlConfig.getHl()).thenReturn("en");
        when(serpApiEtlConfig.getGl()).thenReturn("us");
        when(serpApiEtlConfig.getTbm()).thenReturn("shop");
        when(serpApiEtlConfig.getDirectLink()).thenReturn(true);
        when(serpApiEtlConfig.getLocation()).thenReturn("Utah");
        when(serpApiEtlConfig.getGoogleDomain()).thenReturn("google.com");
        when(serpApiEtlConfig.getApiKey()).thenReturn("dummy-key");
        when(serpApiEtlConfig.getMaxResults()).thenReturn(5);
    }

    @Test
    void fetchAndPersistSerpData_shouldSaveProducts_whenValidResponse() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "123");
        product.put("source", "Amazon");
        product.put("extracted_price", 300.00);
        product.put("thumbnail", "image1.jpg");

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("shopping_results", List.of(product));
        responseBody.put("serpapi_pagination", null);

        ResponseEntity<Map<String, Object>> responseEntity =
                new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                eq(HttpEntity.EMPTY),
                ArgumentMatchers.<ParameterizedTypeReference<Map<String, Object>>>any())
        ).thenReturn(responseEntity);

        googleSerpApiService.fetchAndPersistSerpData(
                "laptop",
                List.of("Amazon"),
                "electronics",
                "l1Cat",
                "l2Cat",
                statusData,
                100.0,
                1000.0
        );

        verify(serpProductPersistenceService, times(1))
                .saveProducts(
                        anyList(),
                        eq("laptop"),
                        eq("electronics"),
                        eq("l1Cat"),
                        eq("l2Cat"),
                        eq(statusData));
    }

    @Test
    void fetchAndPersistSerpData_shouldHandlePartialResults_onException() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "456");
        product.put("source", "Best Buy");
        product.put("extracted_price", 450.0);
        product.put("thumbnail", "img.jpg");

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.GET),
                eq(HttpEntity.EMPTY),
                any(ParameterizedTypeReference.class))
        ).thenThrow(new RuntimeException("Simulated API error"));

        SerpApiFetchException exception = assertThrows(SerpApiFetchException.class, () -> {
            googleSerpApiService.fetchAndPersistSerpData(
                    "tv",
                    List.of("Best Buy"),
                    "electronics",
                    "l1Cat",
                    "l2Cat",
                    statusData,
                    200.0,
                    1000.0
            );
        });

        verify(serpProductPersistenceService, times(1))
                .saveProducts(
                        anyList(),
                        eq("tv"),
                        eq("electronics"),
                        eq("l1Cat"),
                        eq("l2Cat"),
                        eq(statusData));

        assert exception.getMessage().contains("Failed to fetch data from SerpAPI");
    }
}
