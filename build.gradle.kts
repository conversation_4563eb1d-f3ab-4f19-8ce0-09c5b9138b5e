plugins {
	id("java")
	id("org.springframework.boot") version "3.4.4"
	id("io.spring.dependency-management") version "1.1.4"
}
val springBootVersion = "3.4.4"
val lombokVersion = "1.18.32"

group = "com.progleasing.marketplace"
version = "1.0.1"
description = "ETL process to fetch products"

repositories {
	mavenCentral()
	maven("https://repo.spring.io/release")
	maven("https://repo.spring.io/milestone")
	maven("https://repo.spring.io/snapshot")
}

dependencies {
	implementation("org.springframework.boot:spring-boot-starter:${springBootVersion}")
	implementation("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
	implementation("org.springframework.boot:spring-boot-starter-json:${springBootVersion}")
	implementation("org.springframework.boot:spring-boot-starter-actuator:${springBootVersion}")
	implementation("org.hibernate.validator:hibernate-validator:8.0.1.Final")
	implementation("jakarta.validation:jakarta.validation-api:3.0.2")
	implementation("com.commercetools.sdk:commercetools-http-client:17.29.0")
	implementation("com.commercetools.sdk:commercetools-sdk-java-api:17.29.0")
	implementation("com.commercetools.sdk:commercetools-sdk-java-importapi:17.29.0")
	implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.4")
	implementation("io.awspring.cloud:spring-cloud-aws-starter-sqs:3.0.4")
	implementation("software.amazon.awssdk:s3:2.31.38")
	implementation(project(":marketplace-shared-model"))
	implementation(project(":marketplace-amazon-pap-service"))
	implementation(project(":marketplace-impact-service"))
	implementation(project(":marketplace-product-exporter-service"))
	implementation(project(":marketplace-product-importer-service"))
	implementation(project(":marketplace-cj-service"))
	implementation(project(":marketplace-google-serp"))
	implementation(project(":marketplace-product-leasibility"))
	implementation(project(":marketplace-polling-monitoring-service"))
	implementation(project(":marketplace-sovrn-service"))
	implementation("org.apache.logging.log4j:log4j-api:2.16.0")
	compileOnly("org.projectlombok:lombok:$lombokVersion")
	annotationProcessor("org.projectlombok:lombok:$lombokVersion")
	testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
}

subprojects {
	apply(plugin = "java")

	group = "com.progleasing.marketplace"
	version = "1.0.1"

	repositories {
		mavenCentral()
	}

	tasks.withType<JavaCompile> {
		options.encoding = "UTF-8"
	}
}

java {
	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") {
	archiveBaseName.set("marketplace-feeds-processor")
	archiveVersion.set("1.0.1")
	archiveClassifier.set("")
	manifest {
		attributes["Start-Class"] = "com.progleasing.marketplace.etl.ETLApplication"
	}
}