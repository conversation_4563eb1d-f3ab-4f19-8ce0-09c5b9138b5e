package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.ImpactRequestConfig;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.response.*;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.progleasing.marketplace.etl.constants.PLConstants.*;

@Service
@Slf4j
@AllArgsConstructor
public class ImpactCatalogSyncService {


    private ImpactRequestConfig impactRequestConfig;
    private ObjectMapper objectMapper;
    private ProductDataRepository productDataRepository;
    private RestTemplate restTemplate;
    private ImpactDataService impactDataService;


    public void syncCatalogItems(String messagePayload, String messageId) throws Exception {
        ETLMessageDTO messageDTO = objectMapper.readValue(messagePayload, ETLMessageDTO.class);
        log.info("Processing Impact for category {} searchTerm {} retailerName {}", messageDTO.getCategoryKey(), messageDTO.getSearchTerm(), messageDTO.getRetailerName());
        impactDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.STARTED);
        try{
            List<String> retailers = Arrays.asList(messageDTO.getRetailerKey().toLowerCase().split("\\s*,\\s*", -1));
            List<Campaign> campaigns = fetchCampaigns();
            List<ProductData> searchTermProductDataList = productDataRepository.findProductsBySearchTerm(messageDTO.getSearchTerm(), ProductData.FeedSource.impact);
            List<String> searchTermProductIds = searchTermProductDataList.stream().map(ProductData::getId).collect(Collectors.toList());
            campaigns.stream()
                    .map(Campaign::getCampaignId)
                    .forEach(id -> log.debug("Campaign ID received for account: " + id));
            List<Campaign> filteredCampaigns = campaigns.stream()
                    .filter(campaign -> retailers.contains(campaign.getCampaignName().toLowerCase()
                            .replaceAll("\\s+", "").replaceAll("[^a-z0-9]", "")))
                    .toList();
            filteredCampaigns.stream()
                    .map(Campaign::getCampaignId)
                    .forEach(id -> log.debug("Campaign ID filtered for account: " + id));
            for(Campaign campaign : filteredCampaigns) {
                log.info("Fetching catalog for  campaign  {} message id {}", campaign.getCampaignName(),messageDTO.getReferenceId());
                HttpEntity<String> entity = new HttpEntity<>(createBasicAuthHeaders());
                String catalogUrl = String.format(CATALOG_URL_TEMPLATE, impactRequestConfig.getHost(),impactRequestConfig.getAccountSid(), campaign.getCampaignId());
                ResponseEntity<CatalogResponse> responseEntity = restTemplate.exchange(
                        catalogUrl, HttpMethod.GET, entity, CatalogResponse.class);
                CatalogResponse catalogResponse = responseEntity.getBody();
                if (catalogResponse != null && catalogResponse.getCatalogs() != null) {
                    List<String> catalogIds = catalogResponse.getCatalogs().stream()
                            .map(Catalog::getId)
                            .toList();
                    for (String catalogId : catalogIds) {
                        syncProducts(catalogId, messageDTO, searchTermProductIds,campaign.getCampaignName());
                    }
                }
            }
            impactDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.COMPLETED);
        }catch (Exception e) {
            log.error("Catalog item sync failed for category ID {}: {}", messageDTO.getCategoryKey(), e.getMessage(), e);
            impactDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING);
            throw e;
        }
    }

    private HttpHeaders createBasicAuthHeaders() {
        String auth = impactRequestConfig.getUserName() + ":" + impactRequestConfig.getPassword();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + encodedAuth);
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        return headers;
    }

    private List<Campaign> fetchCampaigns() {
        List<Campaign> campaignList = new ArrayList<>();
        try {
            String campaignUrl = String.format( CAMPAIGN_URL_TEMPLATE,impactRequestConfig.getHost(), impactRequestConfig.getAccountSid());
            HttpEntity<String> campaignEntity = new HttpEntity<>(createBasicAuthHeaders());

            ResponseEntity<CampaignResponse> campaignResponseEntity = restTemplate.exchange(
                    campaignUrl, HttpMethod.GET, campaignEntity, CampaignResponse.class);

            CampaignResponse response = campaignResponseEntity.getBody();
            campaignList =  Objects.requireNonNull(response).getCampaigns();
        }catch(Exception exc){
            log.error("Exception getting campaigns ", exc);
            throw exc;
        }
        return campaignList;
    }

    public void syncProducts(String catalogId, ETLMessageDTO messageDTO, List<String> searchTermProductIds, String campaignName) {

        try {
            boolean morePages = true;
            List<CatalogItem> allItems = new ArrayList<>();
           do {
                String itemUrl = getCategoryItemUrl(catalogId, messageDTO);
                HttpEntity<String> entity = new HttpEntity<>(createBasicAuthHeaders());
                log.info("Fetching catalog items with url {}",itemUrl);
                ResponseEntity<CatalogItemResponse> response = restTemplate.exchange(
                        itemUrl, HttpMethod.GET, entity, CatalogItemResponse.class
                );
                CatalogItemResponse catalogResponse = response.getBody();
                if (Objects.nonNull(catalogResponse)) {
                    allItems.addAll(catalogResponse.getItems());
                    int currentPage = catalogResponse.getPage();
                    int numPages = catalogResponse.getNumPages();
                    log.info("Items fetched {} for page {} campaign {}",allItems.size(),currentPage,campaignName);
                    morePages = currentPage < numPages;
                }
            } while (morePages && allItems.size() < impactRequestConfig.getMaxProductsToImport());
            impactDataService.populateProductData(allItems, messageDTO, searchTermProductIds,campaignName);
        } catch (Exception e) {
            log.error("Catalog item sync failed for catalog ID {}: {}", catalogId, e.getMessage(), e);
            throw e;
        }
    }

    private String getCategoryItemUrl(String catalogId, ETLMessageDTO messageDTO) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("(Name ~ '").append(messageDTO.getSearchTerm()).append("'");

        if (messageDTO.getMinPrice() != null && messageDTO.getMinPrice().compareTo(BigDecimal.ZERO) > 0) {
            queryBuilder.append(" AND ").append(PRICE_QUERY_PARAM).append(" >= ").append(messageDTO.getMinPrice());
        }

        if (messageDTO.getMaxPrice() != null && messageDTO.getMaxPrice().compareTo(BigDecimal.ZERO) > 0) {
            queryBuilder.append(" AND ").append(PRICE_QUERY_PARAM).append(" <= ").append(messageDTO.getMaxPrice());
        }

        queryBuilder.append(")");

        String itemUrl = String.format(
                CATALOG_ITEM_URL_TEMPLATE,
                impactRequestConfig.getHost(),
                impactRequestConfig.getAccountSid(),
                catalogId,
                queryBuilder.toString(),
                PAGINATION_LIMIT
        );

        return itemUrl;
    }
}
