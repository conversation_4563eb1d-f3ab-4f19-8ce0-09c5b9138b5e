package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.ImpactRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.response.CatalogItem;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class ImpactDataService {

    private ImpactRequestConfig impactRequestConfig;
    private PriceDataRepository priceDataRepository;
    private ImageDataRepository imageDataRepository;
    private AdditionalProductDataRepository additionalProductDataRepository;
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private ProductDataDisabledRepository productDataDisabledRepository;
    private ProductDataRepository productDataRepository;

    @Transactional
    public void populateProductData(List<CatalogItem> items, ETLMessageDTO messageDTO, List<String> searchTermProductIds, String campaignName) {
        try {
            List<ProductData> productDataList = new ArrayList<>();
            List<PriceData> priceDataList = new ArrayList<>();
            List<ImageData> imageDataList = new ArrayList<>();
            List<String> disabledProductIds = new ArrayList<>();
            List<ProductDataDisabled> productDataDisabledList = productDataDisabledRepository.findProductsByFeedSource(messageDTO.getSearchTerm(),
                    messageDTO.getL1CategoryName(), messageDTO.getL2CategoryName(),ProductData.FeedSource.impact);
            if(!productDataDisabledList.isEmpty()){
                disabledProductIds = productDataDisabledList.stream().map(ProductDataDisabled :: getId).collect(Collectors.toList());
            }
            List<AdditionalProductData> additionalProductData = new ArrayList<>();
            for (CatalogItem item : items) {
                String productId = item.getId();
                if(!disabledProductIds.contains(productId)) {
                    productDataList.add(
                            extractProductData(item,
                                    messageDTO, campaignName)
                    );
                    Optional.of(extractAdditionalProductData(item)).ifPresent(additionalProductData::add);
                    Optional.of(extractPriceData(item)).ifPresent(priceDataList::add);
                    Optional.of(extractImageData(item)).ifPresent(imageDataList::add);
                }
            }
            if (!CollectionUtils.isEmpty(productDataList)) {
                productDataRepository.saveAll(productDataList);
            }
            if (!CollectionUtils.isEmpty(priceDataList)) {
                priceDataRepository.saveAll(priceDataList);
            }
            if (!CollectionUtils.isEmpty(imageDataList)) {
                imageDataRepository.saveAll(imageDataList);
            }
            if (!CollectionUtils.isEmpty(additionalProductData)) {
                additionalProductDataRepository.saveAll(additionalProductData);
            }
            searchTermProductIds.removeAll(productDataList.stream().map(ProductData::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(searchTermProductIds) && impactRequestConfig.isDeleteProducts()) {
                productDataRepository.markForDeletion(searchTermProductIds, Timestamp.valueOf(LocalDateTime.now().plusDays(impactRequestConfig.getGracePeriodDays())));
                log.info("Product marked for deletion, productIds :{}", searchTermProductIds);
            }else{
                log.info("No products there marked for deletion");
            }
            log.info("Adding products {} for category {} search term {} retailer {}",productDataList.size(),messageDTO.getCategoryKey(),messageDTO.getSearchTerm(), messageDTO.getRetailerKey());
        } catch (Exception e) {
            log.error("Error saving data to DB for category {} searchTerms {}", messageDTO.getCategoryKey(), messageDTO.getSearchTerm());
            throw new RuntimeException(e);

        }
    }

    private AdditionalProductData extractAdditionalProductData(CatalogItem item) {
        AdditionalProductData additionalProductData = new AdditionalProductData();
        additionalProductData.setId(item.getId());
        additionalProductData.setSize(item.getSize());
        List<String> colors = item.getColors() != null ? item.getColors() : Collections.emptyList();
        String colorString = String.join(",", colors);
        additionalProductData.setColor(colorString);
        return additionalProductData;
    }

    private ImageData extractImageData(CatalogItem item) {
        ImageData imageData = new ImageData();
        imageData.setId(item.getId());
        imageData.setImageUrl(item.getImageUrl());
        imageData.setLastModified(Timestamp.from(Instant.now()));
        return imageData;
    }


    private PriceData extractPriceData(CatalogItem item) {
        PriceData newPriceData = buildPriceDataFromItem(item);
        Optional<PriceData> existingOpt = priceDataRepository.findById(newPriceData.getId());
        if (existingOpt.isPresent()) {
            PriceData existing = existingOpt.get();
            if (hasPriceChanged(existing, newPriceData)) {
                existing.setPrice(newPriceData.getPrice());
                existing.setSalePrice(newPriceData.getSalePrice());
                existing.setLastModified(Timestamp.from(Instant.now()));
                return existing;
            }
        }
        return newPriceData;
    }

    private PriceData buildPriceDataFromItem(CatalogItem item) {
        PriceData priceData = new PriceData();
        priceData.setId(item.getId());
        BigDecimal currentPrice = item.getCurrentPrice() != null
                ? new BigDecimal(item.getCurrentPrice())
                : BigDecimal.ZERO;
        String original = item.getOriginalPrice();
        BigDecimal originalPrice = (original != null && !original.isEmpty())
                ? new BigDecimal(original)
                : currentPrice;
        priceData.setPrice(originalPrice);
        if (originalPrice.compareTo(currentPrice) != 0 && currentPrice.compareTo(BigDecimal.ZERO) > 0) {
            priceData.setSalePrice(currentPrice);
        }
        priceData.setLastModified(Timestamp.from(Instant.now()));
        return priceData;
    }

    private ProductData extractProductData(CatalogItem item, ETLMessageDTO messageDTO, String campaignName) {
        ProductData newProduct = buildProductDataFromItem(item, messageDTO,campaignName);
        Optional<ProductData> existingOpt = productDataRepository.findById(newProduct.getId());
        if (existingOpt.isPresent()) {
            ProductData existing = existingOpt.get();
            if (hasProductChanged(existing, newProduct)) {
                updateProduct(existing, newProduct);
                return existing;
            }
        }
        return newProduct;
    }

    private ProductData buildProductDataFromItem(CatalogItem item, ETLMessageDTO messageDTO, String campaignName) {
        ProductData productData = new ProductData();
        productData.setId(item.getId());
        productData.setName(item.getName());
        productData.setDescription(item.getName());
        productData.setLongDescription(item.getDescription());
        productData.setRetailerName(campaignName);
        productData.setRetailerKey(campaignName.toLowerCase().replaceAll("[^a-z0-9]", ""));
        productData.setBrand(item.getManufacturer());
        productData.setProductTrackingUrl(item.getUrl());
        productData.setAffiliateAddToCartUrl(null);
        productData.setLastModified(Timestamp.from(Instant.now()));
        productData.setCategoryKey(messageDTO.getCategoryKey());
        productData.setSearchTerm(messageDTO.getSearchTerm());
        productData.setRatingAverage(BigDecimal.ZERO);
        productData.setRatingCount(0);
        productData.setL1CategoryName(messageDTO.getL1CategoryName());
        productData.setL2CategoryName(messageDTO.getL2CategoryName());
        productData.setFeedSource(ProductData.FeedSource.impact);
        return productData;
    }


    @Transactional
    public void updateStatusQueue(ETLMessageDTO messageDTO, String messageId, EtlQueueStatusData.Status status) {
        try {
            String messageData = String.format(
                    "%s|%s|%s|%s|%s|%s|%s|%s|%s",
                    messageId,
                    messageDTO.getSearchTerm(),
                    messageDTO.getCategoryKey(),
                    messageDTO.getRetailerName(),
                    messageDTO.getRetailerKey(),
                    messageDTO.getMinPrice(),
                    messageDTO.getMaxPrice(),
                    messageDTO.getL1CategoryName(),
                    messageDTO.getL2CategoryName()
            );
            EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_SEARCH;
            Optional<EtlQueueStatusData> existing =
                    etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(
                            messageDTO.getReferenceId(),
                            ProductData.FeedSource.impact.name(),
                            etlStep,
                            messageId
                    );
            EtlQueueStatusData etlQueueStatusData;
            if (existing.isPresent()) {
                etlQueueStatusData = existing.get();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
                log.info("Impact Catalog sync {} for message ID {} category {}",status.toString(), messageId, messageDTO.getCategoryKey());
            } else {
                etlQueueStatusData = new EtlQueueStatusData();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setEtlStep(etlStep);
                etlQueueStatusData.setEtlID(messageDTO.getReferenceId());
                etlQueueStatusData.setMessageId(messageId);
                etlQueueStatusData.setSqsMessageData(messageData);
                etlQueueStatusData.setRetailerSearch(ProductData.FeedSource.impact.name());
                if (status == EtlQueueStatusData.Status.STARTED) {
                    etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
                }
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
            }
            etlQueueStatusDataRepository.save(etlQueueStatusData);
            etlQueueStatusDataRepository.flush();
            log.info("Queue status saved successfully for message ID {} import id {} to {}", messageId,messageDTO.getReferenceId(),status);
        } catch (Exception e) {
            log.error("Error updating queue status for {} referenceId {}",messageId,messageDTO.getReferenceId());
            throw e;
        }
    }

    private boolean hasProductChanged(ProductData existing, ProductData incoming) {
        return !Objects.equals(trimToNull(existing.getName()), trimToNull(incoming.getName())) ||
                !Objects.equals(trimToNull(existing.getDescription()), trimToNull(incoming.getDescription())) ||
                !Objects.equals(trimToNull(existing.getLongDescription()), trimToNull(incoming.getLongDescription())) ||
                !Objects.equals(trimToNull(existing.getRetailerName()), trimToNull(incoming.getRetailerName())) ||
                !Objects.equals(trimToNull(existing.getRetailerKey()), trimToNull(incoming.getRetailerKey())) ||
                !Objects.equals(trimToNull(existing.getBrand()), trimToNull(incoming.getBrand())) ||
                !Objects.equals(trimToNull(existing.getProductTrackingUrl()), trimToNull(incoming.getProductTrackingUrl())) ||
                !Objects.equals(trimToNull(existing.getAffiliateAddToCartUrl()), trimToNull(incoming.getAffiliateAddToCartUrl()));
    }

    private String trimToNull(String s) {
        return (s == null || s.trim().isEmpty()) ? null : s.trim();
    }


    private void updateProduct(ProductData existing, ProductData updated) {
        existing.setName(updated.getName());
        existing.setDescription(updated.getDescription());
        existing.setLongDescription(updated.getLongDescription());
        existing.setRetailerName(updated.getRetailerName());
        existing.setRetailerKey(updated.getRetailerKey());
        existing.setBrand(updated.getBrand());
        existing.setProductTrackingUrl(updated.getProductTrackingUrl());
        existing.setAffiliateAddToCartUrl(updated.getAffiliateAddToCartUrl());
        existing.setCategoryKey(String.join(",", new HashSet<>(List.of(existing.getCategoryKey().split(",")))) + "," + updated.getCategoryKey());
        existing.setSearchTerm(String.join(",", new HashSet<>(List.of(existing.getSearchTerm().split(",")))) + "," + updated.getSearchTerm());
        existing.setLastModified(Timestamp.from(Instant.now()));
    }


    private boolean hasPriceChanged(PriceData existing, PriceData incoming) {
        return existing.getPrice() == null || incoming.getPrice() == null ||
                existing.getPrice().compareTo(incoming.getPrice()) != 0 ||
                existing.getSalePrice() == null || incoming.getSalePrice() == null ||
                existing.getSalePrice().compareTo(incoming.getSalePrice()) != 0 ;
    }

}
