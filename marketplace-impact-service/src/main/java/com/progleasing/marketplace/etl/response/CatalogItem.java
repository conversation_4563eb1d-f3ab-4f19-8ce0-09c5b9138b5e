package com.progleasing.marketplace.etl.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CatalogItem {
    @JsonProperty("Id")
    private String id;
    @JsonProperty("Name")
    private String name;
    @JsonProperty("Description")
    private String description;
    @JsonProperty("Url")
    private String url;
    @JsonProperty("ImageUrl")
    private String imageUrl;
    @JsonProperty("CurrentPrice")
    private String currentPrice;
    @JsonProperty("OriginalPrice")
    private String originalPrice;
    @JsonProperty("Colors")
    private List<String> colors;
    @JsonProperty("Size")
    private String size;
    @JsonProperty("Manufacturer")
    private String manufacturer;
}
