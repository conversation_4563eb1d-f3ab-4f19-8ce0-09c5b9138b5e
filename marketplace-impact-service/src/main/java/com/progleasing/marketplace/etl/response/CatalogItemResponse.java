package com.progleasing.marketplace.etl.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CatalogItemResponse {
    @JsonProperty("Items")
    private List<CatalogItem> items;

    @JsonProperty("@page")
    private int page;

    @JsonProperty("@numpages")
    private int numPages;

    @JsonProperty("@pagesize")
    private int pageSize;

    @JsonProperty("@total")
    private int total;

    @JsonProperty("@start")
    private int start;

    @JsonProperty("@end")
    private int end;
}
