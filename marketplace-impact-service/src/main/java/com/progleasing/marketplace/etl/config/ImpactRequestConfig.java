package com.progleasing.marketplace.etl.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "impact.etl")
@Setter
@Getter
@Configuration
public class ImpactRequestConfig {
    private String host;
    private String userName;
    private String password;
    private String accountSid;
    private int maxProductsToImport;
    private int gracePeriodDays;
    private boolean deleteProducts;
}
