package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.ImpactRequestConfig;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.response.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@TestConfiguration
@SpringBootTest(classes = {ImpactCatalogSyncService.class,  ImpactRequestConfig.class})
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
})
class ImpactCatalogSyncServiceTest {
    @Mock
    private ImpactRequestConfig impactRequestConfig;

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ImpactCatalogSyncService impactCatalogSyncService;

    @MockitoBean
    private RestTemplate restTemplate;

    @MockitoBean
    private ProductDataRepository productDataRepository;

    @MockitoBean
    private ImpactDataService impactDataService;

    @BeforeEach
    void setUp() {
        Mockito.reset(restTemplate);
        MockitoAnnotations.openMocks(this);
        this.impactCatalogSyncService = new ImpactCatalogSyncService(this.impactRequestConfig,this.objectMapper,this.productDataRepository,
                this.restTemplate,this.impactDataService);
        when(impactRequestConfig.getHost()).thenReturn("example.com");
        when(impactRequestConfig.getPassword()).thenReturn("password");
        when(impactRequestConfig.getUserName()).thenReturn("userName");
        when(impactRequestConfig.getAccountSid()).thenReturn("account");

    }

    @Test
    void testSyncCatalogItems() throws Exception {
        String messageBody = "{" +
                "\"category\":\"electronics\"," +
                "\"searchTerm\":\"smartphone\"," +
                "\"retailerName\":\"eBay\"," +
                "\"retailerKey\":\"ebay\"," +
                "\"minPrice\":100," +
                "\"maxPrice\":1000," +
                "\"referenceId\":\"ref-*********\"" +
                "}";
        String messageId = "test-message-id";
        CampaignResponse response = new CampaignResponse();
        response.setCampaigns(List.of(Campaign.builder().campaignId("1036").campaignName("MSI").build(),
                Campaign.builder().campaignId("1444").campaignName("Target").build(),
                Campaign.builder().campaignId("1234").campaignName("eBay").build()));
        when(restTemplate.exchange(any(String.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(CampaignResponse.class)))
                .thenReturn(new ResponseEntity<>(response, HttpStatus.OK));
        CatalogResponse catalogResponse =  new CatalogResponse();
        catalogResponse.setCatalogs(List.of(Catalog.builder().Id("1").build()));
        when(restTemplate.exchange(any(String.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(CatalogResponse.class)))
                .thenReturn(new ResponseEntity<>(catalogResponse, HttpStatus.OK));
        CatalogItemResponse catalogItemResponse = new CatalogItemResponse();
        catalogItemResponse.setItems(List.of(
                CatalogItem.builder()
                        .id("product_9102_HY58FG")
                        .name("Hoya 58mm Fog A Filter")
                        .description("While capturing the ethereal beauty of a misty landscape...")
                        .manufacturer("Hoya")
                        .url("https://adorama.rfvk.net/c/3656051/1175784/1036?prodsku=HY58FG&u=https%3A%2F%2Fwww.adorama.com%2Fhy58fg.html&intsrc=APIG_9102")
                        .imageUrl("https://www.adorama.com/images/xlarge/HY58FG.JPG")
                        .currentPrice("17.90")
                        .originalPrice("17.90")
                        .build(),
                CatalogItem.builder()
                        .id("product_9102_TFW49NATND3")
                        .name("Tiffen 49mm NATural Full Spectrum Neutral Density 0.3 filter")
                        .description("What sets the Tiffen 49mm NATural Full Spectrum...")
                        .manufacturer("Tiffen")
                        .url("https://adorama.rfvk.net/c/3656051/1175784/1036?prodsku=TFW49NATND3&u=https%3A%2F%2Fwww.adorama.com%2Ftfw49natnd3.html&intsrc=APIG_9102")
                        .imageUrl("https://www.adorama.com/images/xlarge/TFW49NATND3.JPG")
                        .currentPrice("114.99")
                        .originalPrice("110.99")
                        .build()
        ));
        catalogItemResponse.setTotal(2);
        catalogItemResponse.setPage(1);
        catalogItemResponse.setNumPages(1);
        when(restTemplate.exchange(any(String.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(CatalogItemResponse.class)))
                .thenReturn(new ResponseEntity<>(catalogItemResponse, HttpStatus.OK));
        impactCatalogSyncService.syncCatalogItems(messageBody, messageId);
        verify(impactDataService, times(1)).populateProductData(any(),any(),any(),anyString());
    }

    @Test
    void testSyncCatalogItems_NoCampaign() throws Exception {
        String messageBody = "{" +
                "\"category\":\"electronics\"," +
                "\"searchTerm\":\"smartphone\"," +
                "\"retailerName\":\"eBay\"," +
                "\"retailerKey\":\"ebay\"," +
                "\"minPrice\":100," +
                "\"maxPrice\":1000," +
                "\"referenceId\":\"ref-*********\"" +
                "}";
        String messageId = "test-message-id";
        CampaignResponse response = new CampaignResponse();
        response.setCampaigns(List.of(Campaign.builder().campaignId("1036").campaignName("MSI").build(),
                Campaign.builder().campaignId("1444").campaignName("Target").build()));
        when(restTemplate.exchange(any(String.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(CampaignResponse.class)))
                .thenReturn(new ResponseEntity<>(response, HttpStatus.OK));
        impactCatalogSyncService.syncCatalogItems(messageBody, messageId);
        verify(productDataRepository, times(0)).saveAll(any());
        verify(impactDataService, times(2)).updateStatusQueue(any(),anyString(),any());
    }

    @Test
    void testSyncCatalogItems_CatalogResponseError() throws Exception {
        String messagePayload = "{\"category\":\"Electronics\",\"searchTerm\":\"laptops\",\"retailerName\":\"Target\",\"retailerKey\":\"target\"}";
        String messageId = "test-message-id";

        when(restTemplate.exchange(any(String.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(CatalogResponse.class)))
                .thenReturn(new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR));

        assertThrows(Exception.class, () -> impactCatalogSyncService.syncCatalogItems(messagePayload, messageId));
        verify(productDataRepository, times(0)).saveAll(any());
        verify(impactDataService, times(2)).updateStatusQueue(any(),anyString(),any());
    }

}
