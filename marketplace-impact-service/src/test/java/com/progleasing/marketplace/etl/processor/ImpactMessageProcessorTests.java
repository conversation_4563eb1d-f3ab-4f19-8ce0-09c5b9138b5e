package com.progleasing.marketplace.etl.processor;

import com.progleasing.marketplace.etl.service.ImpactCatalogSyncService;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static org.mockito.Mockito.*;

@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
public class ImpactMessageProcessorTests {

    @Mock
    private ImpactCatalogSyncService impactCatalogSyncService;

    @InjectMocks
    private ImpactMessageProcessor messageProcessor;

    @Mock
    private AcknowledgementCallback acknowledgement;


    @BeforeEach
    void setUp() {
        Executor directExecutor = Runnable::run;
        messageProcessor = new ImpactMessageProcessor(impactCatalogSyncService, directExecutor);
    }


    @Test
    void testHandleMessagesSuccessfully() throws Exception {
        Message<String> message = mock(Message.class);
        UUID messageId = UUID.randomUUID();
        String payload = "Sample Payload";
        Map<String, Object> headersMap = new HashMap<>();
        headersMap.put("id", messageId);
        headersMap.put("AcknowledgementCallback", acknowledgement);
        MessageHeaders messageHeaders = new MessageHeaders(headersMap);

        when(message.getPayload()).thenReturn(payload);
        when(message.getHeaders()).thenReturn(messageHeaders);
        when(acknowledgement.onAcknowledge(message))
                .thenReturn(CompletableFuture.completedFuture(null));

        messageProcessor.handle(Collections.singletonList(message));

        ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> idCaptor = ArgumentCaptor.forClass(String.class);
        verify(impactCatalogSyncService, times(1)).syncCatalogItems(payloadCaptor.capture(), idCaptor.capture());
        Assertions.assertEquals(payload, payloadCaptor.getValue());
    }

    @Test
    void testHandleMessageWithException() throws Exception {
        Message<String> message = mock(Message.class);
        UUID messageId = UUID.randomUUID();
        String payload = "Sample Payload";
        Map<String, Object> headersMap = new HashMap<>();
        headersMap.put("id", messageId);
        headersMap.put("AcknowledgementCallback", acknowledgement);
        MessageHeaders messageHeaders = new MessageHeaders(headersMap);
        when(message.getPayload()).thenReturn(payload);
        when(message.getHeaders()).thenReturn(messageHeaders);
        doThrow(new RuntimeException("Test Exception")).when(impactCatalogSyncService).syncCatalogItems(any(), any());
        messageProcessor.handle(Collections.singletonList(message));
        ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> idCaptor = ArgumentCaptor.forClass(String.class);
        verify(impactCatalogSyncService, times(1)).syncCatalogItems(payloadCaptor.capture(), idCaptor.capture());
        verify(acknowledgement, never()).onAcknowledge(message);
    }

}
