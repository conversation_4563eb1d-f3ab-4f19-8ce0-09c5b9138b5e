package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.ImpactRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.ProductDataDisabled;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.response.CatalogItem;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ImpactDataServiceTest {
    @Mock
    private ProductDataRepository productDataRepository;
    @Mock private PriceDataRepository priceDataRepository;
    @Mock private ImageDataRepository imageDataRepository;
    @Mock private AdditionalProductDataRepository additionalProductDataRepository;
    @Mock private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    @Mock private ProductDataDisabledRepository productDataDisabledRepository;
    @Mock private ImpactRequestConfig impactRequestConfig;
    @InjectMocks
    private ImpactDataService impactDataService;


    @Test
    void populateProductData_savesAllEntitiesCorrectly() {
        CatalogItem item = new CatalogItem();
        item.setId("prod123");
        item.setName("Test Product");
        item.setDescription("Full Desc");
        item.setUrl("http://url");
        item.setManufacturer("BrandX");
        item.setImageUrl("http://image.url");
        item.setCurrentPrice("20.00");
        item.setOriginalPrice("25.00");
        item.setColors(List.of("Red", "Blue"));
        item.setSize("XL");

        List<CatalogItem> items = List.of(item);
        ETLMessageDTO dto = new ETLMessageDTO();
        dto.setSearchTerm("phones");
        dto.setCategoryKey("cat1");
        dto.setL1CategoryName("Electronics");
        dto.setL2CategoryName("Mobiles");

        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        when(productDataRepository.findById("prod123")).thenReturn(Optional.empty());
        when(priceDataRepository.findById("prod123")).thenReturn(Optional.empty());
        when(impactRequestConfig.isDeleteProducts()).thenReturn(true);
        when(impactRequestConfig.getGracePeriodDays()).thenReturn(5);

        List<String> searchTermProductIds = new ArrayList<>(List.of("prod123", "prod999"));

        impactDataService.populateProductData(items, dto, searchTermProductIds, "ImpactRetailer");

        verify(productDataRepository).saveAll(anyList());
        verify(priceDataRepository).saveAll(anyList());
        verify(imageDataRepository).saveAll(anyList());
        verify(additionalProductDataRepository).saveAll(anyList());
        verify(productDataRepository).markForDeletion(eq(List.of("prod999")), any());
    }

    @Test
    void populateProductData_skipsDisabledProducts() {
        CatalogItem item = new CatalogItem();
        item.setId("prod123");
        ProductDataDisabled disabled = new ProductDataDisabled();
        disabled.setId("prod123");
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any()))
                .thenReturn(List.of(disabled));
        impactDataService.populateProductData(List.of(item), new ETLMessageDTO(), new ArrayList<>(), "Campaign");
        verify(productDataRepository, never()).saveAll(any());
    }

    @Test
    void updateStatusQueue_createsNewEntryIfNotFound() {
        ETLMessageDTO dto = new ETLMessageDTO();
        dto.setReferenceId("ref123");
        dto.setSearchTerm("shoes");
        dto.setCategoryKey("catX");
        dto.setRetailerKey("impact");
        dto.setRetailerName("Impact");
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(any(), any(), any(), any()))
                .thenReturn(Optional.empty());
        impactDataService.updateStatusQueue(dto, "msg123", EtlQueueStatusData.Status.STARTED);
        verify(etlQueueStatusDataRepository).save(any(EtlQueueStatusData.class));
        verify(etlQueueStatusDataRepository).flush();
    }



}
