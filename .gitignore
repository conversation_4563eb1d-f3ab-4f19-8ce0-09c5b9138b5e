# Maven build output
target/

# IntelliJ IDEA project files
.idea/
*.iml
*.iws

# macOS system files
.DS_Store

.gradle
/.gradle/
HELP.md
/target/
/build/
!.mvn/wrapper/maven-wrapper.jar
!gradle/wrapper/gradle-wrapper.jar

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### VS Code ###
.vscode/

# Exclude build directories in any module
**/build/
# Gradle build folders and files
.gradle/
build/

# Ignore compiled .class files
*.class
