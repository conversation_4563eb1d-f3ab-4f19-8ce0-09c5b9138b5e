package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.*;
import com.commercetools.api.models.category.CategoryPagedQueryResponse;
import com.commercetools.api.models.product.ProductProjectionPagedSearchResponse;
import com.commercetools.api.models.product_type.ProductType;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.progleasing.marketplace.etl.config.PurgeQueueProvider;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.message.ProductImportTriggerEvent;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import software.amazon.awssdk.services.sqs.SqsClient;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductImporterServiceTest {
    @Mock
    private SqsTemplate sqsTemplate;

    @Mock
    @Qualifier("ctApiClient")
    private ProjectApiRoot projectApiRoot;

    @Autowired
    private ObjectMapper objectMapper;

    @Mock
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @InjectMocks
    private ProductImporterService productImporterService;

    @Mock
    private ByProjectKeyCategoriesRequestBuilder byProjectKeyCategoriesRequestBuilder;

    @Mock
    private ByProjectKeyCategoriesGet byProjectKeyCategoriesGet;

    @Mock
    private ByProjectKeyProductTypesRequestBuilder byProjectKeyProductTypesRequestBuilder;

    @Mock
    private ByProjectKeyProductTypesKeyByKeyRequestBuilder byProjectKeyProductTypesKeyByKeyRequestBuilder;

    @Mock
    private ByProjectKeyProductTypesKeyByKeyGet byProjectKeyProductTypesKeyByKeyGet;

    @Mock
    private ByProjectKeyProductProjectionsRequestBuilder byProjectKeyProductProjectionsRequestBuilder;

    @Mock
    private ByProjectKeyProductProjectionsSearchRequestBuilder byProjectKeyProductProjectionsSearchRequestBuilder;

    @Mock
    private ByProjectKeyProductProjectionsSearchGet byProjectKeyProductProjectionsSearchGet;

    @Mock
    PurgeQueueProvider queueProvider;

    @Mock
    SqsClient sqsClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        productImporterService = new ProductImporterService(
                sqsTemplate,
                projectApiRoot,
                objectMapper,
                etlQueueStatusDataRepository,
                "amazon-queue",
                "impact-queue",
                "cj-queue",
                "google-queue",
                "polling-monitoring-queue",
                "sovrn-queue",
                queueProvider,
                sqsClient
        );
    }

    @Test
    void testInitiateETLProcess_validFlow() throws Exception {
        String payload = "{\"source\":\"test-source\",\"trigger\":\"scheduler\"}";
        String messageId = UUID.randomUUID().toString();

        ProductImportTriggerEvent triggerEvent = new ProductImportTriggerEvent();
        triggerEvent.setSource("test-source");
        triggerEvent.setTrigger("scheduler");
        CategoryPagedQueryResponse categoryResponse = objectMapper.readValue(
                this.getClass().getClassLoader().getResourceAsStream("response/etl-category-search.json")
                , CategoryPagedQueryResponse.class);
        ProductType productType = mock(ProductType.class);
        ProductProjectionPagedSearchResponse searchResponse = objectMapper.readValue(
                this.getClass().getClassLoader().getResourceAsStream("response/retailer-projection-search.json")
                , ProductProjectionPagedSearchResponse.class);
        when(projectApiRoot.categories()).thenReturn(byProjectKeyCategoriesRequestBuilder);
        when(byProjectKeyCategoriesRequestBuilder.get()).thenReturn(byProjectKeyCategoriesGet);
        when(byProjectKeyCategoriesGet.withWhere(anyString())).thenReturn(byProjectKeyCategoriesGet);
        when(byProjectKeyCategoriesGet.withLimit(anyInt())).thenReturn(byProjectKeyCategoriesGet);
        when(byProjectKeyCategoriesGet.executeBlocking()).thenReturn(new ApiHttpResponse<>(200, null, categoryResponse));
        when(projectApiRoot.productTypes()).thenReturn(byProjectKeyProductTypesRequestBuilder);
        when(byProjectKeyProductTypesRequestBuilder.withKey(anyString())).thenReturn(byProjectKeyProductTypesKeyByKeyRequestBuilder);
        when(byProjectKeyProductTypesKeyByKeyRequestBuilder.get()).thenReturn(byProjectKeyProductTypesKeyByKeyGet);
        when(byProjectKeyProductTypesKeyByKeyGet.executeBlocking()).thenReturn(new ApiHttpResponse<>(200, null, productType));
        when(productType.getId()).thenReturn("productType1");
        when(projectApiRoot.productProjections()).thenReturn(byProjectKeyProductProjectionsRequestBuilder);
        when(byProjectKeyProductProjectionsRequestBuilder.search()).thenReturn(byProjectKeyProductProjectionsSearchRequestBuilder);
        when(byProjectKeyProductProjectionsSearchRequestBuilder.get()).thenReturn(byProjectKeyProductProjectionsSearchGet);
        when(byProjectKeyProductProjectionsSearchGet.addExpand(anyString())).thenReturn(byProjectKeyProductProjectionsSearchGet);
        when(byProjectKeyProductProjectionsSearchGet.addFilter(anyString())).thenReturn(byProjectKeyProductProjectionsSearchGet);
        when(byProjectKeyProductProjectionsSearchGet.withOffset(anyInt())).thenReturn(byProjectKeyProductProjectionsSearchGet);
        when(byProjectKeyProductProjectionsSearchGet.withLimit(anyInt())).thenReturn(byProjectKeyProductProjectionsSearchGet);
        when(byProjectKeyProductProjectionsSearchGet.executeBlocking()).thenReturn(new ApiHttpResponse<>(200, null, searchResponse));
        productImporterService.initiateETLProcess(payload, messageId);
        verify(sqsTemplate, atLeastOnce())
                .send(anyString(), any());
        verify(etlQueueStatusDataRepository, atLeastOnce()).save(any(EtlQueueStatusData.class));
    }

    @Test
    void testInitiateETLProcess_validFlow_NoCategories() throws Exception {
        String payload = "{\"source\":\"test-source\",\"trigger\":\"scheduler\"}";
        String messageId = UUID.randomUUID().toString();

        ProductImportTriggerEvent triggerEvent = new ProductImportTriggerEvent();
        triggerEvent.setSource("test-source");
        triggerEvent.setTrigger("scheduler");
        ProductType productType = mock(ProductType.class);
        when(projectApiRoot.categories()).thenReturn(byProjectKeyCategoriesRequestBuilder);
        when(byProjectKeyCategoriesRequestBuilder.get()).thenReturn(byProjectKeyCategoriesGet);
        when(byProjectKeyCategoriesGet.withWhere(anyString())).thenReturn(byProjectKeyCategoriesGet);
        when(byProjectKeyCategoriesGet.withLimit(anyInt())).thenReturn(byProjectKeyCategoriesGet);
        when(byProjectKeyCategoriesGet.executeBlocking()).thenReturn(new ApiHttpResponse<>(200, null, null));
        when(projectApiRoot.productTypes()).thenReturn(byProjectKeyProductTypesRequestBuilder);
        when(byProjectKeyProductTypesRequestBuilder.withKey(anyString())).thenReturn(byProjectKeyProductTypesKeyByKeyRequestBuilder);
        when(byProjectKeyProductTypesKeyByKeyRequestBuilder.get()).thenReturn(byProjectKeyProductTypesKeyByKeyGet);
        when(byProjectKeyProductTypesKeyByKeyGet.executeBlocking()).thenReturn(new ApiHttpResponse<>(200, null, productType));
        productImporterService.initiateETLProcess(payload, messageId);
        verify(sqsTemplate, times(1)).send(eq("polling-monitoring-queue"), any());
        verify(etlQueueStatusDataRepository, atLeastOnce()).save(any(EtlQueueStatusData.class));
    }
}
