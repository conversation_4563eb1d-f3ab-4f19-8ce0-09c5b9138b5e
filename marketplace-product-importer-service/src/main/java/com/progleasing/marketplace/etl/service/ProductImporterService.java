package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.category.Category;
import com.commercetools.api.models.category.CategoryPagedQueryResponse;
import com.commercetools.api.models.category.CategoryReference;
import com.commercetools.api.models.common.Money;
import com.commercetools.api.models.product.Attribute;
import com.commercetools.api.models.product.ProductProjection;
import com.commercetools.api.models.product.ProductProjectionPagedSearchResponse;
import com.commercetools.api.models.product_type.AttributePlainEnumValueImpl;
import com.commercetools.api.models.product_type.ProductType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.PurgeQueueProvider;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.message.ProductImportTriggerEvent;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest;
import software.amazon.awssdk.services.sqs.model.PurgeQueueInProgressException;
import software.amazon.awssdk.services.sqs.model.PurgeQueueRequest;
import software.amazon.awssdk.services.sqs.model.SqsException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.progleasing.marketplace.etl.constants.PLConstants.PRODUCT_LIMIT_SEARCH;

@Service
@Slf4j
public class ProductImporterService {

    private final SqsTemplate sqsTemplate;
    private ProjectApiRoot projectApiRoot;
    private ObjectMapper objectMapper;
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private String amazonRetailerQueue;
    private String impactRetailerQueue;
    private String cjRetailerQueue;
    private String googleRetailerQueue;
    private String pollingMonitoringQueue;
    private String sovrnRetailerQueue;
    private final PurgeQueueProvider queueProvider;
    private final SqsClient sqsClient;

    private final Map<String, String> retailerQueueMap = new HashMap<>();
    private static final BigDecimal CENT_MULTIPLIER = BigDecimal.valueOf(100L);

    public ProductImporterService(
            SqsTemplate sqsTemplate, @Qualifier("ctApiClient") ProjectApiRoot projectApiRoot,
            ObjectMapper objectMapper,
            EtlQueueStatusDataRepository etlQueueStatusDataRepository,
            @Value("${aws.sqs.amazon.queue-name}")
            String amazonRetailerQueue,
            @Value("${aws.sqs.impact.queue-name}")
            String impactRetailerQueue,
            @Value("${aws.sqs.cj.queue-name}")
            String cjRetailerQueue,
            @Value("${aws.sqs.serp.queue-name}")
            String googleRetailerQueue,
            @Value("${aws.sqs.polling-monitoring.queue-name}")
            String pollingMonitoringQueue,
            @Value("${aws.sqs.sovrn.queue-name}")
            String sovrnQueue
            , PurgeQueueProvider queueProvider, SqsClient sqsClient) {
        this.sqsTemplate = sqsTemplate;
        this.projectApiRoot = projectApiRoot;
        this.objectMapper = objectMapper;
        this.etlQueueStatusDataRepository = etlQueueStatusDataRepository;
        this.amazonRetailerQueue = amazonRetailerQueue;
        this.impactRetailerQueue = impactRetailerQueue;
        this.cjRetailerQueue = cjRetailerQueue;
        this.googleRetailerQueue = googleRetailerQueue;
        this.pollingMonitoringQueue = pollingMonitoringQueue;
        this.sovrnRetailerQueue = sovrnQueue;
        this.queueProvider = queueProvider;
        this.sqsClient = sqsClient;
        retailerQueueMap.put("amazon", amazonRetailerQueue);
        retailerQueueMap.put("impact", impactRetailerQueue);
        retailerQueueMap.put("cj", cjRetailerQueue);
        retailerQueueMap.put("google", googleRetailerQueue);
        retailerQueueMap.put("sovrn", sovrnQueue);
    }

    public void initiateETLProcess(String messagePayload, String messageId) throws Exception {
        ProductImportTriggerEvent event = objectMapper.readValue(messagePayload, ProductImportTriggerEvent.class);
        try {
            log.info("Initiate ETL Process at {} with message id {}", LocalDate.now(), messageId);
            purgeQueues();
            updateStatusQueue(event, messageId, EtlQueueStatusData.Status.STARTED);
            List<String> etlCategoryList = queryCategories();
            ProductType productType = projectApiRoot.productTypes().withKey("retailerproduct")
                    .get().executeBlocking().getBody();
            if (!CollectionUtils.isEmpty(etlCategoryList) && Objects.nonNull(productType)) {
                processCategoriesAsync(etlCategoryList, productType, messageId);
            }
            publishToPolling(messageId);
            updateStatusQueue(event, messageId, EtlQueueStatusData.Status.COMPLETED);
        }catch (Exception e) {
            log.error("Product import failed for message ID {}: {}", messageId, e.getMessage(), e);
            updateStatusQueue(event, messageId,  EtlQueueStatusData.Status.ERROR_RESTARTING);
            throw e;
        }
    }

    public void purgeQueues() {
        if(!CollectionUtils.isEmpty(queueProvider.getAllQueueNames())) {
            log.info("Purging queues before Import process");
            for (String queue : queueProvider.getAllQueueNames()) {
                try {
                    String queueUrl = sqsClient.getQueueUrl(
                            GetQueueUrlRequest.builder().queueName(queue).build()
                    ).queueUrl();
                    sqsClient.purgeQueue(
                            PurgeQueueRequest.builder().queueUrl(queueUrl).build()
                    );
                    log.info("Successfully purged: {}", queue);
                } catch (PurgeQueueInProgressException e) {
                    log.error("Queue is already being purged: {}", queue,e);
                } catch (SqsException e) {
                    log.error("Error purging queue {}", queue,e);
                }
            }
        }
    }

    private void processCategoriesAsync(List<String> etlCategories, ProductType productType, String messageId) throws Exception {
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        for (String category : etlCategories) {
                executorService.submit(() -> {
                    boolean hasMore = true;
                    int offset = 0;
                    while (hasMore) {
                        try {
                            ProductProjectionPagedSearchResponse response = searchProducts(offset, productType.getId(), category);
                            if (!CollectionUtils.isEmpty(response.getResults())) {
                                publishMessage(response.getResults(), messageId, category);
                            }
                            offset += PRODUCT_LIMIT_SEARCH;
                            hasMore = offset < response.getTotal();
                        } catch (Exception e) {
                            log.error("Error processing category {}: {}", category, e.getMessage(), e);
                            throw new RuntimeException("Error processing category: " + category, e);
                        }
                    }
                });
        }
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.MINUTES)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
            throw e;
        }
    }


    private void publishMessage(List<ProductProjection> products, String messageId, String etlCategory) throws Exception {
        try{
        Map<String, List<ProductProjection>> feedTypeToProductsMap = new HashMap<>();
        Category category = null;
        for (ProductProjection product : products) {
            try {
                if (Objects.isNull(category)) {
                    List<CategoryReference> categories = product.getCategories();
                    for (CategoryReference categoryRef : categories) {
                        Category productCategory = categoryRef.getObj();
                        if (etlCategory.equals(productCategory.getId()) && Objects.nonNull(productCategory.getCustom()) &&
                                Objects.nonNull(productCategory.getCustom().getFields())) {
                            category = productCategory;
                            break;
                        }
                    }
                }
                Attribute attribute = product.getMasterVariant().getAttribute("retailerFeedType");
                if (attribute != null) {
                    AttributePlainEnumValueImpl attributeValue = objectMapper.convertValue(attribute.getValue(), AttributePlainEnumValueImpl.class);
                    String retailerFeedType = attributeValue.getKey();
                    if (retailerQueueMap.containsKey(retailerFeedType)) {
                        feedTypeToProductsMap.computeIfAbsent(retailerFeedType, k -> new ArrayList<>()).add(product);
                    }
                }
            } catch (Exception e) {
                log.warn("Error reading retailerFeedType from product {}", product.getKey());
            }
        }
        if (Objects.nonNull(category)) {

            for (Map.Entry<String, List<ProductProjection>> entry : feedTypeToProductsMap.entrySet()) {
                List<String> retailerNameList = new ArrayList<>();
                List<String> retailerKeyList = new ArrayList<>();
                String retailerKey = entry.getKey();
                List<ProductProjection> groupedProducts = entry.getValue();
                if (!retailerQueueMap.containsKey(retailerKey)) continue;
                for (ProductProjection product : groupedProducts) {
                    try {
                        retailerNameList.add(product.getName().get("en-US"));
                        retailerKeyList.add(product.getKey());
                    } catch (Exception ex) {
                        log.error("Exception sending grouped message for product {}", product.getKey(), ex);
                    }
                }
                Category parentCategory = category.getParent().getObj();
                Map<String, Object> fields = category.getCustom().getFields().values();
                String searchTerm = fields.containsKey("etlSearchTerm") ?
                        fields.get("etlSearchTerm").toString() : category.getName().get("en-US");
                Money minPrice = (Money) fields.get("etlPriceMin");
                Money maxPrice = (Money) fields.get("etlPriceMax");
                ETLMessageDTO messageDTO = new ETLMessageDTO();
                messageDTO.setCategoryKey(category.getKey());
                messageDTO.setRetailerKey(String.join(",", retailerKeyList));
                messageDTO.setRetailerName(String.join(",", retailerNameList));
                messageDTO.setL2CategoryName(category.getName().get("en-US"));
                messageDTO.setL1CategoryName(parentCategory.getName().get("en-US"));
                messageDTO.setReferenceId(messageId);
                if (minPrice != null && minPrice.getCentAmount() > 0) {
                    messageDTO.setMinPrice(new BigDecimal(minPrice.getCentAmount())
                            .divide(CENT_MULTIPLIER).setScale(2, RoundingMode.HALF_UP));
                }
                if (maxPrice != null && maxPrice.getCentAmount() > 0) {
                    messageDTO.setMaxPrice(new BigDecimal(maxPrice.getCentAmount())
                            .divide(CENT_MULTIPLIER).setScale(2, RoundingMode.HALF_UP));
                }
                List<String> searchTerms = Arrays.stream(searchTerm.split(","))
                        .map(String::trim)
                        .filter(term -> !term.isEmpty())
                        .collect(Collectors.toList());
                for(String searchKeyword : searchTerms) {
                    messageDTO.setSearchTerm(searchKeyword);
                    log.info("Publishing message for etId {} to retailer queue {} for category {} retailers {} search term {}", messageId,
                            retailerQueueMap.get(retailerKey), messageDTO.getCategoryKey(), messageDTO.getRetailerKey(), searchKeyword);
                    sqsTemplate.send(
                            retailerQueueMap.get(retailerKey),
                            MessageBuilder.withPayload(objectMapper.writeValueAsString(messageDTO)).build()
                    );
                }
            }
        }
        }catch(Exception  exc){
            log.error("Exception sending message for product {}",etlCategory);
            throw exc;
        }
    }


    private ProductProjectionPagedSearchResponse searchProducts(int offset, String productTypeId, String categoryId) {
        String searchableCategoryId = String.format("\"%s\"", categoryId);
        String categoryFilter = String.format("categories.id:%s", searchableCategoryId);

        return projectApiRoot
                .productProjections()
                .search()
                .get()
                .addExpand("categories[*].parent")
                .addFilter(String.format("productType.id:\"%s\"", productTypeId))
                .addFilter(categoryFilter)
                .withOffset(offset)
                .withLimit(PRODUCT_LIMIT_SEARCH)
                .executeBlocking()
                .getBody();
    }

    private List<String> queryCategories() {
        CategoryPagedQueryResponse response = projectApiRoot.categories()
                .get()
                .withWhere("custom(fields(isNotETLCategory=false))")
                .withLimit(100)
                .executeBlocking()
                .getBody();
        if (response == null || response.getResults() == null) {
            return Collections.emptyList();
        }

        return response.getResults().stream()
                .map(Category::getId)
                .collect(Collectors.toList());
    }

    @Transactional
    private void updateStatusQueue(ProductImportTriggerEvent event, String messageId, EtlQueueStatusData.Status status) {
        EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_IMPORT;
        String messageData = String.format(
                "%s|%s|%s",
                messageId,
                event.getSource(),
                event.getTrigger()
        );
        Optional<EtlQueueStatusData> existing =
                etlQueueStatusDataRepository.findByEtlIDAndSqsMessageDataAndEtlStep(
                        messageId,
                        messageData,
                        etlStep
                );
        EtlQueueStatusData etlQueueStatusData;
        if (existing.isPresent()) {
            etlQueueStatusData = existing.get();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        } else {
            etlQueueStatusData = new EtlQueueStatusData();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setEtlStep(etlStep);
            etlQueueStatusData.setEtlID(messageId);
            etlQueueStatusData.setMessageId(messageId);
            etlQueueStatusData.setSqsMessageData(messageData);
            if (status == EtlQueueStatusData.Status.STARTED) {
                etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
            }
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        }
        etlQueueStatusDataRepository.save(etlQueueStatusData);
    }

    public void publishToPolling(String messageId) {
        String payload = String.format("""
                {  "trigger": "polling-monitoring",
                        "timestamp": "%s",
                        "source": "product-import",
                        "referenceId":"%s"
                }
        """, Instant.now().toString(), messageId);
        log.info("publishing Payload: {} queue: {}", payload,pollingMonitoringQueue);
        sqsTemplate.send(pollingMonitoringQueue, MessageBuilder.withPayload(payload)
                .build());
    }
}