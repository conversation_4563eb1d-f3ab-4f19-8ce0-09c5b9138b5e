package com.progleasing.marketplace.etl.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PurgeQueueProvider {

    @Value("${aws.sqs.sovrn.queue-name:sovrn-retailer-queue}")
    private String sovrn;

    @Value("${aws.sqs.product-exporter.queue-name:product-exporter-queue}")
    private String productExporter;

    @Value("${aws.sqs.amazon.queue-name:amazon-retailer-queue}")
    private String amazon;

    @Value("${aws.sqs.impact.queue-name:impact-retailer-queue}")
    private String impact;

    @Value("${aws.sqs.cj.queue-name:cj-retailer-queue}")
    private String cj;

    @Value("${aws.sqs.serp.queue-name:serp-retailer-queue}")
    private String serp;

    @Value("${aws.sqs.leasibility-exporter.queue-name:leasibility-export-queue}")
    private String leasibilityExport;

    @Value("${aws.sqs.polling-monitoring.queue-name:polling-monitoring-queue}")
    private String pollingMonitoring;

    public List<String> getAllQueueNames() {
        return List.of(
                sovrn, productExporter,
                amazon, impact, cj, serp, leasibilityExport, pollingMonitoring
        );
    }
}
