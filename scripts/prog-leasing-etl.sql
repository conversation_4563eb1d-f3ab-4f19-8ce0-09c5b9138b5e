-- Database: marketplace_ETL_DB
/* -- this must be run before the rest of schema and table SQL
-- DROP DATABASE IF EXISTS "marketplace_ETL_DB";
CREATE DATABASE "marketplace_ETL_DB"
    WITH
    OWNER = ""{REPLACEMEWITHUSER}""
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_United States.1252'
    LC_CTYPE = 'English_United States.1252'
    LOCALE_PROVIDER = 'libc'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;
*/

/* -- if you are running PSQL command line interface then...*/
-- \connect marketplace_ETL_DB;

/* -- if you are using pgAdmin, you will need to login and create the Database if it does exist and select the "Query Tool"*/
-- SCHEMA: marketplace_ETL_Schema
-- DROP SCHEMA IF EXISTS marketplace_ETL_Schema ;
CREATE SCHEMA IF NOT EXISTS marketplace_ETL_Schema AUTHORIZATION ""{REPLACEMEWITHUSER}"";


-- Table: marketplace_ETL_Schema.etl_queue_status
-- DROP TABLE IF EXISTS marketplace_ETL_Schema.etl_queue_status;
CREATE TABLE IF NOT EXISTS marketplace_ETL_Schema.etl_queue_status
(
    id bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1 ),
    etlid character varying(255) COLLATE pg_catalog."default" NOT NULL,
    message_id character varying(255) COLLATE pg_catalog."default" NOT NULL,
    etl_start timestamp(6) without time zone,
    etl_step character varying(255) COLLATE pg_catalog."default" NOT NULL,
    last_update timestamp(6) without time zone,
    sqs_message_data character varying(1000) COLLATE pg_catalog."default" NOT NULL,
    status character varying(255) COLLATE pg_catalog."default" NOT NULL,
    retailer_search character varying(255) COLLATE pg_catalog."default",
    processed character varying(50) COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT etl_queue_status_pkey PRIMARY KEY (id),
    CONSTRAINT etl_queue_status_etl_step_check CHECK (etl_step::text = ANY (ARRAY['PRODUCT_IMPORT'::character varying::text, 'PRODUCT_SEARCH'::character varying::text, 'PRODUCT_LEASIBILITY_EXPORT'::character varying::text, 'PRODUCT_LEASIBILITY_IMPORT'::character varying::text, 'PRODUCT_EXPORT'::character varying::text, 'IMAGE_UPLOAD'::character varying::text, 'RETAILER_IMPORT'::character varying::text])),
    CONSTRAINT etl_queue_status_status_check CHECK (status::text = ANY (ARRAY['NOT_STARTED'::character varying::text, 'STARTED'::character varying::text, 'COMPLETED'::character varying::text, 'ERROR_RESTARTING'::character varying::text])),
    CONSTRAINT etl_queue_status_processed_check CHECK (
        processed IS NULL OR processed IN ('ERROR', 'SUCCESS', 'IN_PROCESS')
    )
)
TABLESPACE pg_default;
ALTER TABLE IF EXISTS marketplace_ETL_Schema.etl_queue_status OWNER to ""{REPLACEMEWITHUSER}"";
-- Index: idx_etl_sqs
-- DROP INDEX IF EXISTS marketplace_ETL_Schema.idx_etl_sqs;
CREATE INDEX IF NOT EXISTS idx_etl_sqs
    ON marketplace_ETL_Schema.etl_queue_status USING btree
    (etlid COLLATE pg_catalog."default" ASC NULLS LAST, sqs_message_data COLLATE pg_catalog."default" ASC NULLS LAST)
    TABLESPACE pg_default;


-- Table: marketplace_ETL_Schema.pl_product_data
-- DROP TABLE IF EXISTS marketplace_ETL_Schema.pl_product_data;
CREATE TABLE IF NOT EXISTS marketplace_etl_schema.pl_product_data
(
    id character varying(255) COLLATE pg_catalog."default" NOT NULL,
    affiliate_add_to_cart_url character varying(500) COLLATE pg_catalog."default",
    brand character varying(255) COLLATE pg_catalog."default",
    category_key character varying(255) COLLATE pg_catalog."default",
    description character varying(500) COLLATE pg_catalog."default",
    last_modified timestamp without time zone,
    last_sent_to_ct timestamp without time zone,
    leasable boolean NOT NULL,
    long_description text COLLATE pg_catalog."default",
    name character varying(500) COLLATE pg_catalog."default",
    product_tracking_url text COLLATE pg_catalog."default",
    rating_average numeric(38,2),
    rating_count integer,
    retailer_key character varying(255) COLLATE pg_catalog."default",
    retailer_name character varying(255) COLLATE pg_catalog."default",
    search_term character varying(255) COLLATE pg_catalog."default",
    feed_source character varying(255) COLLATE pg_catalog."default",
    l1_category_name character varying(255) COLLATE pg_catalog."default",
    l2_category_name character varying(255) COLLATE pg_catalog."default",
    marked_for_deletion timestamp without time zone,
    CONSTRAINT pl_product_data_pkey PRIMARY KEY (id),
    CONSTRAINT pl_product_data_feed_source_check CHECK (feed_source::text = ANY (ARRAY['amazon'::character varying, 'google'::character varying, 'impact'::character varying, 'cj'::character varying, 'sovrn'::character varying]::text[]))
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS marketplace_etl_schema.pl_product_data
    OWNER to "svc-marketplacedb-tst";

-- Table: marketplace_ETL_Schema.pl_extra_product_data
-- DROP TABLE IF EXISTS marketplace_ETL_Schema.pl_extra_product_data;
CREATE TABLE IF NOT EXISTS marketplace_ETL_Schema.pl_extra_product_data
(
    id character varying(255) COLLATE pg_catalog."default" NOT NULL,
    color character varying(255) COLLATE pg_catalog."default",
    features text COLLATE pg_catalog."default",
    item_part_number character varying(255) COLLATE pg_catalog."default",
    last_modified timestamp without time zone,
    last_sent_to_ct timestamp without time zone,
    model character varying(255) COLLATE pg_catalog."default",
    other_info text COLLATE pg_catalog."default",
    size character varying(255) COLLATE pg_catalog."default",
    technical_info text COLLATE pg_catalog."default",
    CONSTRAINT pl_extra_product_data_pkey PRIMARY KEY (id)
)
TABLESPACE pg_default;
ALTER TABLE IF EXISTS marketplace_ETL_Schema.pl_extra_product_data OWNER to ""{REPLACEMEWITHUSER}"";

-- Table: marketplace_ETL_Schema.pl_image_data
-- DROP TABLE IF EXISTS marketplace_ETL_Schema.pl_image_data;
CREATE TABLE IF NOT EXISTS marketplace_ETL_Schema.pl_image_data
(
    id character varying(255) COLLATE pg_catalog."default" NOT NULL,
    image_url character varying(512) COLLATE pg_catalog."default" NOT NULL,
    last_modified timestamp without time zone,
    last_sent_to_ct timestamp without time zone,
    last_uploaded timestamp without time zone,
    CONSTRAINT pl_image_data_pkey PRIMARY KEY (id, image_url)
)
TABLESPACE pg_default;
ALTER TABLE IF EXISTS marketplace_ETL_Schema.pl_image_data OWNER to ""{REPLACEMEWITHUSER}"";

-- Table: marketplace_ETL_Schema.pl_price_data
-- DROP TABLE IF EXISTS marketplace_ETL_Schema.pl_price_data;
CREATE TABLE IF NOT EXISTS marketplace_ETL_Schema.pl_price_data
(
    id character varying(255) COLLATE pg_catalog."default" NOT NULL,
    last_modified timestamp without time zone,
    last_sent_to_ct timestamp without time zone,
    price numeric(38,2),
    sale_price numeric(38,2),
    CONSTRAINT pl_price_data_pkey PRIMARY KEY (id)
)
TABLESPACE pg_default;
ALTER TABLE IF EXISTS marketplace_ETL_Schema.pl_price_data OWNER to ""{REPLACEMEWITHUSER}"";

-- Table: marketplace_ETL_Schema.pl_retailer_partner_data
-- DROP TABLE IF EXISTS marketplace_ETL_Schema.pl_retailer_partner_data;
CREATE TABLE IF NOT EXISTS marketplace_ETL_Schema.pl_retailer_partner_data
(
    id bigint NOT NULL,
    retailer_name text COLLATE pg_catalog."default",
    partner_id text COLLATE pg_catalog."default",
    CONSTRAINT pl_retailer_partner_data_pkey PRIMARY KEY (id)
)
TABLESPACE pg_default;
ALTER TABLE IF EXISTS marketplace_ETL_Schema.pl_retailer_partner_data OWNER to ""{REPLACEMEWITHUSER}"";

-- create pl_retailer_partner_data records
INSERT INTO marketplace_ETL_Schema.pl_retailer_partner_data (id, partner_id, retailer_name) VALUES (1, '2125808', 'Dell');
INSERT INTO marketplace_ETL_Schema.pl_retailer_partner_data (id, partner_id, retailer_name) VALUES (2, '1463221', 'Tire Rack');

CREATE TABLE IF NOT EXISTS marketplace_ETL_Schema.sovrn_products
(
    sku                   character varying(100) COLLATE pg_catalog."default" NOT NULL,
    retailer_key          character varying(100) COLLATE pg_catalog."default" NOT NULL,
    ean                   character varying(50)  COLLATE pg_catalog."default",
    isbn                  character varying(50)  COLLATE pg_catalog."default",
    mpn                   character varying(100) COLLATE pg_catalog."default",
    upc                   character varying(50)  COLLATE pg_catalog."default",
    product_name          text COLLATE pg_catalog."default",
    brand                 character varying(100) COLLATE pg_catalog."default",
    language              character varying(20)  COLLATE pg_catalog."default",
    in_stock              character varying(10)  COLLATE pg_catalog."default",
    merchant_name         character varying(100) COLLATE pg_catalog."default",
    merchant_raw_category text COLLATE pg_catalog."default",
    retail_price          numeric(10, 2),
    sale_price            numeric(10, 2),
    image_url             text COLLATE pg_catalog."default",
    thumbnail_url         text COLLATE pg_catalog."default",
    affiliated_url        text COLLATE pg_catalog."default",
    domain                character varying(255) COLLATE pg_catalog."default",
    creation_date         timestamp without time zone DEFAULT now(),
    CONSTRAINT sovrn_products_pkey PRIMARY KEY (sku, retailer_key)
)
TABLESPACE pg_default;
CREATE INDEX idx_product_name_lower ON marketplace_etl_schema.sovrn_products (LOWER(product_name));
CREATE INDEX idx_category_lower ON marketplace_etl_schema.sovrn_products (LOWER(merchant_raw_category));
CREATE INDEX idx_retailer_key ON marketplace_etl_schema.sovrn_products (retailer_key);


ALTER TABLE IF EXISTS marketplace_ETL_Schema.sovrn_products OWNER TO ""{REPLACEMEWITHUSER}"";


DROP TABLE IF EXISTS marketplace_ETL_Schema.pl_retailer_static_data;

CREATE TABLE marketplace_ETL_Schema.pl_retailer_static_data (
    retailer_id  BIGINT PRIMARY KEY,
    feed_type    TEXT,
    partner_type TEXT
)
TABLESPACE pg_default;
ALTER TABLE IF EXISTS marketplace_ETL_Schema.pl_retailer_static_data OWNER to ""{REPLACEMEWITHUSER}"";

INSERT INTO marketplace_ETL_Schema.pl_retailer_static_data (retailer_id, feed_type, partner_type) VALUES
(162958 , 'Amazon'     , NULL),
( 75817 , 'GoogleSERP' , NULL),
( 92123 , 'SOVRN'      , NULL),
(164062 , 'Ebay'       , NULL),
(241019 , 'GoogleSERP' , NULL),
( 97520 , 'GoogleSERP' , NULL),
(  6765 , 'GoogleSERP' , NULL),
(150058 , 'SOVRN'      , NULL),
( 63191 , 'GoogleSERP' , NULL),
(114413 , 'GoogleSERP' , NULL),
(161107 , 'CJ'         , NULL),
( 60365 , 'GoogleSERP' , NULL),
(108650 , 'GoogleSERP' , NULL),
(124365 , 'GoogleSERP' , NULL),
( 97248 , 'GoogleSERP' , NULL),
(241660 , 'SOVRN'      , NULL);

CREATE TABLE IF NOT EXISTS marketplace_ETL_Schema.pl_product_data_disabled
(
    id character varying(255) COLLATE pg_catalog."default" NOT NULL,
    affiliate_add_to_cart_url text COLLATE pg_catalog."default",
    brand character varying(255) COLLATE pg_catalog."default",
    category_key character varying(255) COLLATE pg_catalog."default",
    description character varying(500) COLLATE pg_catalog."default",
    feed_source character varying(255) COLLATE pg_catalog."default",
    l1_category_name character varying(255) COLLATE pg_catalog."default",
    l2_category_name character varying(255) COLLATE pg_catalog."default",
    last_modified timestamp(6) without time zone,
    last_sent_to_ct timestamp(6) without time zone,
    leasable boolean NOT NULL,
    long_description text COLLATE pg_catalog."default",
    name character varying(500) COLLATE pg_catalog."default",
    product_tracking_url text COLLATE pg_catalog."default",
    rating_average numeric(38,2),
    rating_count integer,
    retailer_key character varying(255) COLLATE pg_catalog."default",
    retailer_name character varying(255) COLLATE pg_catalog."default",
    search_term character varying(255) COLLATE pg_catalog."default",
    marked_for_deletion timestamp without time zone,
    CONSTRAINT pl_product_data_disabled_pkey PRIMARY KEY (id),
    CONSTRAINT pl_product_data_disabled_feed_source_check CHECK (feed_source::text = ANY (ARRAY['amazon'::character varying, 'google'::character varying, 'impact'::character varying, 'cj'::character varying, 'sovrn'::character varying]::text[]))
)
TABLESPACE pg_default;
ALTER TABLE IF EXISTS marketplace_ETL_Schema.pl_product_data_disabled OWNER to ""{REPLACEMEWITHUSER}"";

