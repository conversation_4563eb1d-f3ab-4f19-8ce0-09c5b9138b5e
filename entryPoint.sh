#!/bin/sh

set -e

CERT_DIR="/etc/ssl/certs"
TRUSTSTORE_PATH="/app/truststore.jks"
TRUSTSTORE_PASS="changeit"
CERTS="stormwind.local.pem proginternal.local.pem"
APP_JAR="/app/build/libs/marketplace-feeds-processor-1.0.1.jar"

echo "[INFO] Using existing JVM truststore at $TRUSTSTORE_PATH"

# Import internal certs on top of default truststore
for cert_file in $CERTS; do
  cert_path="$CERT_DIR/$cert_file"
  if [ -f "$cert_path" ]; then
    alias_name=`basename "$cert_file" | sed 's/[^a-zA-Z0-9]/_/g'`
    keytool -import -trustcacerts -noprompt \
            -alias "$alias_name" \
            -file "$cert_path" \
            -keystore "$TRUSTSTORE_PATH" \
            -storepass "$TRUSTSTORE_PASS"
  else
    echo "Warning: Certificate $cert_path not found!"
  fi
done


echo "[INFO] Launching Java app with custom truststore..."

 exec summon --provider summon-conjur -f /app/secrets.yml \
  java -Djavax.net.ssl.trustStore="$TRUSTSTORE_PATH" \
       -Djavax.net.ssl.trustStorePassword="$TRUSTSTORE_PASS" \
       -jar "$APP_JAR"

#exec java -Djavax.net.ssl.trustStore="$TRUSTSTORE_PATH" \
#       -Djavax.net.ssl.trustStorePassword="$TRUSTSTORE_PASS" \
#       -jar "$APP_JAR"