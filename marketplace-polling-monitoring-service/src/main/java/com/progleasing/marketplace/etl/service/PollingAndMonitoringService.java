package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.message.PollingAndMonitoringTriggerEvent;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

@Service
@Slf4j
public class PollingAndMonitoringService {

    private final SqsTemplate sqsTemplate;
    private String pollingMonitoringQueue;
    private ObjectMapper objectMapper;
    private MonitoringManager monitoringManager;
    private static final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$");

    public PollingAndMonitoringService(SqsTemplate sqsTemplate,
                                       @Value("${aws.sqs.polling-monitoring.queue-name}")
                                       String pollingMonitoringQueue,
                                       ObjectMapper objectMapper,
                                       MonitoringManager monitoringManager) {

        this.sqsTemplate = sqsTemplate;
        this.pollingMonitoringQueue = pollingMonitoringQueue;
        this.objectMapper = objectMapper;
        this.monitoringManager = monitoringManager;
    }

    public void initiatePollingProcess(String payload) throws JsonProcessingException {
        PollingAndMonitoringTriggerEvent event = objectMapper.readValue(payload, PollingAndMonitoringTriggerEvent.class);

        if (isValidReferenceId(event.getReferenceId())) {
            monitoringManager.receiveKickoffMessage(event);
        } else {
            log.warn("Invalid referenceId: {}", event.getReferenceId());
        }
    }

    private boolean isValidReferenceId(String referenceId) {
        return UUID_PATTERN.matcher(referenceId).matches();
    }
}
