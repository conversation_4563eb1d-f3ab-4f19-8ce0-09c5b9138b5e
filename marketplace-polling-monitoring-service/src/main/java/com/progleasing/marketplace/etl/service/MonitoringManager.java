package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.constants.Constants;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.message.PollingAndMonitoringTriggerEvent;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import jakarta.transaction.Transactional;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MonitoringManager {

    private final EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private final SqsTemplate sqsTemplate;
    private final long longRunningThreshold;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    @Getter
    public boolean isDormant = false;
    private final String leasibilityExportQueue;
    private final String productExportQueue;
    private final Integer dataRetentionDays;
    private ObjectMapper objectMapper;

    @Autowired
    public MonitoringManager(EtlQueueStatusDataRepository etlQueueStatusDataRepository,
                             ObjectMapper objectMapper,
                             @Value("${aws.sqs.leasibility-exporter.queue-name}")
                                   String leasibilityExportQueue,
                             @Value("${aws.sqs.product-exporter.queue-name}")
                                   String productExportQueue,
                             SqsTemplate sqsTemplate,
                             @Value("${aws.sqs.polling-monitoring.data-retention-days}")
                           Integer dataRetentionDays,
                             @Value("${aws.sqs.polling-monitoring.long-running-threshold}")
                           long longRunningThreshold) {
        this.etlQueueStatusDataRepository = etlQueueStatusDataRepository;
        this.objectMapper=objectMapper;
        this.leasibilityExportQueue = leasibilityExportQueue;
        this.sqsTemplate = sqsTemplate;
        this.productExportQueue = productExportQueue;
        this.dataRetentionDays = dataRetentionDays;
        this.longRunningThreshold = longRunningThreshold;
    }


    public void receiveKickoffMessage(PollingAndMonitoringTriggerEvent event) {
        log.info("Received polling message. Starting polling for {}",event.getReferenceId());
        isDormant = false;
        Timestamp etlStartDate = getEtlStartTime(event.getReferenceId());
        scheduler.scheduleAtFixedRate(() -> checkForNewRecordsWithId(event.getReferenceId(),etlStartDate), 0, 10, TimeUnit.SECONDS);
    }

    private Timestamp getEtlStartTime(String referenceId) {
        List<EtlQueueStatusData> productImport =  etlQueueStatusDataRepository.findByEtlIDAndStatusAndEtlStepIn(referenceId,EtlQueueStatusData.Status.COMPLETED,List.of(EtlQueueStatusData.EtlStep.PRODUCT_IMPORT));
        if(Objects.nonNull(productImport) && productImport.size()==1) {
           return productImport.get(0).getEtlStart();
        }
        return Timestamp.valueOf(LocalDateTime.now());
    }

    @Transactional
    private void checkForNewRecordsWithId(String etlId, Timestamp etlStartTime) {

        try {
            if (isDormant) {
                log.debug("Scheduler is dormant, waiting for kickoff message.");
                return;
            }
            List<EtlQueueStatusData> records = etlQueueStatusDataRepository.findByEtlIDAndRetailerSearch(etlId);
            if (!records.isEmpty()) {
                boolean allComplete = records.stream().allMatch(record ->
                        EtlQueueStatusData.Status.COMPLETED.toString().equals(record.getStatus().toString()));
                if(allComplete){
                    boolean anyNotProcessed = records.stream()
                            .anyMatch(record ->
                                    record.getProcessed() == null);
                    if (anyNotProcessed) {
                        updateRecordsStatus(etlId, EtlQueueStatusData.EtlStep.PRODUCT_SEARCH, EtlQueueStatusData.PollingStatus.IN_PROGRESS);
                    }
                    else {
                        boolean processed = records.stream()
                                .allMatch(record ->
                                        record.getProcessed() != null &&
                                                EtlQueueStatusData.PollingStatus.SUCCESS.toString()
                                                        .equals(record.getProcessed().toString()));
                        if (!processed) {
                            log.info("Polling status - All product search completed for etlId: {} " , etlId);
                            publishToLeasibilityExportQueue(etlId);
                        } else {
                            Optional<EtlQueueStatusData> productLeasibilityExportRecord = etlQueueStatusDataRepository.findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(
                                    etlId, EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT, EtlQueueStatusData.Status.COMPLETED, etlStartTime);
                            if (productLeasibilityExportRecord.isPresent()) {
                                log.info("Polling status - Product Leasibility Export  completed for etlId:{} ", etlId);
                                int exportFileCount = leasibilityExportFileCounter(productLeasibilityExportRecord.get().getSqsMessageData());
                                if(exportFileCount > 0) {
                                    updateRecordsStatus(etlId, EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT, EtlQueueStatusData.PollingStatus.SUCCESS);
                                    Timestamp leasibilityExportUpdate = productLeasibilityExportRecord.get().getLastUpdate();
                                    List<EtlQueueStatusData> productLeasibilityRecords = etlQueueStatusDataRepository.findAllByEtlStepAndStatusAndLastUpdateAfter(
                                            EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT, EtlQueueStatusData.Status.COMPLETED, leasibilityExportUpdate);
                                    if (exportFileCount == productLeasibilityRecords.size()) {
                                        log.info("Polling status - Product Leasibility Import  completed for etlId: {} ", etlId);
                                        boolean allUnprocessed = productLeasibilityRecords.stream()
                                                .allMatch(record -> record.getProcessed() == null);

                                        boolean allProcessedSuccess = productLeasibilityRecords.stream()
                                                .allMatch(record -> EtlQueueStatusData.PollingStatus.SUCCESS.toString()
                                                        .equals(record.getProcessed() != null ? record.getProcessed().toString() : ""));

                                        if (allUnprocessed) {
                                            publishToProductExportQueue(etlId);
                                            productLeasibilityRecords.forEach(importRecord->{
                                                        updateRecordsStatus(importRecord.getEtlID(), EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT, EtlQueueStatusData.PollingStatus.SUCCESS);
                                                    });
                                         } else if (allProcessedSuccess) {
                                            Optional<EtlQueueStatusData> productExportRecords = etlQueueStatusDataRepository.findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(
                                                    etlId, EtlQueueStatusData.EtlStep.PRODUCT_EXPORT, EtlQueueStatusData.Status.COMPLETED, etlStartTime);
                                            if (productExportRecords.isPresent()) {
                                                log.info("Polling status - Product Export completed for etlId:  {}", etlId);
                                                updateRecordsStatus(etlId, EtlQueueStatusData.EtlStep.PRODUCT_EXPORT, EtlQueueStatusData.PollingStatus.SUCCESS);
                                                endPollingSequence(etlId);
                                            }
                                        }
                                    } else {
                                        log.info("Polling status - Product Leasibility Import not started for {}", etlId);
                                    }
                                } else {
                                    log.error("Polling status - No export file captured for Product Leasibility Export  for {}", etlId);
                                    endPollingSequence(etlId);
                                }
                            } else {
                                log.info("Polling status - Product Leasibility Export in progress for {}", etlId);
                            }
                        }
                    }
                } else{
                    log.info("Polling status - Product Search in progress for {}", etlId);
                }
                logErrorMessages(etlId);
                checkForLongRunningProcesses(etlId,etlStartTime);
            } else {
                log.info("Polling status - No product search records found for {}", etlId);
            }
        }
        catch (Exception e){
            log.error(" *** Exception in Polling Monitor : ", e);
            throw e;
        }
    }


    private int leasibilityExportFileCounter(String exportSqsData){
        if (exportSqsData == null || !exportSqsData.contains("|")) {
            return 0;
        }
        return (int) Arrays.stream(
                        exportSqsData.substring(exportSqsData.indexOf('|') + 1)
                                .replaceAll("[\\[\\]\\s]", "")
                                .split(",")
                )
                .filter(file -> file.endsWith(".csv"))
                .count();
    }


    private void logErrorMessages(String etlId) {
        try {
            List<EtlQueueStatusData> errorRecords = etlQueueStatusDataRepository.findByEtlIDAndStatusAndEtlStepIn(etlId, EtlQueueStatusData.Status.ERROR_RESTARTING, Constants.ETL_STEPS);
            if (!errorRecords.isEmpty()) {
                log.info("Polling status - log errors for process in error state for etlId : {} ",etlId);
                for (EtlQueueStatusData record : errorRecords) {
                    log.error("Error in ETL process for etlId:  {}  Step: {}  with id {}" , etlId,record.getEtlStep(),record.getMessageId());
                }
            }
        }  catch (Exception e) {
            log.error("Exception checking for processed in error for etIld {}",etlId);
        }

    }

    private void checkForLongRunningProcesses(String etlId, Timestamp etlStartTime) {
        log.info("Polling status - Checking for long running processed for etlId : {} ",etlId);
        long thresholdInSeconds = TimeUnit.MILLISECONDS.toSeconds(longRunningThreshold);
        try{
            List<EtlQueueStatusData> longRunningRecords = etlQueueStatusDataRepository.findLongRunningProcesses(thresholdInSeconds,etlStartTime);
            for (EtlQueueStatusData record : longRunningRecords) {
                log.info("Long running process detected for etlId: {}, Step: {} ID : {}"  +
                        "with start time {} end time {}", etlId, record.getEtlStep(), record.getId(), record.getEtlStart(), record.getLastUpdate());
                endPollingSequence(etlId);
            }
        } catch (Exception e) {
            log.error("Exception checking for long running process {} for etIld {}",thresholdInSeconds,etlId);
        }
    }


    private void endPollingSequence(String messageId) {
        log.info("Polling status - Stopping polling sequence for etlId : {}", messageId);
        deleteOldEtlData(dataRetentionDays); // Default to 30 days
        isDormant = true;
    }

    private void deleteOldEtlData(int days) {
        log.info("Polling status - Deleting ETL data older than {} days.",days);
        Timestamp cutoffTime = new Timestamp(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(days));
        try {
            etlQueueStatusDataRepository.deleteByLastUpdateBefore(cutoffTime);
        } catch (Exception e) {
            log.error("Exception deleting data older than {}",cutoffTime);
        }
    }


    private void publishToLeasibilityExportQueue(String messageId) {
        log.info("Polling status - Publish message to leasibilty export queue for {}",messageId);
        updateRecordsStatus(messageId, EtlQueueStatusData.EtlStep.PRODUCT_SEARCH, EtlQueueStatusData.PollingStatus.SUCCESS);
        try{
            String payload = String.format("""
                        {  "trigger": "leasibility-export",
                                "timestamp": "%s",
                                "source": "polling-monitoring",
                                "referenceId":"%s"
                        }
                """, Instant.now().toString(), messageId);
            log.info("Publishing Payload: {} to queue {}", objectMapper.writeValueAsString(payload),leasibilityExportQueue);
            sqsTemplate.send(leasibilityExportQueue, MessageBuilder.withPayload(payload)
                    .build());
        }
        catch (Exception e){
            log.error("Error publishing message to: {} for message {}", leasibilityExportQueue,messageId,e);
            updateRecordsStatus(messageId, EtlQueueStatusData.EtlStep.PRODUCT_SEARCH, EtlQueueStatusData.PollingStatus.ERROR);
        }
    }

    private void publishToProductExportQueue(String messageId) {
        log.info("Polling status - Publish message to product export queue for {}",messageId);
        updateRecordsStatus(messageId, EtlQueueStatusData.EtlStep.PRODUCT_EXPORT, EtlQueueStatusData.PollingStatus.IN_PROGRESS);
        try{
            String payload = String.format("""
                        {  "trigger": "product-export",
                                "timestamp": "%s",
                                "source": "polling-monitoring",
                                "referenceId":"%s"
                        }
                """, Instant.now().toString(), messageId);
            log.info("Publishing Payload: {} to {}", objectMapper.writeValueAsString(payload),productExportQueue);
            sqsTemplate.send(productExportQueue, MessageBuilder.withPayload(payload)
                    .build());
            updateRecordsStatus(messageId, EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT, EtlQueueStatusData.PollingStatus.SUCCESS);
        }
        catch (Exception e){
            log.error("Exception publishing message to : {}", productExportQueue,e);
            updateRecordsStatus(messageId, EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT, EtlQueueStatusData.PollingStatus.ERROR);
        }
    }

    private void updateRecordsStatus(String messageId, EtlQueueStatusData.EtlStep etlStep, EtlQueueStatusData.PollingStatus status) {
        try{
            List<EtlQueueStatusData> records = etlQueueStatusDataRepository.findAllByEtlIDAndEtlStep(messageId, etlStep);
            for (EtlQueueStatusData record : records) {
                record.setProcessed(status);
                etlQueueStatusDataRepository.save(record);
            }
        }catch (Exception e){
            log.error("Exception updating the status of step {} to {} for message {}",etlStep.toString(),status.toString(),messageId);
        }

    }
}
