package com.progleasing.marketplace.etl.processor;


import com.progleasing.marketplace.etl.service.PollingAndMonitoringService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;

@Service
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.polling-monitoring.listener.enabled", havingValue = "true", matchIfMissing = true)
public class PollingMonitoringMessageProcessor {

    private PollingAndMonitoringService pollingAndMonitoringService;
    private final Executor executor;

    public PollingMonitoringMessageProcessor(
            PollingAndMonitoringService pollingAndMonitoringService,
            @Qualifier("pollingTaskExecutor") Executor executor) {
        this.pollingAndMonitoringService = pollingAndMonitoringService;
        this.executor = executor;
    }

    @SqsListener(value = "${aws.sqs.polling-monitoring.queue-name}",
            maxConcurrentMessages = "${aws.sqs.concurrent-message}", acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL
    )
    public void handle(List<Message<String>> messages) {
        log.info("POLLING MONITORING Messages received at {}", LocalDate.now());
        List<CompletableFuture<Void>> futures = messages.stream().map(message ->
                CompletableFuture.runAsync(() -> {
                    try {
                        UUID messageId = (UUID) message.getHeaders().get("id");
                        String payload = message.getPayload();
                        log.info("Received message with payload: {} and id {}", payload, messageId.toString());
                        pollingAndMonitoringService.initiatePollingProcess(payload);
                        Acknowledgement.acknowledge(message);
                    } catch (Exception e) {
                        log.error("Error processing message", e);
                    }
                }, executor)
        ).toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

}