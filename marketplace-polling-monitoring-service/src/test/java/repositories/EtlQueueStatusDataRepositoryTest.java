package repositories;


import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EtlQueueStatusDataRepositoryTest {

    @Mock
    private EtlQueueStatusDataRepository repository;

    private EtlQueueStatusData etlQueueStatusData;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        etlQueueStatusData = new EtlQueueStatusData();
        etlQueueStatusData.setEtlID(UUID.randomUUID().toString());
        etlQueueStatusData.setEtlStep(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT);
        etlQueueStatusData.setStatus(EtlQueueStatusData.Status.COMPLETED);
        etlQueueStatusData.setLastUpdate(new Timestamp(System.currentTimeMillis() - 5000));
    }

    @Test
    public void testFindByEtlID() {
        when(repository.findByEtlID("testEtlId"))
                .thenReturn(Collections.singletonList(etlQueueStatusData));

        List<EtlQueueStatusData> result = repository.findByEtlID("testEtlId");

        assertThat(result).hasSize(1);
        assertThat(result.get(0).getStatus()).isEqualTo(EtlQueueStatusData.Status.COMPLETED);

        verify(repository, times(1)).findByEtlID("testEtlId");
    }

    @Test
    public void testFindByEtlIDAndRetailerSearch() {
        String etlId = "28c8594a-1d04-4aab-871f-7a07fa73f716";
        EtlQueueStatusData mockData = new EtlQueueStatusData(
                null,
                etlId,
                "1234",
                new Timestamp(System.currentTimeMillis()),
                "impact",
                EtlQueueStatusData.EtlStep.PRODUCT_IMPORT,
                "Sample SQS Message Data",
                EtlQueueStatusData.Status.COMPLETED,
                EtlQueueStatusData.PollingStatus.SUCCESS,
                new Timestamp(System.currentTimeMillis())
        );

        when(repository.findByEtlIDAndRetailerSearch(etlId)).thenReturn(List.of(mockData));

        List<EtlQueueStatusData> results = repository.findByEtlIDAndRetailerSearch(etlId);

        assertThat(results).isNotEmpty();
        assertThat(results).allMatch(record -> record.getRetailerSearch().matches("impact|google|sovrn|cj|amazon"));

        verify(repository, times(1)).findByEtlIDAndRetailerSearch(etlId);
    }

    @Test
    public void testFindAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter() {
        String etlId = "28c8594a-1d04-4aab-871f-7a07fa73f716";
        EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT;
        EtlQueueStatusData.Status status = EtlQueueStatusData.Status.COMPLETED;
        Timestamp lastUpdate = new Timestamp(System.currentTimeMillis() - 10000);

        when(repository.findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(etlId, etlStep, status, lastUpdate))
                .thenReturn(Optional.of(etlQueueStatusData));

        Optional<EtlQueueStatusData> result = repository.findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(etlId, etlStep, status, lastUpdate);

        assertThat(result).isPresent();
        assertThat(result.get().getEtlStep()).isEqualTo(etlStep);
        assertThat(result.get().getStatus()).isEqualTo(status);

        verify(repository, times(1)).findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(etlId, etlStep, status, lastUpdate);
    }
}
