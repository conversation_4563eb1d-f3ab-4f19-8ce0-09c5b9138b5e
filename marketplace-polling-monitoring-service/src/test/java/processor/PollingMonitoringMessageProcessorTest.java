package processor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.progleasing.marketplace.etl.processor.PollingMonitoringMessageProcessor;
import com.progleasing.marketplace.etl.service.PollingAndMonitoringService;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.messaging.Message;

import java.lang.reflect.Field;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PollingMonitoringMessageProcessorTest {

    @Mock
    private PollingAndMonitoringService pollingAndMonitoringService;

    @InjectMocks
    private PollingMonitoringMessageProcessor processor;

    @Mock
    private AcknowledgementCallback acknowledgementCallback;


    @BeforeEach
    void setUp() {
        Executor directExecutor = Runnable::run;
        processor = new PollingMonitoringMessageProcessor(pollingAndMonitoringService, directExecutor);
    }

    @Test
    public void testHandleMessagesSuccessfully() throws ExecutionException, InterruptedException, JsonProcessingException {
        String referenceId = UUID.randomUUID().toString();
        String payload = String.format("{referenceId: %s}", referenceId);
        Message<String> message = MessageBuilder.withPayload(payload)
                .setHeader("customId", referenceId)
                .build();

        processor.handle(List.of(message));

        Mockito.verify(pollingAndMonitoringService, times(1)).initiatePollingProcess(payload);
    }

    @Test
    public void testHandleMessageWithException() throws JsonProcessingException {
        String referenceId = UUID.randomUUID().toString();
        String payload = String.format("{referenceId: %s}", referenceId);
        Message<String> message = MessageBuilder.withPayload(payload)
                .setHeader("customId", referenceId)
                .build();

        doThrow(new RuntimeException("Test Exception")).when(pollingAndMonitoringService).initiatePollingProcess(payload);

        processor.handle(List.of(message));
        verify(pollingAndMonitoringService, times(1)).initiatePollingProcess(payload);
    }

    @Test
    public void testHandleMultipleMessages() throws ExecutionException, InterruptedException, JsonProcessingException {
        String referenceId1 = UUID.randomUUID().toString();
        String referenceId2 = UUID.randomUUID().toString();
        String payload1 = String.format("{referenceId: %s}", referenceId1);
        String payload2 = String.format("{referenceId: %s}", referenceId2);

        Message<String> message1 = MessageBuilder.withPayload(payload1)
                .setHeader("customId", referenceId1)
                .build();
        Message<String> message2 = MessageBuilder.withPayload(payload2)
                .setHeader("customId", referenceId2)
                .build();

        processor.handle(List.of(message1, message2));

        Mockito.verify(pollingAndMonitoringService, times(1)).initiatePollingProcess(payload1);
        Mockito.verify(pollingAndMonitoringService, times(1)).initiatePollingProcess(payload2);
  }
}
