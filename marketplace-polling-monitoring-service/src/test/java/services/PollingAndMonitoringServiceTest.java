package services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.message.PollingAndMonitoringTriggerEvent;
import com.progleasing.marketplace.etl.service.MonitoringManager;
import com.progleasing.marketplace.etl.service.PollingAndMonitoringService;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PollingAndMonitoringServiceTest {

    @Mock
    private SqsTemplate sqsTemplate;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private MonitoringManager monitoringManager;

    @InjectMocks
    private PollingAndMonitoringService pollingAndMonitoringService;

    @Value("${aws.sqs.polling-monitoring.queue-name}")
    private String pollingMonitoringQueue;

    @BeforeEach
    void setUp() {
        pollingAndMonitoringService = new PollingAndMonitoringService(
                sqsTemplate,
                pollingMonitoringQueue,
                objectMapper,
                monitoringManager
        );
    }

    @Test
    void testInitiatePollingProcess_validReferenceId() throws Exception {
        String validPayload = "{referenceId:28c8594a-1d04-4aab-871f-7a07fa73f716}";
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId("28c8594a-1d04-4aab-871f-7a07fa73f716");

        when(objectMapper.readValue(validPayload, PollingAndMonitoringTriggerEvent.class)).thenReturn(event);

        pollingAndMonitoringService.initiatePollingProcess(validPayload);

        verify(monitoringManager, times(1)).receiveKickoffMessage(event);
        verifyNoInteractions(sqsTemplate);
    }

    @Test
    void testInitiatePollingProcess_invalidReferenceId() throws Exception {
        String invalidPayload = "{referenceId:invalid-uuid}";
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId("invalid-uuid");

        when(objectMapper.readValue(invalidPayload, PollingAndMonitoringTriggerEvent.class)).thenReturn(event);

        pollingAndMonitoringService.initiatePollingProcess(invalidPayload);

        verify(monitoringManager, never()).receiveKickoffMessage(any());
        verifyNoInteractions(sqsTemplate);
    }
}

