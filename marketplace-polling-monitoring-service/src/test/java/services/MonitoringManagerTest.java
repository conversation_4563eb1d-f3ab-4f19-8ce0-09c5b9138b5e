package services;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.message.PollingAndMonitoringTriggerEvent;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import com.progleasing.marketplace.etl.service.MonitoringManager;
import io.awspring.cloud.sqs.operations.SqsTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.InvocationTargetException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
public class MonitoringManagerTest {

    @Mock
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @Mock
    private SqsTemplate sqsTemplate;

    private MonitoringManager monitoringManager;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setup() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        monitoringManager = new MonitoringManager(
                etlQueueStatusDataRepository,
                objectMapper,
                "leasibility-export-queue",
                "product-export-queue",
                sqsTemplate,
                30,
                60000L
        );
    }

    @Test
    void testReceiveKickoffMessage_shouldStartScheduler() {
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId("test-etl-id");

        monitoringManager.receiveKickoffMessage(event);

        assertFalse(monitoringManager.isDormant());
    }

    @Test
    void testCheckForNewRecordsWithId_allRecordsComplete_shouldSendMessageToLeasibilityExportQueue() {
        String etlId = "etl-123";
        EtlQueueStatusData data =  EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_SEARCH)
                .status(EtlQueueStatusData.Status.COMPLETED)
                .processed(EtlQueueStatusData.PollingStatus.IN_PROGRESS)
                .build();

        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearch(anyString()))
                .thenReturn(List.of(data));

        when(etlQueueStatusDataRepository.findAllByEtlIDAndEtlStep(anyString(), any()))
                .thenReturn(List.of(data));
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId(UUID.randomUUID().toString());
        monitoringManager.receiveKickoffMessage(event);

        await().atMost(5, SECONDS).until(() ->
                !monitoringManager.isDormant()
        );
        verify(sqsTemplate, atLeastOnce()).send(eq("leasibility-export-queue"), any());
    }

    @Test
    void testCheckForNewRecordsWithId_LeasibilityExportInProgress() {
        String etlId = "etl-123";

        EtlQueueStatusData searchData = EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_SEARCH)
                .status(EtlQueueStatusData.Status.COMPLETED)
                .processed(EtlQueueStatusData.PollingStatus.SUCCESS)
                .build();

        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearch(anyString()))
                .thenReturn(List.of(searchData));

        when(etlQueueStatusDataRepository.findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(anyString(), eq(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT)
                , eq(EtlQueueStatusData.Status.COMPLETED), any()))
                .thenReturn(Optional.empty());
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId(UUID.randomUUID().toString());
        monitoringManager.receiveKickoffMessage(event);

        await().atMost(5, SECONDS).until(() ->
                !monitoringManager.isDormant()
        );
        verify(etlQueueStatusDataRepository, never()).findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(anyString(), eq(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT)
                , eq(EtlQueueStatusData.Status.COMPLETED), any());
    }


    @Test
    void testCheckForNewRecordsWithId_LeasibilityExportComplete() {
        String etlId = "etl-123";
        EtlQueueStatusData leasibilityExport = EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT)
                .sqsMessageData("0c746d1c-bfa3-4c65-a39a-da9956c2f186|[product_2_d2c_07232025_170927.csv]")
                .status(EtlQueueStatusData.Status.COMPLETED)
                .build();
        EtlQueueStatusData searchData = EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_SEARCH)
                .status(EtlQueueStatusData.Status.COMPLETED)
                .processed(EtlQueueStatusData.PollingStatus.SUCCESS)
                .build();

        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearch(anyString()))
                .thenReturn(List.of(searchData));

        when(etlQueueStatusDataRepository.findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(anyString(), eq(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT)
                , eq(EtlQueueStatusData.Status.COMPLETED), any()))
                .thenReturn(Optional.of(leasibilityExport));
        when(etlQueueStatusDataRepository.findAllByEtlStepAndStatusAndLastUpdateAfter(eq(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT)
                , eq(EtlQueueStatusData.Status.COMPLETED), any()))
                .thenReturn(Collections.emptyList());
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId(UUID.randomUUID().toString());
        monitoringManager.receiveKickoffMessage(event);
        await().atMost(10, SECONDS).until(() ->
                !monitoringManager.isDormant()
        );
        verify(etlQueueStatusDataRepository, never()).findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(anyString(), eq(EtlQueueStatusData.EtlStep.PRODUCT_EXPORT)
                , eq(EtlQueueStatusData.Status.COMPLETED), any());
    }


    @Test
    void testCheckForNewRecordsWithId_LeasibilityImportComplete() {
        String etlId = "etl-123";
        EtlQueueStatusData leasibilityExport = EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT)
                .sqsMessageData("0c746d1c-bfa3-4c65-a39a-da9956c2f186|[product_2_d2c_07232025_170927.csv]")
                .status(EtlQueueStatusData.Status.COMPLETED)
                .build();
        EtlQueueStatusData searchData = EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_SEARCH)
                .status(EtlQueueStatusData.Status.COMPLETED)
                .processed(EtlQueueStatusData.PollingStatus.SUCCESS)
                .build();
        EtlQueueStatusData leasibilityImport = EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT)
                .status(EtlQueueStatusData.Status.COMPLETED)

                .build();

        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearch(anyString()))
                .thenReturn(List.of(searchData));

        when(etlQueueStatusDataRepository.findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(anyString(), eq(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT)
                , eq(EtlQueueStatusData.Status.COMPLETED), any()))
                .thenReturn(Optional.of(leasibilityExport));
        when(etlQueueStatusDataRepository.findAllByEtlStepAndStatusAndLastUpdateAfter(eq(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT)
                , eq(EtlQueueStatusData.Status.COMPLETED), any()))
                .thenReturn(List.of(leasibilityImport));
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId(UUID.randomUUID().toString());
        monitoringManager.receiveKickoffMessage(event);
        await().atMost(10, SECONDS).until(() ->
                !monitoringManager.isDormant()
        );
        verify(sqsTemplate, atLeastOnce()).send(eq("product-export-queue"), any());
    }

    @Test
    void testCheckForNewRecordsWithId_ProductExportComplete() {
        String etlId = "etl-123";
        PollingAndMonitoringTriggerEvent event = new PollingAndMonitoringTriggerEvent();
        event.setReferenceId(UUID.randomUUID().toString());
        monitoringManager.receiveKickoffMessage(event);
        verify(sqsTemplate, never()).send(eq("product-export-queue"), any());
    }

    @Test
    void testCheckForNewRecordsWithId_longRunning() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String etlId = "etl-123";
        Timestamp now = new Timestamp(System.currentTimeMillis());
        Timestamp earlier = new Timestamp(System.currentTimeMillis() - 100_000); // 100 seconds ago

        EtlQueueStatusData longRunningRecord = EtlQueueStatusData.builder()
                .etlID(etlId)
                .etlStep(EtlQueueStatusData.EtlStep.PRODUCT_SEARCH)
                .status(EtlQueueStatusData.Status.COMPLETED)
                .processed(null)
                .build();
        longRunningRecord.setLastUpdate(now);
        longRunningRecord.setEtlStart(earlier);
        when(etlQueueStatusDataRepository.findLongRunningProcesses(
                anyLong(), any(Timestamp.class)))
                .thenReturn(List.of(longRunningRecord));
        var method = MonitoringManager.class.getDeclaredMethod("checkForLongRunningProcesses", String.class, Timestamp.class);
        method.setAccessible(true);
        method.invoke(monitoringManager, etlId, Timestamp.valueOf(LocalDateTime.now()));

        assertTrue(monitoringManager.isDormant(), "Polling should be set to dormant for long-running process");
    }
}

