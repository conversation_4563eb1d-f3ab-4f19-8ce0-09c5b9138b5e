spring:
  application:
    name: progressive.leasing
  datasource:
    url: ****************************************
    username: admin
    password: admin123
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.PostgresSQLDialect
  cloud:
    aws:
      region:
        static: us-east-1
      credentials:
        access-key: awsAccessKey
        secret-key: awsSecretKey

impact:
  etl:
    host: example.com
    userName: userName
    password: password
    accountSid: account
    maxProductsToImport: 10

aws:
  sqs:
    listener:
      impact:
        enabled: true
    queue-name: amazon-retailer-test-queue
    concurrent-message: 2


