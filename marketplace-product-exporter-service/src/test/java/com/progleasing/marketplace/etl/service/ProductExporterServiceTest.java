package com.progleasing.marketplace.etl.service;

import com.commercetools.importapi.client.*;
import com.commercetools.importapi.models.importcontainers.ImportContainer;
import com.commercetools.importapi.models.importcontainers.ImportContainerDraft;
import com.commercetools.importapi.models.importcontainers.ImportContainerDraftBuilder;
import com.commercetools.importapi.models.importrequests.ProductDraftImportRequest;
import com.commercetools.importapi.models.productdrafts.ProductDraftImport;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.dto.ImageUrlsByProductId;
import com.progleasing.marketplace.etl.entity.AdditionalProductData;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.PriceData;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageImpl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductExporterServiceTest {

    @InjectMocks
    private ProductExporterService productExporterService;

    @Mock
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @Mock
    private ProductDataRepository productDataRepository;

    @Mock
    private PriceDataRepository priceDataRepository;

    @Mock
    ObjectMapper objectMapper;

    @Mock
    @Qualifier("ctImportApiClient")
    ProjectApiRoot importProjectApi;

    @Mock
    ByProjectKeyImportContainersRequestBuilder projectKeyImportContainersRequestBuilder;

    @Mock
    ByProjectKeyImportContainersPost projectKeyImportContainersPost;

    @Mock
    ByProjectKeyProductDraftsRequestBuilder projectKeyProductDraftsRequestBuilder;

    @Mock
    ByProjectKeyProductDraftsImportContainersRequestBuilder projectKeyProductDraftsImportContainersRequestBuilder;

    @Mock
    ByProjectKeyProductDraftsImportContainersByImportContainerKeyRequestBuilder productDraftsImportContainersBuilder;

    @Mock
    ByProjectKeyProductDraftsImportContainersByImportContainerKeyPost projectKeyProductDraftsImportContainersByImportContainerKeyPost;

    @Mock
    ProductDeleteExportService productDeleteExportService;

    @Mock
    AdditionalProductDataRepository additionalProductDataRepository;

    @Mock
    ImageDataRepository imageDataRepository;

    @Mock
    ByProjectKeyImportContainersGet byProjectKeyImportContainersGet;

    @Test
    void testUploadProducts_capturesProductDraftImportRequest() throws Exception {
        String messagePayload = "{\"referenceId\": \"ref1\"}";
        String messageId = "msg-123";
        ETLMessageDTO messageDTO =  new ETLMessageDTO();
        messageDTO.setReferenceId("123abc");
        when(objectMapper.readValue(anyString(), eq(ETLMessageDTO.class))).thenReturn(messageDTO);
        ImportContainer importContainer = mock(ImportContainer.class);
        when(importContainer.getKey()).thenReturn("pl-product-import1");
        PageImpl<ProductData> mockPage = new PageImpl<>(getProducts());
        when(importProjectApi.importContainers()).thenReturn(projectKeyImportContainersRequestBuilder);
        when(projectKeyImportContainersRequestBuilder.post(any(ImportContainerDraft.class))).thenReturn(projectKeyImportContainersPost);
        when(projectKeyImportContainersPost.executeBlocking()).thenReturn(new ApiHttpResponse<>(200,null,importContainer));
        when(productDataRepository.findByLastModified(any(), any(), any()))
                .thenReturn(mockPage);
        AdditionalProductData additionalProductData = new AdditionalProductData();
        additionalProductData.setId("prod3");
        when(priceDataRepository.findByIdIn(any())).thenReturn(getPriceData());
        when(imageDataRepository.findImageUrlsByProductIds(any())).thenReturn(getImageData());
        when(additionalProductDataRepository.findAllById(any())).thenReturn(List.of(additionalProductData));
        when(importProjectApi.productDrafts()).thenReturn(projectKeyProductDraftsRequestBuilder);
        when(projectKeyProductDraftsRequestBuilder.importContainers()).thenReturn(projectKeyProductDraftsImportContainersRequestBuilder);
        when(projectKeyProductDraftsImportContainersRequestBuilder.withImportContainerKeyValue(anyString())).thenReturn(productDraftsImportContainersBuilder);
        ArgumentCaptor<ProductDraftImportRequest> productDraftImportRequestArgumentCaptor =
               ArgumentCaptor.forClass(ProductDraftImportRequest.class);
        when(productDraftsImportContainersBuilder.post(productDraftImportRequestArgumentCaptor.capture())).thenReturn(projectKeyProductDraftsImportContainersByImportContainerKeyPost);
        when(importProjectApi.importContainers()).thenReturn(projectKeyImportContainersRequestBuilder);
//      when(projectKeyImportContainersRequestBuilder.get()).thenReturn(byProjectKeyImportContainersGet);
//      when(byProjectKeyImportContainersGet.addLimit(anyInt())).thenReturn(byProjectKeyImportContainersGet);
//      when(byProjectKeyImportContainersGet.executeBlocking()).thenReturn(new ApiHttpResponse<>(200,null,null));
        productExporterService.uploadProducts(messagePayload, messageId);
        verify(productDraftsImportContainersBuilder).post(productDraftImportRequestArgumentCaptor.capture());
        ProductDraftImportRequest request = productDraftImportRequestArgumentCaptor.getValue();
        assertNotNull(request);
        assertEquals(2, request.getResources().size());
        ProductDraftImport draft = request.getResources().get(0);
        assertEquals("prod1", draft.getKey());
        ProductDraftImport draft2 = request.getResources().get(1);
        assertEquals("prod2", draft2.getKey());
    }

    @Test
    void testUploadProducts_capturesProductDraftImportRequest_NoData() throws Exception {
        String messagePayload = "{\"referenceId\": \"ref1\"}";
        String messageId = "msg-123";
        ETLMessageDTO messageDTO =  new ETLMessageDTO();
        messageDTO.setReferenceId("123abc");
        when(objectMapper.readValue(anyString(), eq(ETLMessageDTO.class))).thenReturn(messageDTO);
        ImportContainer importContainer = mock(ImportContainer.class);
        PageImpl<ProductData> mockPage = new PageImpl<>(Collections.emptyList());
        when(importProjectApi.importContainers()).thenReturn(projectKeyImportContainersRequestBuilder);
        when(projectKeyImportContainersRequestBuilder.post(any(ImportContainerDraft.class))).thenReturn(projectKeyImportContainersPost);
        when(projectKeyImportContainersPost.executeBlocking()).thenReturn(new ApiHttpResponse<>(200,null,importContainer));
        when(importProjectApi.importContainers()).thenReturn(projectKeyImportContainersRequestBuilder);
      //  when(projectKeyImportContainersRequestBuilder.get()).thenReturn(byProjectKeyImportContainersGet);
     //   when(byProjectKeyImportContainersGet.addLimit(anyInt())).thenReturn(byProjectKeyImportContainersGet);
      //  when(byProjectKeyImportContainersGet.executeBlocking()).thenReturn(new ApiHttpResponse<>(200,null,null));
        when(productDataRepository.findByLastModified(any(), any(), any()))
                .thenReturn(mockPage);
        verify(priceDataRepository,times(0)).findByIdIn(anyList());
        productExporterService.uploadProducts(messagePayload, messageId);
    }


    private List<ProductData> getProducts() {
        ProductData productData = new ProductData();
        productData.setId("prod1");
        productData.setName("name");
        productData.setDescription("description");
        productData.setLongDescription("long description");
        productData.setBrand("brand");
        productData.setCategoryKey("categoryKey1,categoryKey2");
        productData.setSearchTerm("search");
        productData.setLeasable(true);
        productData.setRetailerKey("ebay");
        productData.setRetailerName("eBay");
        productData.setProductTrackingUrl("http://product.com");
        productData.setAffiliateAddToCartUrl("http://product.com");
        productData.setRatingCount(0);
        productData.setRatingAverage(BigDecimal.ZERO);
        ProductData productData1 = new ProductData();
        productData1.setId("prod2");
        productData1.setName("name");
        productData1.setDescription("description");
        productData1.setLongDescription("long description");
        productData1.setBrand("brand");
        productData1.setCategoryKey("categoryKey1,categoryKey2");
        productData1.setSearchTerm("search");
        productData1.setLeasable(true);
        productData1.setRetailerKey("ebay");
        productData1.setRetailerName("eBay");
        productData1.setProductTrackingUrl("http://product1.com");
        productData1.setAffiliateAddToCartUrl("http://product1.com");
        productData1.setRatingCount(0);
        productData1.setRatingAverage(BigDecimal.ZERO);
        return List.of(productData,productData1);
    }

    private List<PriceData> getPriceData(){
        PriceData priceData = new PriceData();
        priceData.setId("prod1");
        priceData.setPrice(BigDecimal.valueOf(12.90));
        PriceData priceData1 = new PriceData();
        priceData1.setId("prod2");
        priceData1.setPrice(BigDecimal.valueOf(10.90));
        return List.of(priceData,priceData1);
    }

    private List<ImageUrlsByProductId> getImageData(){
        ImageUrlsByProductId item1 = mockImageUrlsByProductId("prod1", "http://image.com/1a.jpg");
        ImageUrlsByProductId item2 = mockImageUrlsByProductId("prod1", "http://image.com/1b.jpg");
        ImageUrlsByProductId item3 = mockImageUrlsByProductId("prod2", "http://image.com/2a.jpg");
        return Arrays.asList(item1, item2, item3);
    }

    private ImageUrlsByProductId mockImageUrlsByProductId(String id, String imageUrl) {
        return new ImageUrlsByProductId() {
            @Override
            public String getId() {
                return id;
            }

            @Override
            public String getImageUrl() {
                return imageUrl;
            }
        };
    }
}
