package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.*;
import com.commercetools.api.models.product.Product;
import com.commercetools.api.models.product.ProductPagedQueryResponse;
import com.commercetools.api.models.product.ProductProjection;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.repositories.*;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProductDeleteExportServiceTest {
    @Mock
    private ProjectApiRoot importProjectApi;

    @Mock
    private ProductDataRepository productDataRepository;

    @Mock
    private PriceDataRepository priceDataRepository;

    @Mock
    private AdditionalProductDataRepository additionalProductDataRepository;

    @Mock
    private ImageDataRepository imageDataRepository;

    @InjectMocks
    private ProductDeleteExportService productExporterService;

    @Mock
    ByProjectKeyProductsRequestBuilder byProjectKeyProductsRequestBuilder;

    @Mock
    ByProjectKeyProductsByIDRequestBuilder byProjectKeyProductsByIDRequestBuilder;

    @Mock
    ByProjectKeyProductsByIDGet byProjectKeyProductsByIDGet;

    @Mock
    ByProjectKeyProductsByIDDelete byProjectKeyProductsByIDDelete;

    @Mock
    ByProjectKeyProductsGet byProjectKeyProductsGet;

    @Mock
    private ProductDataDisabledRepository productDataDisabledRepository;

    @Mock
    ProductPagedQueryResponse response;



    @Test
    void testDeleteMarkedProducts_NoProductsMarked() {
        when(productDataRepository.findMarkedForDeletion(any(LocalDate.class)))
                .thenReturn(Collections.emptyList());

        when(importProjectApi.products()).thenReturn(byProjectKeyProductsRequestBuilder);
        when(byProjectKeyProductsRequestBuilder.get()).thenReturn(byProjectKeyProductsGet);
        when(byProjectKeyProductsGet.withWhere(anyString())).thenReturn(byProjectKeyProductsGet);
        when(byProjectKeyProductsGet.executeBlocking()).thenReturn(new ApiHttpResponse<ProductPagedQueryResponse>(200, null,response));
        productExporterService.deleteMarkedProducts("message1");

        verify(productDataRepository, times(1)).findMarkedForDeletion(any(LocalDate.class));
        verify(productDataRepository, never()).deleteAll(anyList());
        verify(importProjectApi, times(1)).products();
        verify(productDataDisabledRepository, never()).saveAll(any());
    }

    @Test
    void testDeleteMarkedProducts_WithProductsMarked() {
        ProductData productData = new ProductData();
        productData.setId("product-id");
        List<ProductData> productsToDelete = List.of(productData);

        when(productDataRepository.findMarkedForDeletion(any(LocalDate.class)))
                .thenReturn(productsToDelete);

        ApiHttpResponse<ProductProjection> apiHttpResponse = mock(ApiHttpResponse.class);
        Product productDeleted = mock(Product.class);
        when(importProjectApi.products()).thenReturn(byProjectKeyProductsRequestBuilder);
        when(importProjectApi.products()).thenReturn(byProjectKeyProductsRequestBuilder);
        when(byProjectKeyProductsRequestBuilder.get()).thenReturn(byProjectKeyProductsGet);
        when(byProjectKeyProductsGet.withWhere(anyString())).thenReturn(byProjectKeyProductsGet);
        when(byProjectKeyProductsGet.executeBlocking()).thenReturn(new ApiHttpResponse<ProductPagedQueryResponse>(200, null,response));

        productExporterService.deleteMarkedProducts("message2");

        verify(productDataRepository, times(1)).deleteAllById(anySet());
        verify(priceDataRepository, times(1)).deleteAllById(anySet());
        verify(imageDataRepository, times(1)).deleteAllByIdIn(anyList());
        verify(productDataDisabledRepository, never()).saveAll(any());
    }
}
