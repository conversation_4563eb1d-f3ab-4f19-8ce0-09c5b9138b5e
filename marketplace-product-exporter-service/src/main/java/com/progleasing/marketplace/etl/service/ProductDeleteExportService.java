package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.product.Product;
import com.commercetools.api.models.product.ProductPagedQueryResponse;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.entity.ProductDataDisabled;
import com.progleasing.marketplace.etl.repositories.*;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ProductDeleteExportService {
    private ProductDataRepository productDataRepository;
    private PriceDataRepository priceDataRepository;
    private ImageDataRepository imageDataRepository;
    private AdditionalProductDataRepository additionalProductDataRepository;
    private ProductDataDisabledRepository productDataDisabledRepository;

    private ProjectApiRoot apiRoot;

    public ProductDeleteExportService(ProductDataRepository productDataRepository,
                                      PriceDataRepository priceDataRepository,
                                      ImageDataRepository imageDataRepository,
                                      AdditionalProductDataRepository additionalProductDataRepository,
                                      ProductDataDisabledRepository productDataDisabledRepository,
                                      @Qualifier("ctApiClient") ProjectApiRoot apiRoot) {
        this.productDataRepository = productDataRepository;
        this.priceDataRepository = priceDataRepository;
        this.imageDataRepository = imageDataRepository;
        this.additionalProductDataRepository = additionalProductDataRepository;
        this.productDataDisabledRepository = productDataDisabledRepository;
        this.apiRoot = apiRoot;
    }

    @Transactional
    public void deleteMarkedProducts(String messageId) {
        try {
            LocalDate currentDate = LocalDate.now();
            List<ProductData> productsToDelete = productDataRepository.findMarkedForDeletion(currentDate);
            ProductPagedQueryResponse response = apiRoot.products()
                    .get()
                    .withWhere("masterData(published=false and staged(masterVariant(attributes(name=\"disabled\" and value=true))))")
                    .executeBlocking()
                    .getBody();

            List<String> commerceToolsProductKeys = Optional.ofNullable(response)
                    .map(ProductPagedQueryResponse::getResults)
                    .orElse(List.of())
                    .stream()
                    .map(Product::getKey)
                    .collect(Collectors.toList());

            if (!commerceToolsProductKeys.isEmpty()) {
                log.info("Found products marked for deletion: {} for message {}",
                        String.join(", ", commerceToolsProductKeys), messageId);
            }

            if (productsToDelete.isEmpty() && commerceToolsProductKeys.isEmpty()) {
                log.info("No products marked for deletion for message {}", messageId);
                return;
            }

            Set<String> productKeysToDelete = Stream.concat(
                    productsToDelete.stream().map(ProductData::getId),
                    commerceToolsProductKeys.stream()
            ).collect(Collectors.toSet());

            if (!commerceToolsProductKeys.isEmpty()) {
                List<ProductData> productsToCopy = productDataRepository.findAllById(commerceToolsProductKeys);
                if (!productsToCopy.isEmpty()) {
                    List<ProductDataDisabled> disabledList = productsToCopy.stream()
                            .map(this::getProductDataDisabled)
                            .collect(Collectors.toList());
                    productDataDisabledRepository.saveAll(disabledList);
                    log.info("Copied products Keys: {} to product_data_disabled for message {}",
                            String.join(", ", commerceToolsProductKeys), messageId);
                } else {
                    log.info("No matching products found in product_data to copy. Keys: {}",
                            String.join(", ", commerceToolsProductKeys));
                }
            }

            ExecutorService executorService = Executors.newFixedThreadPool(10);
            productKeysToDelete.forEach(productKey -> executorService.submit(() -> deleteFromCommercetools(productKey)));
            executorService.shutdown();
            productDataRepository.deleteAllById(productKeysToDelete);
            priceDataRepository.deleteAllById(productKeysToDelete);
            imageDataRepository.deleteAllByIdIn(new ArrayList<>(productKeysToDelete));
            additionalProductDataRepository.deleteAllById(productKeysToDelete);
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        } catch (Exception e) {
            log.error("Exception deleting products for etl {}",messageId);
        }
    }

    private void deleteFromCommercetools(String productKey) {
        try {
            Product product = getProductVersion(productKey);
            apiRoot.products()
                    .withId(product.getId())
                    .delete()
                    .withVersion(product.getVersion())
                    .executeBlocking();
            log.info("Deleted product, productKey {} from CommerceTools", productKey);
        } catch (Exception e) {
            String msg = e.getMessage() != null ? e.getMessage() : "";
            if (msg.contains("404") || msg.contains("Not Found")) {
                log.warn("Product, productKey {} was already deleted in CommerceTools.", productKey);
            } else {
                log.error("Failed to delete product, productKey {} from CommerceTools: {}", productKey, msg);
            }
        }
    }


    private ProductDataDisabled getProductDataDisabled(ProductData product) {
        ProductDataDisabled disabledProduct = new ProductDataDisabled();
        disabledProduct.setId(product.getId());
        disabledProduct.setName(product.getName());
        disabledProduct.setDescription(product.getDescription());
        disabledProduct.setLongDescription(product.getLongDescription());
        disabledProduct.setRetailerName(product.getRetailerName());
        disabledProduct.setRetailerKey(product.getRetailerKey());
        disabledProduct.setBrand(product.getBrand());
        disabledProduct.setRatingAverage(product.getRatingAverage());
        disabledProduct.setRatingCount(product.getRatingCount());
        disabledProduct.setProductTrackingUrl(product.getProductTrackingUrl());
        disabledProduct.setAffiliateAddToCartUrl(product.getAffiliateAddToCartUrl());
        disabledProduct.setLastModified(product.getLastModified());
        disabledProduct.setLastSentToCT(product.getLastSentToCT());
        disabledProduct.setCategoryKey(product.getCategoryKey());
        disabledProduct.setL1CategoryName(product.getL1CategoryName());
        disabledProduct.setL2CategoryName(product.getL2CategoryName());
        disabledProduct.setLeasable(product.isLeasable());
        disabledProduct.setSearchTerm(product.getSearchTerm());
        disabledProduct.setMarkedForDeletion(product.getMarkedForDeletion());
        disabledProduct.setFeedSource(product.getFeedSource());
        return disabledProduct;
    }

    Product getProductVersion(String productKey) {
        return apiRoot.products()
                .withKey(productKey)
                .get()
                .executeBlocking()
                .getBody();
    }
}
