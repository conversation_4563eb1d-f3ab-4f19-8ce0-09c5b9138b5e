package com.progleasing.marketplace.etl.processor;


import com.progleasing.marketplace.etl.service.ProductExporterService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

@Service
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.product-exporter.listener.enabled", havingValue = "true", matchIfMissing = true)
public class ProductExporterMessageProcessor {

    private final ProductExporterService productExporterService;

    private final Executor executor;

    public ProductExporterMessageProcessor(
            ProductExporterService productExporterService,
            @Qualifier("etlTaskExecutor") Executor executor) {
        this.productExporterService = productExporterService;
        this.executor = executor;
    }

    @SqsListener(value = "${aws.sqs.product-exporter.queue-name}",
            maxConcurrentMessages = "${aws.sqs.concurrent-message}", acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL
    )
    @Transactional
    public void handle(List<Message<String>> messages) {
        log.info("Messages received  in Product Exporter{}",messages.size());
        List<CompletableFuture<Void>> futures = messages.stream().map(message ->
                CompletableFuture.runAsync(() -> {
                    try {
                        UUID messageId = (UUID) message.getHeaders().get("id");
                        String payload = message.getPayload();
                        log.info("Received message with payload: {} and id {}", payload, messageId.toString());
                        productExporterService.uploadProducts(payload, messageId.toString());
                        Acknowledgement.acknowledge(message);
                    } catch (Exception e) {
                        log.error("Error processing message", e);
                    }
                }, executor)
        ).toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

}
