package com.progleasing.marketplace.etl.constants;


public class PLConstants {
    private PLConstants() {}

    public static final int DB_BATCH_SIZE = 500;
    public static final int IMPORT_BATCH_SIZE = 20;
    public static final String IMPORT_CONTAINER_KEY = "pl-product-import-";
    public static final String BRAND_ATTR = "brand";
    public static final String RETAILER_ATTR = "retailer";
    public static final String RETAILER_KEY_ATTR = "retailerProductKey";
    public static final String LONG_DESC_ATTR = "longDescription";
    public static final String TRACKING_URL_ATTR = "productTrackingURL";
    public static final String AFFILIATE_URL_ATTR = "affiliateAddToCartUrl";
    public static final String RATING_AVERAGE_ATTR = "ratingAverage";
    public static final String COLOR_ATTR = "color";
    public static final String SIZE_ATTR = "size";
    public static final String PART_NUMBER_ATTR = "partNumber";
    public static final String MODEL_ATTR = "model";
    public static final String FEATURES_ATTR = "features";
    public static final String TECHNICAL_INFO_ATTR ="technicalInfo";
    public static final String OTHER_INFO_ATTR = "otherInfo";
    public static final String IMAGE_URLS_ATTR = "externalImageURLs";
    public static final String RATING_COUNT_ATTR = "ratingCount";
    public static final String LEASIBLE_PRODUCT_TYPE = "leasableproduct";
    public static final String PRICE_CUSTOM_TYPE_KEY = "pl-price-custom-type";
    public static final String ORIGINAL_PRICE_FIELD = "originalPrice";
    public static final String DISCOUNT_PRICE_FIELD = "discountedPrice";
    public static final String CURRENCY_CODE = "USD";
}
