package com.progleasing.marketplace.etl.service;


import com.commercetools.importapi.client.ProjectApiRoot;
import com.commercetools.importapi.models.common.*;
import com.commercetools.importapi.models.customfields.*;
import com.commercetools.importapi.models.customfields.FieldContainerBuilder;
import com.commercetools.importapi.models.importcontainers.ImportContainer;
import com.commercetools.importapi.models.importcontainers.ImportContainerDraft;
import com.commercetools.importapi.models.importcontainers.ImportContainerDraftBuilder;
import com.commercetools.importapi.models.importcontainers.ImportContainerPagedResponse;
import com.commercetools.importapi.models.importrequests.ProductDraftImportRequest;
import com.commercetools.importapi.models.importrequests.ProductDraftImportRequestBuilder;
import com.commercetools.importapi.models.productdrafts.*;
import com.commercetools.importapi.models.productvariants.Attribute;
import com.commercetools.importapi.models.productvariants.AttributeBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.dto.ImageUrlsByProductId;
import com.progleasing.marketplace.etl.entity.AdditionalProductData;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.PriceData;
import com.progleasing.marketplace.etl.entity.ProductData;
import io.vrap.rmf.base.client.ApiHttpResponse;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.*;
import java.time.chrono.ChronoZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.progleasing.marketplace.etl.constants.PLConstants.*;

@Service
@Slf4j
public class ProductExporterService {


    private ProjectApiRoot importProjectApi;
    private ObjectMapper objectMapper;
    private ProductDataRepository productDataRepository;
    private PriceDataRepository priceDataRepository;
    private ImageDataRepository imageDataRepository;
    private AdditionalProductDataRepository additionalProductDataRepository;
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private ProductDeleteExportService productDeleteExportService;

    public ProductExporterService( @Qualifier("ctImportApiClient")ProjectApiRoot importProjectApi,
                                  ObjectMapper objectMapper,
                                  ProductDataRepository productDataRepository,
                                  PriceDataRepository priceDataRepository,
                                  EtlQueueStatusDataRepository etlQueueStatusDataRepository,
                                   ImageDataRepository imageDataRepository,
                                   AdditionalProductDataRepository additionalProductDataRepository,
                                   ProductDeleteExportService productDeleteExportService
                                   ) {
        this.importProjectApi = importProjectApi;
        this.objectMapper = objectMapper;
        this.productDataRepository = productDataRepository;
        this.priceDataRepository = priceDataRepository;
        this.etlQueueStatusDataRepository = etlQueueStatusDataRepository;
        this.imageDataRepository = imageDataRepository;
        this.additionalProductDataRepository = additionalProductDataRepository;
        this.productDeleteExportService = productDeleteExportService;
    }

    public void uploadProducts(String messagePayload, String messageId) throws Exception {
        ETLMessageDTO messageDTO = objectMapper.readValue(messagePayload, ETLMessageDTO.class);
        log.info("Processing Product Exporter for message  {}", messageDTO.getReferenceId());
        updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.STARTED);
        try{
            Timestamp start = Timestamp.valueOf(LocalDate.now().minusDays(1).atStartOfDay());
            Timestamp end = Timestamp.valueOf(LocalDate.now().atStartOfDay());
            int page = 0;
            Page<ProductData> productPage;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
            String timestamp = LocalDateTime.now().format(formatter);
            ImportContainerDraft importContainerDraft = ImportContainerDraftBuilder.of()
                    .key(IMPORT_CONTAINER_KEY + timestamp)
                    .resourceType(ImportResourceType.PRODUCT)
                    .build();
            ImportContainer importContainer = importProjectApi.importContainers()
                    .post(importContainerDraft)
                    .executeBlocking()
                    .getBody();
            if (importContainer == null) throw new RuntimeException("Failed to create import container");
            ExecutorService executor = Executors.newFixedThreadPool(10);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            log.info("Product query start {} end {} for message {}",start,end,messageDTO.getReferenceId());
            do {
                Pageable pageable = PageRequest.of(page, DB_BATCH_SIZE);
                productPage = productDataRepository.findByLastModified(start, end, pageable);
                List<ProductData> productsToExport = productPage.getContent();
                if (!productsToExport.isEmpty()) {
                    List<String> productIds = productsToExport.stream()
                            .map(ProductData::getId)
                            .collect(Collectors.toList());
                    List<PriceData> priceExport = priceDataRepository.findByIdIn(productIds);
                    List<AdditionalProductData> additionalProductData = additionalProductDataRepository.findAllById(productIds);
                    Map<String, List<String>> imageData = getImageUrlsGroupedByProductId(productIds);
                    Map<String, PriceData> priceMap = priceExport.stream()
                            .collect(Collectors.toMap(PriceData::getId, Function.identity()));
                    Map<String, AdditionalProductData> additionDetailsMap = additionalProductData.stream()
                            .collect(Collectors.toMap(AdditionalProductData::getId, Function.identity()));
                    uploadProductsToCommerceTools(productsToExport, priceMap, imageData,additionDetailsMap,
                            importContainer,executor,futures);
                    updateProduct(productsToExport, priceExport);
                }
                page++;
            } while (productPage.hasNext());
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            } catch (Exception ex) {
                log.error("Failed while waiting for import batch uploads", ex);
                throw ex;
            } finally {
                executor.shutdown();
            }
            log.info("Product export successful for products on {}",Timestamp.from(Instant.now()));
            updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.COMPLETED);

            log.info("starting product deletion which are marked for deletion. ");
            productDeleteExportService.deleteMarkedProducts(messageDTO.getReferenceId());
            //deleteDormantImportContainer();
        }catch (Exception e) {
            log.error("Product export failed for message ID {}: {}", messageId, e.getMessage(), e);
            updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING);
            throw e;
        }
    }

    private Map<String, List<String>> getImageUrlsGroupedByProductId(List<String> productIds) {
        List<ImageUrlsByProductId> results = imageDataRepository.findImageUrlsByProductIds(productIds);
        return results.stream()
                .collect(Collectors.groupingBy(
                        ImageUrlsByProductId::getId,
                        Collectors.mapping(ImageUrlsByProductId::getImageUrl, Collectors.toList())
                ));
    }


    private void uploadProductsToCommerceTools(List<ProductData> productsToExport, Map<String, PriceData> priceExport,
                                               Map<String, List<String>> imageMap, Map<String, AdditionalProductData> additionDetailsMap,
                                               ImportContainer importContainer,
                                               ExecutorService executor, List<CompletableFuture<Void>> futures) {
        List<ProductDraftImport> batch = new ArrayList<>();

        for (ProductData productData : productsToExport) {
            PriceData priceData = priceExport.get(productData.getId());
            if (priceData == null) {
                log.warn("No price found for product {}", productData.getId());
                continue;
            }
            AdditionalProductData additionalProductData = additionDetailsMap.get(productData.getId());
            List<String> productImages = imageMap.get(productData.getId());
            List<CategoryKeyReference> categoryReferences = Arrays.stream(productData.getCategoryKey().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(key -> CategoryKeyReferenceBuilder.of().key(key).build())
                    .collect(Collectors.toList());
            List<Attribute> attributes = generateAttributes(productData, productImages, additionalProductData);
            PriceDraftImport priceDraftImport = getPriceImportDraft(priceData,productData.getId());
            ProductDraftImport productDraftImport = ProductDraftImportBuilder.of()
                    .key(productData.getId())
                    .productType(ProductTypeKeyReferenceBuilder.of().key(LEASIBLE_PRODUCT_TYPE).build())
                    .name(LocalizedStringBuilder.of().addValue("en-US", productData.getName()).addValue("es",productData.getName()).build())
                    .description(LocalizedStringBuilder.of().addValue("en-US", productData.getDescription()).addValue("es",productData.getDescription()).build())
                    .slug(LocalizedStringBuilder.of().addValue("en-US", productData.getId()).addValue("es",productData.getId()).build())
                    .categories(categoryReferences)
                    .masterVariant(ProductVariantDraftImportBuilder.of()
                            .key(productData.getId())
                            .sku(productData.getId())
                            .prices(priceDraftImport)
                            .attributes(attributes)
                            .build())
                    .publish(true)
                    .build();
            batch.add(productDraftImport);
            if (batch.size() == IMPORT_BATCH_SIZE) {
                List<ProductDraftImport> finalBatch = new ArrayList<>(batch);
                futures.add(CompletableFuture.runAsync(() -> submitBatch(finalBatch, importContainer.getKey()), executor));
                batch.clear();
            }
        }
        if (!batch.isEmpty()) {
            List<ProductDraftImport> finalBatch = new ArrayList<>(batch);
            futures.add(CompletableFuture.runAsync(() -> submitBatch(finalBatch, importContainer.getKey()), executor));
        }
    }

    private static List<Attribute> generateAttributes(ProductData productData, List<String> productImages, AdditionalProductData additionalProductData) {
        List<Attribute> attributes = new ArrayList<>(Arrays.asList(
               AttributeBuilder.of().textBuilder().name(BRAND_ATTR).value(productData.getBrand()).build(),
                AttributeBuilder.of().textBuilder().name(RETAILER_ATTR).value(productData.getRetailerName()).build(),
                AttributeBuilder.of().textBuilder().name(RETAILER_KEY_ATTR).value(productData.getRetailerKey()).build(),
              AttributeBuilder.of().ltextBuilder().name(LONG_DESC_ATTR).value(
                      LocalizedString.builder().addValue("en-US", productData.getLongDescription()).addValue("es",productData.getLongDescription()).build()).build(),
              AttributeBuilder.of().textBuilder().name(TRACKING_URL_ATTR).value(productData.getProductTrackingUrl()).build(),
               AttributeBuilder.of().numberBuilder().name(RATING_AVERAGE_ATTR).value(productData.getRatingAverage().doubleValue()).build(),
                AttributeBuilder.of().numberBuilder().name(RATING_COUNT_ATTR).value((double) productData.getRatingCount()).build()
        ));
        if(StringUtils.isNotEmpty(productData.getAffiliateAddToCartUrl())) {
            attributes.add(AttributeBuilder.of().textBuilder().name(AFFILIATE_URL_ATTR).value(productData.getAffiliateAddToCartUrl()).build());
        }
        if(!CollectionUtils.isEmpty(productImages)){
            attributes.add(  AttributeBuilder.of().textBuilder().name(IMAGE_URLS_ATTR).value(String.join(",", productImages)).build());
        }
        if(Objects.nonNull(additionalProductData)) {
            if (StringUtils.isNotEmpty(additionalProductData.getColor())) {
                attributes.add(AttributeBuilder.of().textBuilder().name(COLOR_ATTR).value(additionalProductData.getColor()).build());
            }
            if (StringUtils.isNotEmpty(additionalProductData.getSize())) {
                attributes.add(AttributeBuilder.of().textBuilder().name(SIZE_ATTR).value(additionalProductData.getSize()).build());
            }
            if (StringUtils.isNotEmpty(additionalProductData.getItemPartNumber())) {
                attributes.add(AttributeBuilder.of().textBuilder().name(PART_NUMBER_ATTR)
                        .value(additionalProductData.getItemPartNumber()).build());
            }
            if (StringUtils.isNotEmpty(additionalProductData.getModel())) {
                attributes.add(AttributeBuilder.of().textBuilder().name(MODEL_ATTR)
                        .value(additionalProductData.getModel()).build());
            }
            if (StringUtils.isNotEmpty(additionalProductData.getFeatures())) {
                attributes.add(AttributeBuilder.of().ltextBuilder().name(FEATURES_ATTR).value(
                        LocalizedString.builder().addValue("en-US", additionalProductData.getFeatures()).addValue("es", additionalProductData.getFeatures()).build()).build());
            }
            if (StringUtils.isNotEmpty(additionalProductData.getTechnicalInfo())) {
                attributes.add(AttributeBuilder.of().ltextBuilder().name(TECHNICAL_INFO_ATTR).value(
                        LocalizedString.builder().addValue("en-US", additionalProductData.getTechnicalInfo()).addValue("es", additionalProductData.getTechnicalInfo()).build()).build());
            }
            if (StringUtils.isNotEmpty(additionalProductData.getOtherInfo())) {
                attributes.add(AttributeBuilder.of().ltextBuilder().name(OTHER_INFO_ATTR).value(
                        LocalizedString.builder().addValue("en-US", additionalProductData.getOtherInfo()).addValue("es", additionalProductData.getOtherInfo()).build()).build());
            }
        }

        return attributes;
    }

    private PriceDraftImport getPriceImportDraft(PriceData priceData, String productId) {
        BigDecimal price = priceData.getPrice();
        BigDecimal salePrice = Optional.ofNullable(priceData.getSalePrice()).orElse(BigDecimal.ZERO);

        boolean hasValidSalePrice = salePrice.compareTo(BigDecimal.ZERO) > 0;
        BigDecimal effectivePrice = hasValidSalePrice ? salePrice : price;

        Long effectiveCentAmount = effectivePrice.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP).longValue();
        PriceDraftImportBuilder priceBuilder = PriceDraftImportBuilder.of()
                .key(productId + "-price")
                .value(TypedMoneyBuilder.of().centPrecisionBuilder()
                        .centAmount(effectiveCentAmount)
                        .currencyCode(CURRENCY_CODE)
                        .build());
        boolean setDiscountField = price != null && salePrice.compareTo(BigDecimal.ZERO) > 0;
        if (setDiscountField) {
            Long priceCentAmount = price.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP).longValue();

            priceBuilder.custom(CustomBuilder.of()
                    .type(TypeKeyReferenceBuilder.of().key(PRICE_CUSTOM_TYPE_KEY).build())
                    .fields(FieldContainerBuilder.of()
                            .addValue(ORIGINAL_PRICE_FIELD,
                                    MoneyFieldBuilder.of()
                                            .value(TypedMoneyBuilder.of().centPrecisionBuilder()
                                                    .centAmount(priceCentAmount)
                                                    .currencyCode("USD")
                                                    .build())
                                            .build())
                            .build())
                    .build());
        }
        return priceBuilder.build();
    }


    private void submitBatch(List<ProductDraftImport> batch, String containerKey) {
        log.info("Submit batch for products {}",batch.size());
        ProductDraftImportRequest request = ProductDraftImportRequestBuilder.of()
                .resources(batch)
                .build();

        importProjectApi.productDrafts()
                .importContainers()
                .withImportContainerKeyValue(containerKey)
                .post(request)
                .executeBlocking();
    }

    @Transactional
    private void updateProduct(List<ProductData> productsToExport, List<PriceData> priceExport) {
        for(ProductData productData : productsToExport) {
            productData.setLastSentToCT(Timestamp.from(Instant.now()));
        }
        for(PriceData priceData: priceExport) {
            priceData.setLastSentToCT(Timestamp.from(Instant.now()));
        }
        productDataRepository.saveAll(productsToExport);
        priceDataRepository.saveAll(priceExport);
    }

    @Transactional
    private void updateStatusQueue(ETLMessageDTO messageDTO, String messageId, EtlQueueStatusData.Status status) {
        String messageData = String.format(
                "%s|%s|%s|%s|%s|%s|%s",
                messageId,
                messageDTO.getSearchTerm(),
                messageDTO.getCategoryKey(),
                messageDTO.getRetailerName(),
                messageDTO.getRetailerKey(),
                messageDTO.getMinPrice(),
                messageDTO.getMaxPrice()
        );
        EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_EXPORT;
        Optional<EtlQueueStatusData> existing =
                etlQueueStatusDataRepository.findByEtlIDAndSqsMessageDataAndEtlStep(
                        messageDTO.getReferenceId(),
                        messageData,
                        etlStep
                );
        EtlQueueStatusData etlQueueStatusData;
        if (existing.isPresent()) {
            etlQueueStatusData = existing.get();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        } else {
            etlQueueStatusData = new EtlQueueStatusData();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setEtlStep(etlStep);
            etlQueueStatusData.setEtlID(messageDTO.getReferenceId());
            etlQueueStatusData.setMessageId(messageId);
            etlQueueStatusData.setSqsMessageData(messageData);
            if (status == EtlQueueStatusData.Status.STARTED) {
                etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
            }
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        }
        etlQueueStatusDataRepository.save(etlQueueStatusData);
    }

    private void deleteDormantImportContainer() {
        ApiHttpResponse<ImportContainerPagedResponse> importContainerResponse =
                importProjectApi.importContainers().get().addLimit(100).executeBlocking();
        try {
            if (importContainerResponse.getBody() != null && importContainerResponse.getBody().getResults() != null) {
                List<ImportContainer> containers = importContainerResponse.getBody().getResults();
                ZonedDateTime sixtyDaysAgo = ZonedDateTime.now().minusDays(60);
                List<ImportContainer> recentContainers = containers.stream()
                        .filter(container -> container.getCreatedAt().isAfter(sixtyDaysAgo))
                        .collect(Collectors.toList());
                List<CompletableFuture<Void>> deletionFutures = recentContainers.stream()
                        .map(container -> CompletableFuture.runAsync(() -> {
                            try {
                                importProjectApi.importContainers()
                                        .withImportContainerKeyValue(container.getKey())
                                        .delete()
                                        .executeBlocking();
                                log.info("Deleted ImportContainer: {}", container.getKey());
                            } catch (Exception e) {
                                log.error("Failed to delete ImportContainer {}: {}", container.getKey(), e.getMessage(), e);
                            }
                        }))
                        .collect(Collectors.toList());
                CompletableFuture.allOf(deletionFutures.toArray(new CompletableFuture[0])).join();
            }
        } catch (Exception exc) {
            log.error("Deleting import containers", exc);
        }
    }

}
