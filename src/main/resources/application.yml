spring:
  application:
    name: marketplace-feeds-processor
  datasource:
    url: ${DATASOURCE_URL:*****************************************}
    username: ${DATASOURCE_USER_NAME:postgres}
    password: ${DATASOURCE_PASSWORD:admin123}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-east-1}

prog:
  etl:
    job:
      enabled: ${ETL_JOB_ENABLED:false}
    schedule: ${ETL_JOB_SCHEDULE:0 */45 * * * *}

cloud:
  aws:
    sqs:
      listener:
        auto-create-queue: false

aws:
  sovrn:
    sqs:
      concurrent-message: ${AWS_SOVRN_CONCURRENT_MESSAGE:5}
  sqs:
    sovrn:
      queue-name: ${SOVRN_QUEUE:sovrn-retailer-queue}
      listener:
        enabled: ${SOVRN_LISTENER_ENABLED:false}
    sovrn-products-load:
      queue-name: ${SOVRN_PRODUCTS_LOAD:sovrn_products_load_queue}
      listener:
        enabled: ${SOVRN_PRODUCTS_LOAD_LISTENER_ENABLED:true}
    product-importer:
      queue-name: ${PRODUCT_IMPORTER_QUEUE:product-importer-queue}
      listener:
        enabled: ${PRODUCT_IMPORTER_LISTENER_ENABLED:false}
    retailer-importer:
      listener:
        enabled: ${RETAILER_IMPORTER_LISTENER_ENABLED:false}
      queue-name: ${RETAILER_IMPORTER_QUEUE:retailer-importer-queue}
    product-exporter:
      listener:
        enabled: ${PRODUCT_EXPORTER_LISTENER_ENABLED:false}
      queue-name: ${PRODUCT_EXPORTER_QUEUE:product-exporter-queue}
    amazon:
      listener:
        enabled: ${AMAZON_PAP_LISTENER_ENABLED:false}
      queue-name: ${AMAZON_PAP_QUEUE:amazon-retailer-queue}
    impact:
      listener:
        enabled: ${IMPACT_LISTENER_ENABLED:false}
      queue-name: ${IMPACT_QUEUE:impact-retailer-queue}
    cj:
      listener:
        enabled: ${CJ_LISTENER_ENABLED:false}
      queue-name: ${CJ_QUEUE:cj-retailer-queue}
    serp:
      listener:
        enabled: ${SERP_API_LISTENER_ENABLED:false}
      queue-name: ${SERP_API_QUEUE:serp-retailer-queue}
    leasibility-exporter:
      listener:
        enabled: ${LEASIBILITY_EXPORT_QUEUE_LISTENER_ENABLED:false}
      queue-name: ${LEASIBILITY_EXPORT_QUEUE:leasibility-export-queue}
    leasibility-importer:
      listener:
        enabled: ${LEASIBILITY_IMPORT_QUEUE_LISTENER_ENABLED:false}
      queue-name: ${LEASIBILITY_IMPORT_QUEUE:leasibility-import-queue}
    polling-monitoring:
      queue-name: ${POLLING_MONITORING_QUEUE:polling-monitoring-queue}
      listener:
        enabled: ${POLLING_MONITORING_LISTENER_ENABLED:false}
      data-retention-days: ${DATA-RETENTION_DAYS:30}
      long-running-threshold: ${LONG-RUNNING-THRESHOLD:900000}
    concurrent-message: ${AMAZON_CONCURRENT_MESSAGE:10}


sovrn:
  etl:
    maxProductsToImport: ${AWS_ETL_PRODUCT_LIMIT:50}
    gracePeriodDays: ${DELETION_GRACE_PERIOD_DAYS:5}
    deleteProducts: ${DELETE_PRODUCTS:false}
  database:
    bulk-copy:
      enabled: ${SOVRN_BULK_COPY_ENABLED:true}  # Set to false to use old row-by-row processing

impact:
  etl:
    host: ${IMPACT_ETL_HOST:https://api.impact.com}
    userName: ${IMPACT_ETL_USERNAME}
    password: ${IMPACT_ETL_PASSWORD}
    accountSid: ${IMPACT_ETL_ACCOUNT_SID}
    maxProductsToImport: ${AWS_ETL_PRODUCT_LIMIT:50}
    gracePeriodDays: ${DELETION_GRACE_PERIOD_DAYS:5}
    deleteProducts: ${DELETE_PRODUCTS:false}

amazon:
  etl:
    host: ${AWS_ETL_AMAZON_PAP_HOST:webservices.amazon.com}
    region: ${AWS_AMAZON_PAP_REGION:us-east-1}
    accessKey: ${AWS_ETL_AMAZON_PAP_ACCESS_KEY}
    secretKey: ${AWS_ETL_AMAZON_PAP_SECRET_KEY}
    partnerTag: ${AWS_ETL_AMAZON_PAP_PARTNER_TAG}
    maxProductsToImport: ${AWS_ETL_PRODUCT_LIMIT:5}
    gracePeriodDays: ${DELETION_GRACE_PERIOD_DAYS:5}
    deleteProducts: ${DELETE_PRODUCTS:false}

cj:
  etl:
    host: ${CJ_ETL_HOST}
    company-id: ${CJ_COMPANY_ID}
    promotional-id: ${CJ_PROMOTIONAL_ID}
    auth-token: ${CJ_QUERY_TOKEN}
    search:
      advertiser-countries: ${CJ_ADVERTISER_COUNTRIES:US}
      limit: ${CJ_SEARCH_LIMIT:1000}
      include-deleted-products: ${CJ_INCLUDE_DELETED_PRODUCTS:false}
      currency: ${CJ_CURRENCY:USD}
    maxProductsToImport: ${AWS_ETL_PRODUCT_LIMIT:50}
    gracePeriodDays: ${DELETION_GRACE_PERIOD_DAYS:5}
    deleteProducts: ${DELETE_PRODUCTS:false}

serp:
  etl:
    base-url: ${SERP_API_BASE_URL:https://serpapi.com/search.json}
    api-key: ${SERP_API_KEY}
    engine: ${SERP_API_ENGINE:google_shopping}
    hl: ${SERP_API_HL:en}
    gl: ${SERP_API_GL:us}
    tbm: ${SERP_API_TBM:shop}
    direct-link: ${SERP_API_DIRECT_LINK:true}
    location: ${SERP_API_LOCATION:United States}
    google_domain: ${SERP_API_GOOGLE_DOMAIN:google.com}
    maxResults: ${AWS_ETL_PRODUCT_LIMIT:20}
    gracePeriodDays: ${DELETION_GRACE_PERIOD_DAYS:5}
    deleteProducts: ${DELETE_PRODUCTS:false}

market:
  place:
    commercetools:
      projectKey: ${PROJECT_KEY:pl-test}
      clientId: ${CLIENT_ID:RmRUHXdC4T8Szii1m-iAdCJx}
      clientSecret: ${CLIENT_SECRET:JhjvEjXAG3JxLmXCUPcXRaZiY1sN6YKY}
      apiUrl: ${API_URL:https://api.us-central1.gcp.commercetools.com}
      authUrl: ${AUTH_URL:https://auth.us-central1.gcp.commercetools.com}
      importApiUrl: ${IMPORT_API_URL:https://import.us-central1.gcp.commercetools.com}
      scopes: ${SCOPES:manage_project:pl-test}

    retailer:
      productType:
        key: ${RETAILER_PRODUCT_KEY:retailerproduct}
      thread:
        count: ${THREAD_COUNT:5}
      grace:
        days: ${GRACE_DAYS:5}
      path:
        prefix: ${RETAILER_PATH:retailer_import/inbound/}
        archive: ${RETAILER_ARCHIVE:retailer_import/archive/}
        images: ${RETAILER_Images:retailer_import/images/}

product:
  etl:
    grace:
      days: ${GRACE_DAYS:5}

  leasibility:
    export:
      s3:
        bucket: ${LEASIBILITY_S3_BUCKET_NAME:asdfdf}
        folder: ${LEASIBILITY_S3_FOLDER_NAME:leasability_service}
      max:
        records: ${LEASIBILITY_MAX_RECORDS:10000}
        parallelism: ${LEASIBILITY_MAX_THREADS:8}

    import:
      s3:
        bucket: ${LEASIBILITY_S3_BUCKET_NAME:asdfd}
        folder: ${LEASIBILITY_IMPORT_ARCHIVE_FOLDER:leasability_service_archive}
      s3-prefix: ${LEASIBILITY_S3_IMPORT_PREFIX:leasability_service_D2C/}
      db-chunk:
        size: ${LEASIBILITY_IMPORT_CHUNK_SIZE:500}

  sovrn-products-load:
    s3:
      bucket: ${SOVRN_PRODUCTS_LOAD_S3_BUCKET_NAME:feeds.shared.monetizer101.com}
      folder: ${SOVRN_PRODUCTS_LOAD_S3_FOLDER_NAME:feed_exports/ea3e5537553c6cf739af818331fa97f2/}
      access-key: ${SOVRN_ACCESS_KEY:********************}
      secret-key: ${SOVRN_SECRET_KEY:xKyBBpQOaTQ1o/VLqT3vo0WXk196Tzsl52llsvjp}
      s3-uri: ${SOVRN_S3_URI:s3://feeds.shared.monetizer101.com/feed_exports/ea3e5537553c6cf739af818331fa97f2/}
      region: ${SOVRN_REGION:us-east-1}
      allowed-folder-ids:
        wayfair: ${SOVRN_WAYFAIR_FOLDER_IDS:48541}
        homedepot: ${SOVRN_HOMEDEPOT_FOLDER_IDS:10334}
        lowes: ${SOVRN_LOWES_FOLDER_IDS:25447}
        walmart: ${SOVRN_WALMART_FOLDER_IDS:11946}
        target: ${SOVRN_TARGET_FOLDER_IDS:10238}
        dickssportinggoods: ${SOVRN_DICKSSPORTINGGOODS_FOLDER_IDS:112075}
        westelm: ${SOVRN_WESTELM_FOLDER_IDS:38693}
        tractorsupply: ${SOVRN_TRACTORSUPPLY_FOLDER_IDS:19274}
        adorama: ${SOVRN_ADORAMA_FOLDER_IDS:11359}
        bestbuy: ${SOVRN_BESTBUY_FOLDER_IDS:39734}
        williamssonoma: ${SOVRN_WILLIAMSSONOMA_FOLDER_IDS:38699}
        golfgalaxy: ${SOVRN_GOLFGALAXY_FOLDER_IDS:82062}
        tirerack: ${SOVRN_TIRERACK_FOLDER_IDS:46739}
        ashley: ${SOVRN_ASHLEY_FOLDER_IDS:44337}
        gamestop: ${SOVRN_GAMESTOP_FOLDER_IDS:43939}
        costplusworldmarket: ${SOVRN_COSTPLUSWORLDMARKET_FOLDER_IDS:10203}
        surlatable: ${SOVRN_SURLATABLE_FOLDER_IDS:89909}
        dellhomehomeoffice: ${SOVRN_DELLHOMEHOMEOFFICE_FOLDER_IDS:814}
        dysoninc: ${SOVRN_DYSONINC_FOLDER_IDS:11127}
        hp: ${SOVRN_HP_FOLDER_IDS:106183}
        mattressfirm: ${SOVRN_MATTRESSFIRM_FOLDER_IDS:47236}
      allowed-file-ids:
        walmart: ${SOVRN_WALMART_FILE_IDS:36616,36703,36669,36849,36704,36859,36631,36636}
        target: ${SOVRN_TARGET_FILE_IDS:11551}
  pl:
    s3:
      bucket: ${PL_SOVRN_S3_BUCKET_NAME:pl-2t-mrktplace-sss-marketplace-data-02}
      folder: ${PL_SOVRN_S3_FOLDER_NAME:sovrn_feed_exports/}
      temp-folder: ${PL_SOVRN_S3_TEMP_FOLDER:temp_transformed/}
      # Connection pool and timeout settings for large file processing (7GB files)
      connection:
        max-connections: ${PL_MAX_CONNECTIONS:100}
        connection-timeout-seconds: ${PL_CONNECTION_TIMEOUT:60}
        socket-timeout-minutes: ${PL_SOCKET_TIMEOUT:10}
        api-call-timeout-minutes: ${PL_API_CALL_TIMEOUT:30}
        retry-attempts: ${PL_RETRY_ATTEMPTS:3}