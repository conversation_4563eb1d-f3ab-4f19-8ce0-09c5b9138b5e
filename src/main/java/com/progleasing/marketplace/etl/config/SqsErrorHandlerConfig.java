package com.progleasing.marketplace.etl.config;

import io.awspring.cloud.sqs.listener.errorhandler.ErrorHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import software.amazon.awssdk.core.exception.SdkClientException;

@Configuration
@Slf4j
public class SqsErrorHandlerConfig {

    @Bean
    public ErrorHandler<String> sqsErrorHandler() {
        return new ErrorHandler<>() {
            @Override
            public void handle(Message<String> message, Throwable exception) {
                Throwable root = unwrap(exception);
                if (root instanceof SdkClientException) {
                    log.warn("Transient SQS I/O error – will retry: {}", root.getMessage());
                    return;
                }
                log.error("ETL failed, letting SQS requeue. Message‑ID={}, payload‑len={}",
                        message.getHeaders().getId(), message.getPayload().length(), root);
                if (root instanceof RuntimeException) {
                    throw (RuntimeException) root;
                }
                throw new RuntimeException(root);
            }
        };
    }

    private Throwable unwrap(Throwable t) {
        Throwable cur = t;
        while (cur.getCause() != null && cur.getCause() != cur) {
            cur = cur.getCause();
        }
        return cur;
    }
}
