package com.progleasing.marketplace.etl.config;


import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.defaultconfig.ApiRootBuilder;
import com.commercetools.importapi.defaultconfig.ImportApiRootBuilder;
import io.vrap.rmf.base.client.oauth2.ClientCredentials;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.sqs.SqsClient;

@Configuration
public class ServiceConfiguration {

    private final CommerceToolsConfig config;
    private static final String CLIENT_CREDENTIALS_AUTH_TOKEN_URI = "/oauth/token";

    @Value("${spring.cloud.aws.region.static}")
    private String awsRegion;

    public ServiceConfiguration(CommerceToolsConfig config) {
        this.config = config;
    }


    @Bean(name = "ctApiClient")
    public ProjectApiRoot ctApiClient() {
        return ApiRootBuilder.of()
                .defaultClient(
                        config.getApiUrl(),
                        ClientCredentials.of()
                                .withClientId(config.getClientId())
                                .withClientSecret(config.getClientSecret())
                                .withScopes(config.getScopes())
                                .build(),
                        config.getAuthUrl() + CLIENT_CREDENTIALS_AUTH_TOKEN_URI
                )
                .build(config.getProjectKey());
    }


    @Primary
    @Bean(name = "ctImportApiClient")
    public com.commercetools.importapi.client.ProjectApiRoot ctImportApiClient() {
        return ImportApiRootBuilder.of()
                .defaultClient(
                        config.getImportApiUrl(),
                        ClientCredentials.of()
                                .withClientId(config.getClientId())
                                .withClientSecret(config.getClientSecret())
                                .withScopes(config.getScopes())
                                .build(),
                        config.getAuthUrl() + CLIENT_CREDENTIALS_AUTH_TOKEN_URI
                )
                .build(config.getProjectKey());
    }

    @Primary
    @Bean
    public S3Client s3Client() {
        return S3Client.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .serviceConfiguration(S3Configuration.builder()
                        .pathStyleAccessEnabled(false)
                        .chunkedEncodingEnabled(true)
                        .build())
                .build();
    }

    @Bean
    public SqsClient sqsClient() {
        return SqsClient.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .build();
    }
}