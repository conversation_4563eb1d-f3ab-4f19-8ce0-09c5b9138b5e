package com.progleasing.marketplace.etl.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;

import static software.amazon.awssdk.profiles.ProfileProperty.REGION;

@Configuration
public class SovrnConfiguration {

    @Value("${product.sovrn-products-load.s3.region}")
    private String awsRegion;

    @Value("${product.sovrn-products-load.s3.access-key}")
    private String sovrnAccessKey;

    @Value("${product.sovrn-products-load.s3.secret-key}")
    private String sovrnSecretKey;

    @Bean(name = "sovrnS3Client")
    public S3Client sovrnS3Client() {
        return S3Client.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(
                        StaticCredentialsProvider.create(
                                AwsBasicCredentials.create(sovrnAccessKey, sovrnSecretKey)))
                .serviceConfiguration(S3Configuration.builder()
                        .pathStyleAccessEnabled(false)
                        .chunkedEncodingEnabled(true)
                        .build())
                .build();
    }
}