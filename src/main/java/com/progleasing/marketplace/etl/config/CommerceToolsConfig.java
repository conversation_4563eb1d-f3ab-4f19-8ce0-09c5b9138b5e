package com.progleasing.marketplace.etl.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties("market.place.commercetools")
public class CommerceToolsConfig {
    private String projectKey;
    private String clientId;
    private String clientSecret;
    private String authUrl;
    private String apiUrl;
    private String importApiUrl;
    private String scopes;

    public String getScopes() {
        return String.format("%s:%s", scopes, getProjectKey());
    }

}
