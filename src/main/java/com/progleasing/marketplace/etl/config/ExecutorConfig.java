package com.progleasing.marketplace.etl.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
public class ExecutorConfig {

    @Value("${aws.sqs.concurrent-message:10}")
    private int defaultConcurrency;

    @Value("${aws.sovrn.sqs.concurrent-message:5}")
    private int defaultSovrnConcurrency;

    @Bean(name = "googleSerpTaskExecutor")
    public Executor googleSerpTaskExecutor() {
        return buildExecutor("google-serp-", defaultConcurrency);
    }

    @Bean(name = "amazonPapTaskExecutor")
    public Executor amazonFeedTaskExecutor() {
        return buildExecutor("amazon-pap-", defaultConcurrency);
    }

    @Bean(name = "impactTaskExecutor")
    public Executor impactFeedTaskExecutor() {
        return buildExecutor("impact-", defaultConcurrency);
    }

    @Bean(name = "cjTaskExecutor")
    public Executor cjFeedTaskExecutor() {
        return buildExecutor("cj-", defaultConcurrency);
    }

    @Bean(name = "sovrnTaskExecutor")
    public Executor sovrnTaskExecutor() {
        return buildExecutor("sovrn-", defaultSovrnConcurrency);
    }

    @Bean(name = "etlTaskExecutor")
    public Executor etlFeedTaskExecutor() {
        return buildExecutor("etl-", defaultConcurrency);
    }

    @Bean(name = "pollingTaskExecutor")
    public Executor pollingFeedTaskExecutor() {
        return buildExecutor("polling-", defaultConcurrency);
    }

    private Executor buildExecutor(String prefix, int poolSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(poolSize);
        executor.setMaxPoolSize(poolSize);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix(prefix);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }
}
