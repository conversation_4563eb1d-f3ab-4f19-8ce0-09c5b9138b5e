package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.*;
import com.commercetools.api.models.category.CategoryReference;
import com.commercetools.api.models.common.LocalizedString;
import com.commercetools.api.models.product.*;
import com.commercetools.api.models.product_type.ProductTypeReference;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.repositories.ProductDataRepository;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ProductDeletionServiceTest {

    @Mock
    private ProjectApiRoot commercetoolsClient;

    @Mock
    private ByProjectKeyProductsRequestBuilder productsBuilder;

    @Mock
    private ByProjectKeyProductsByIDRequestBuilder byIdBuilder;

    @Mock
    private ByProjectKeyProductsByIDDelete byIdDelete;

    @Mock
    private ByProjectKeyProductsByIDPost post;

    @Mock
    private ApiHttpResponse<Product> apiResponse;

    @Mock
    private ByProjectKeyProductsByIDPost postRequest;

    @Mock
    private ProductDataRepository productDataRepository;

    @Mock
    EtlQueueStatusData statusData;

    @InjectMocks
    private ProductDeletionService productDeletionService;

    private static final String MARKED_FOR_DELETION = "markedForDeletion";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(productDeletionService, "threadCount", 2);
        ReflectionTestUtils.setField(productDeletionService, "gracePeriodDays", 30);

        when(commercetoolsClient.products()).thenReturn(productsBuilder);
        when(productsBuilder.withId(anyString())).thenReturn(byIdBuilder);
        when(byIdBuilder.post(any(ProductUpdate.class))).thenReturn(post);
        when(byIdBuilder.delete()).thenReturn(byIdDelete);
        when(byIdDelete.withVersion(anyLong())).thenReturn(byIdDelete);
        when(post.executeBlocking()).thenReturn(apiResponse);
        when(byIdDelete.executeBlocking()).thenReturn(mock(ApiHttpResponse.class));
        when(apiResponse.getBody()).thenReturn(mock(Product.class));
    }

    @Test
    void testProcessMissingProduct_withMarkedForDeletionWithinGrace() {
        ProductProjection retailerProduct = mock(ProductProjection.class);
        ProductVariant variant = mock(ProductVariant.class);
        Attribute attribute = mock(Attribute.class);

        when(retailerProduct.getKey()).thenReturn("retailerKey");
        when(retailerProduct.getMasterVariant()).thenReturn(variant);
        when(retailerProduct.getVersion()).thenReturn(1L);
        when(retailerProduct.getId()).thenReturn("r1");
        when(retailerProduct.getPublished()).thenReturn(true);

        when(attribute.getName()).thenReturn(MARKED_FOR_DELETION);
        when(attribute.getValue()).thenReturn("\"" + LocalDate.now().minusDays(10) + "\"");

        when(variant.getAttributes()).thenReturn(List.of(attribute));

        productDeletionService.processMissingProduct(retailerProduct, new ArrayList<>(),statusData);

        verify(byIdBuilder, never()).delete();
    }

    @Test
    void testProcessMissingProduct_withMarkedForDeletionBeyondGrace_deletes() {
        ReflectionTestUtils.setField(productDeletionService, "gracePeriodDays", 5);

        ProductProjection retailerProduct = mock(ProductProjection.class);
        ProductVariant variant = mock(ProductVariant.class);
        Attribute attribute = mock(Attribute.class);

        when(retailerProduct.getKey()).thenReturn("retailerKey");
        when(retailerProduct.getMasterVariant()).thenReturn(variant);
        when(retailerProduct.getVersion()).thenReturn(1L);
        when(retailerProduct.getId()).thenReturn("r1");
        when(retailerProduct.getPublished()).thenReturn(true);

        when(attribute.getName()).thenReturn(MARKED_FOR_DELETION);
        when(attribute.getValue()).thenReturn("\"" + LocalDate.now().minusDays(10) + "\"");

        when(variant.getAttributes()).thenReturn(List.of(attribute));

        productDeletionService.processMissingProduct(retailerProduct, new ArrayList<>(),statusData);

        verify(byIdBuilder).delete();
    }

    @Test
    void testProcessMissingProduct_noMarkedAttribute_triggersMarkAndUnpublish() {
        ProductProjection retailerProduct = mock(ProductProjection.class);
        ProductVariant variant = mock(ProductVariant.class);

        when(retailerProduct.getKey()).thenReturn("retailerKey");
        when(retailerProduct.getMasterVariant()).thenReturn(variant);
        when(retailerProduct.getVersion()).thenReturn(2L);
        when(retailerProduct.getId()).thenReturn("pid");
        when(variant.getId()).thenReturn(1L);
        when(variant.getAttributes()).thenReturn(new ArrayList<>());

        Product mockUpdatedProduct = mock(Product.class);
        when(apiResponse.getBody()).thenReturn(mockUpdatedProduct);
        when(mockUpdatedProduct.getId()).thenReturn("pid");
        when(mockUpdatedProduct.getVersion()).thenReturn(3L);

        productDeletionService.processMissingProduct(retailerProduct, new ArrayList<>(),statusData);

        verify(byIdBuilder).post(any(ProductUpdate.class));
    }

    @Test
    void testHandleInitialMarkForDeletion_gracePeriodZero_deletesImmediately() {
        ReflectionTestUtils.setField(productDeletionService, "gracePeriodDays", 0);

        ProductProjection retailerProduct = mock(ProductProjection.class);
        ProductVariant variant = mock(ProductVariant.class);

        when(retailerProduct.getId()).thenReturn("productId");
        when(retailerProduct.getVersion()).thenReturn(2L);
        when(retailerProduct.getKey()).thenReturn("retailerKey");
        when(retailerProduct.getMasterVariant()).thenReturn(variant);
        when(variant.getId()).thenReturn(1L);
        when(variant.getAttributes()).thenReturn(new ArrayList<>());

        Product mockUpdatedProduct = mock(Product.class);
        when(mockUpdatedProduct.getId()).thenReturn("productId");
        when(mockUpdatedProduct.getVersion()).thenReturn(3L);
        when(apiResponse.getBody()).thenReturn(mockUpdatedProduct);

        ReflectionTestUtils.invokeMethod(productDeletionService, "handleInitialMarkForDeletion", retailerProduct, new ArrayList<>(),statusData);

        verify(byIdBuilder).delete();
    }

    @Test
    void testUnpublishProduct_whenPublished_unpublishesProduct() throws Exception {
        ProductProjection productProjection = mock(ProductProjection.class);
        when(productProjection.getId()).thenReturn("prodId");
        when(productProjection.getVersion()).thenReturn(1L);
        when(productProjection.getKey()).thenReturn("prodKey");
        when(productProjection.getPublished()).thenReturn(true);

        // New: mock masterVariant with empty attributes
        ProductVariant mockVariant = mock(ProductVariant.class);
        when(mockVariant.getAttributes()).thenReturn(Collections.emptyList());
        when(productProjection.getMasterVariant()).thenReturn(mockVariant);

        Product updatedProduct = mock(Product.class);

        ByProjectKeyProductsRequestBuilder productsRequestBuilder = mock(ByProjectKeyProductsRequestBuilder.class);
        ByProjectKeyProductsByIDRequestBuilder byIdRequestBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDPost postRequest = mock(ByProjectKeyProductsByIDPost.class);
        ApiHttpResponse<Product> apiResponse = mock(ApiHttpResponse.class);

        when(commercetoolsClient.products()).thenReturn(productsRequestBuilder);
        when(productsRequestBuilder.withId("prodId")).thenReturn(byIdRequestBuilder);
        when(byIdRequestBuilder.post(any(ProductUpdate.class))).thenReturn(postRequest);
        when(postRequest.executeBlocking()).thenReturn(apiResponse);
        when(apiResponse.getBody()).thenReturn(updatedProduct);

        Product result = (Product) ReflectionTestUtils.invokeMethod(productDeletionService, "unpublishProduct", productProjection,statusData);

        assert result != null;
        verify(byIdRequestBuilder, times(1)).post(any(ProductUpdate.class));
    }

    @Test
    void testUnpublishProduct_whenAlreadyUnpublished_returnsNull() throws Exception {
        ProductProjection productProjection = mock(ProductProjection.class);
        when(productProjection.getPublished()).thenReturn(false);
        when(productProjection.getKey()).thenReturn("prodKey");

        Product result = (Product) ReflectionTestUtils.invokeMethod(productDeletionService, "unpublishProduct", productProjection,statusData);

        assert result == null;
        verify(commercetoolsClient, never()).products();
    }

    @Test
    void testDeleteProduct_deletesMainProduct() throws Exception {
        ProductProjection mainProduct = mock(ProductProjection.class);
        when(mainProduct.getId()).thenReturn("mainId");
        when(mainProduct.getVersion()).thenReturn(2L);

        ProductProjection assoc1 = mock(ProductProjection.class);
        when(assoc1.getId()).thenReturn("assoc1");
        when(assoc1.getVersion()).thenReturn(1L);

        ProductProjection assoc2 = mock(ProductProjection.class);
        when(assoc2.getId()).thenReturn("assoc2");
        when(assoc2.getVersion()).thenReturn(1L);

        ByProjectKeyProductsByIDRequestBuilder mainByIdBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDDelete mainDelete = mock(ByProjectKeyProductsByIDDelete.class);
        when(commercetoolsClient.products().withId("mainId")).thenReturn(mainByIdBuilder);
        when(mainByIdBuilder.delete()).thenReturn(mainDelete);
        when(mainDelete.withVersion(2L)).thenReturn(mainDelete);
        when(mainDelete.executeBlocking()).thenReturn(mock(ApiHttpResponse.class));

        ByProjectKeyProductsByIDRequestBuilder assoc1ByIdBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDDelete assoc1Delete = mock(ByProjectKeyProductsByIDDelete.class);
        when(commercetoolsClient.products().withId("assoc1")).thenReturn(assoc1ByIdBuilder);
        when(assoc1ByIdBuilder.delete()).thenReturn(assoc1Delete);
        when(assoc1Delete.withVersion(1L)).thenReturn(assoc1Delete);
        when(assoc1Delete.executeBlocking()).thenReturn(mock(ApiHttpResponse.class));

        ByProjectKeyProductsByIDRequestBuilder assoc2ByIdBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDDelete assoc2Delete = mock(ByProjectKeyProductsByIDDelete.class);
        when(commercetoolsClient.products().withId("assoc2")).thenReturn(assoc2ByIdBuilder);
        when(assoc2ByIdBuilder.delete()).thenReturn(assoc2Delete);
        when(assoc2Delete.withVersion(1L)).thenReturn(assoc2Delete);
        when(assoc2Delete.executeBlocking()).thenReturn(mock(ApiHttpResponse.class));

        ProductDeletionService spyService = Mockito.spy(productDeletionService);

        ReflectionTestUtils.invokeMethod(spyService, "deleteProduct", mainProduct,statusData);

        verify(mainDelete, times(1)).executeBlocking();
    }

    @Test
    void testRepublishAssociatedProductsInParallel() throws Exception {
        String retailerKey = "retailer123";

        LocalDate today = LocalDate.now();

        Attribute markedForDeletionAttr = Attribute.builder()
                .name(MARKED_FOR_DELETION)
                .value(today)
                .build();

        ProductVariant variantWithAttr = ProductVariantBuilder.of()
                .id(1L)
                .attributes(markedForDeletionAttr)
                .build();

        ByProjectKeyProductProjectionsRequestBuilder productProjectionsRequestBuilder = mock(ByProjectKeyProductProjectionsRequestBuilder.class);
        ByProjectKeyProductProjectionsSearchRequestBuilder searchRequestBuilder = mock(ByProjectKeyProductProjectionsSearchRequestBuilder.class);
        ByProjectKeyProductProjectionsSearchGet getSearch = mock(ByProjectKeyProductProjectionsSearchGet.class);
        ApiHttpResponse<ProductProjectionPagedSearchResponse> apiResponse1 = mock(ApiHttpResponse.class);
        ApiHttpResponse<ProductProjectionPagedSearchResponse> apiResponse2 = mock(ApiHttpResponse.class);

        ProductProjectionPagedSearchResponse page1Response = mock(ProductProjectionPagedSearchResponse.class);
        when(page1Response.getResults()).thenReturn(List.of(
                ProductProjectionBuilder.of().masterVariant(variantWithAttr).variants(ProductVariant.of()).name(LocalizedString.ofEnglish("dummy")).slug(LocalizedString.ofEnglish("d")).categories(CategoryReference.of()).id("unpub1").key("key1").createdAt(ZonedDateTime.now()).productType(ProductTypeReference.of()).lastModifiedAt(ZonedDateTime.now()).description(LocalizedString.ofEnglish("description")).version(1L).published(false).build(),
                ProductProjectionBuilder.of().masterVariant(variantWithAttr).variants(ProductVariant.of()).name(LocalizedString.ofEnglish("dummy")).slug(LocalizedString.ofEnglish("d")).categories(CategoryReference.of()).id("pub1").key("key2").createdAt(ZonedDateTime.now()).productType(ProductTypeReference.of()).lastModifiedAt(ZonedDateTime.now()).description(LocalizedString.ofEnglish("description")).version(2L).published(true).build(),
                ProductProjectionBuilder.of().masterVariant(variantWithAttr).variants(ProductVariant.of()).name(LocalizedString.ofEnglish("dummy")).slug(LocalizedString.ofEnglish("d")).categories(CategoryReference.of()).id("unpub2").key("key3").createdAt(ZonedDateTime.now()).productType(ProductTypeReference.of()).lastModifiedAt(ZonedDateTime.now()).description(LocalizedString.ofEnglish("description")).version(3L).published(false).build()
        ));
        when(page1Response.getTotal()).thenReturn(3L);
        when(page1Response.getCount()).thenReturn(3L);
        when(page1Response.getLimit()).thenReturn(500L);
        when(page1Response.getOffset()).thenReturn(0L);

        ProductProjectionPagedSearchResponse page2Response = mock(ProductProjectionPagedSearchResponse.class);
        when(page2Response.getResults()).thenReturn(Collections.emptyList());
        when(page2Response.getTotal()).thenReturn(3L);
        when(page2Response.getCount()).thenReturn(0L);
        when(page2Response.getLimit()).thenReturn(500L);
        when(page2Response.getOffset()).thenReturn(500L);

        when(commercetoolsClient.productProjections()).thenReturn(productProjectionsRequestBuilder);
        when(productProjectionsRequestBuilder.search()).thenReturn(searchRequestBuilder);
        when(searchRequestBuilder.get()).thenReturn(getSearch);
        when(getSearch.addQueryParam(anyString(), any())).thenReturn(getSearch);
        when(getSearch.withStaged(anyBoolean())).thenReturn(getSearch);
        when(getSearch.withLimit(anyInt())).thenReturn(getSearch);
        when(getSearch.withOffset(anyInt())).thenReturn(getSearch);

        when(getSearch.executeBlocking()).thenReturn(apiResponse1).thenReturn(apiResponse2);
        when(apiResponse1.getBody()).thenReturn(page1Response);
        when(apiResponse2.getBody()).thenReturn(page2Response);

        ByProjectKeyProductsRequestBuilder productsBuilder = mock(ByProjectKeyProductsRequestBuilder.class);
        ByProjectKeyProductsByIDRequestBuilder byIdBuilder1 = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDPost post1 = mock(ByProjectKeyProductsByIDPost.class);
        ApiHttpResponse<Product> postResponse1 = mock(ApiHttpResponse.class);
        Product updatedProduct1 = mock(Product.class);

        ByProjectKeyProductsByIDRequestBuilder byIdBuilder3 = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDPost post3 = mock(ByProjectKeyProductsByIDPost.class);
        ApiHttpResponse<Product> postResponse3 = mock(ApiHttpResponse.class);
        Product updatedProduct3 = mock(Product.class);

        when(commercetoolsClient.products()).thenReturn(productsBuilder);

        when(productsBuilder.withId("unpub1")).thenReturn(byIdBuilder1);
        when(byIdBuilder1.post(any(ProductUpdate.class))).thenReturn(post1);
        when(post1.executeBlocking()).thenReturn(postResponse1);
        when(postResponse1.getBody()).thenReturn(updatedProduct1);

        when(productsBuilder.withId("unpub2")).thenReturn(byIdBuilder3);
        when(byIdBuilder3.post(any(ProductUpdate.class))).thenReturn(post3);
        when(post3.executeBlocking()).thenReturn(postResponse3);
        when(postResponse3.getBody()).thenReturn(updatedProduct3);

        ReflectionTestUtils.invokeMethod(productDeletionService, "republishAssociatedProductsInParallel", retailerKey);

        verify(getSearch).addQueryParam(eq("filter.query"), contains(retailerKey));
        verify(getSearch, atLeastOnce()).withLimit(500);
        verify(getSearch, atLeastOnce()).withOffset(anyInt());
        verify(getSearch, times(1)).executeBlocking();
        verify(productsBuilder).withId("unpub1");
        verify(productsBuilder).withId("unpub2");

        verify(byIdBuilder1).post(any(ProductUpdate.class));
        verify(post1).executeBlocking();

        verify(byIdBuilder3).post(any(ProductUpdate.class));
        verify(post3).executeBlocking();
        verify(productsBuilder, never()).withId("pub1");
    }

    @Test
    void testUnpublishAssociatedProducts_emptyList() throws Exception {
        List<ProductProjection> associatedProducts = new ArrayList<>();

        List<Product> updatedProducts = ReflectionTestUtils.invokeMethod(productDeletionService,
                "unpublishAssociatedProducts", associatedProducts,statusData);

        verify(postRequest, never()).executeBlocking();
        assert updatedProducts.isEmpty();
    }

    @Test
    void testUnpublishAssociatedProducts_failureInUnpublish() throws Exception {
        List<ProductProjection> associatedProducts = Arrays.asList(
                createProductProjection("product1", 1L)
        );

        when(postRequest.executeBlocking()).thenThrow(new RuntimeException("Unpublish failed"));

        List<Product> updatedProducts = ReflectionTestUtils.invokeMethod(productDeletionService,
                "unpublishAssociatedProducts", associatedProducts,statusData);

        assert updatedProducts.isEmpty();
    }

    @Test
    void testProcessMissingProduct_withException() {
        ProductProjection retailerProduct = mock(ProductProjection.class);
        when(retailerProduct.getKey()).thenReturn("retailerKey");
        when(retailerProduct.getMasterVariant()).thenThrow(new RuntimeException("Error fetching variant"));

        productDeletionService.processMissingProduct(retailerProduct, new ArrayList<>(),statusData);

        verify(byIdBuilder, never()).delete();
    }

    @Test
    void testProcessMissingProduct_withMalformedDate_doesNotDelete() {
        ProductProjection retailerProduct = mock(ProductProjection.class);
        ProductVariant variant = mock(ProductVariant.class);
        Attribute attribute = mock(Attribute.class);

        when(retailerProduct.getKey()).thenReturn("retailerKey");
        when(retailerProduct.getMasterVariant()).thenReturn(variant);
        when(retailerProduct.getVersion()).thenReturn(1L);
        when(retailerProduct.getId()).thenReturn("r1");

        when(attribute.getName()).thenReturn(MARKED_FOR_DELETION);
        when(attribute.getValue()).thenReturn("not-a-date");

        when(variant.getAttributes()).thenReturn(List.of(attribute));

        productDeletionService.processMissingProduct(retailerProduct, new ArrayList<>(),statusData);

        verify(byIdBuilder, never()).delete();
    }

    @Test
    void testHandleInitialMarkForDeletion_withException() {
        ProductProjection retailerProduct = mock(ProductProjection.class);
        ProductVariant variant = mock(ProductVariant.class);

        when(retailerProduct.getId()).thenReturn("productId");
        when(retailerProduct.getVersion()).thenReturn(2L);
        when(retailerProduct.getKey()).thenReturn("retailerKey");
        when(retailerProduct.getMasterVariant()).thenReturn(variant);
        when(variant.getId()).thenReturn(1L);
        when(variant.getAttributes()).thenReturn(new ArrayList<>());

        when(byIdBuilder.post(any(ProductUpdate.class))).thenThrow(new RuntimeException("Post failed"));

        ReflectionTestUtils.invokeMethod(productDeletionService, "handleInitialMarkForDeletion", retailerProduct, new ArrayList<>(),statusData);

        verify(byIdBuilder).post(any(ProductUpdate.class));
    }

    @Test
    void testDeleteProduct_withException_onProductProjection() {
        ProductProjection product = mock(ProductProjection.class);
        when(product.getId()).thenReturn("id1");
        when(product.getVersion()).thenReturn(1L);
        when(product.getKey()).thenReturn("key1");

        when(byIdBuilder.delete()).thenReturn(byIdDelete);
        when(byIdDelete.withVersion(1L)).thenReturn(byIdDelete);
        when(byIdDelete.executeBlocking()).thenThrow(new RuntimeException("Delete failed"));

        ReflectionTestUtils.invokeMethod(productDeletionService, "deleteProduct", product,statusData);
    }

    @Test
    void testDeleteProduct_withException_onProduct() {
        Product product = mock(Product.class);
        when(product.getId()).thenReturn("id1");
        when(product.getVersion()).thenReturn(1L);
        when(product.getKey()).thenReturn("key1");

        when(byIdBuilder.delete()).thenReturn(byIdDelete);
        when(byIdDelete.withVersion(1L)).thenReturn(byIdDelete);
        when(byIdDelete.executeBlocking()).thenThrow(new RuntimeException("Delete failed"));

        ReflectionTestUtils.invokeMethod(productDeletionService, "deleteProduct", product,statusData);
    }

    @Test
    void testRepublishAssociatedProductsInParallel_withThreadException() throws Exception {
        String retailerKey = "retailer123";

        ProductProjection product = createProductProjection("prod1", 1L);
        when(product.getKey()).thenReturn("key123");
        when(product.getPublished()).thenReturn(false);

        ProductProjectionPagedSearchResponse response = mock(ProductProjectionPagedSearchResponse.class);
        when(response.getResults()).thenReturn(List.of(product));
        when(response.getCount()).thenReturn(1L);
        when(response.getTotal()).thenReturn(1L);
        when(response.getLimit()).thenReturn(500L);
        when(response.getOffset()).thenReturn(0L);

        ByProjectKeyProductProjectionsRequestBuilder productProjectionsRequestBuilder = mock(ByProjectKeyProductProjectionsRequestBuilder.class);
        ByProjectKeyProductProjectionsSearchRequestBuilder searchRequestBuilder = mock(ByProjectKeyProductProjectionsSearchRequestBuilder.class);
        ByProjectKeyProductProjectionsSearchGet searchGet = mock(ByProjectKeyProductProjectionsSearchGet.class);
        ApiHttpResponse<ProductProjectionPagedSearchResponse> searchResponse = mock(ApiHttpResponse.class);

        when(commercetoolsClient.productProjections()).thenReturn(productProjectionsRequestBuilder);
        when(productProjectionsRequestBuilder.search()).thenReturn(searchRequestBuilder);
        when(searchRequestBuilder.get()).thenReturn(searchGet);
        when(searchGet.addQueryParam(anyString(), anyString())).thenReturn(searchGet);
        when(searchGet.withStaged(anyBoolean())).thenReturn(searchGet);
        when(searchGet.withLimit(anyInt())).thenReturn(searchGet);
        when(searchGet.withOffset(anyInt())).thenReturn(searchGet);
        when(searchGet.executeBlocking()).thenReturn(searchResponse);
        when(searchResponse.getBody()).thenReturn(response);

        when(productsBuilder.withId("prod1")).thenReturn(byIdBuilder);
        when(byIdBuilder.post(any(ProductUpdate.class))).thenReturn(postRequest);
        when(postRequest.executeBlocking()).thenThrow(new RuntimeException("Thread failure"));

        ReflectionTestUtils.invokeMethod(productDeletionService, "republishAssociatedProductsInParallel", retailerKey);
    }

    private ProductProjection createProductProjection(String id, Long version) {
        ProductProjection productProjection = mock(ProductProjection.class);
        when(productProjection.getId()).thenReturn(id);
        when(productProjection.getVersion()).thenReturn(version);
        return productProjection;
    }

}
