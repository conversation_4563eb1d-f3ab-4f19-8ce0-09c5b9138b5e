package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.*;
import com.commercetools.api.models.common.Image;
import com.commercetools.api.models.common.ImageDimensions;
import com.commercetools.api.models.common.ImageDimensionsBuilder;
import com.commercetools.api.models.product.*;
import com.commercetools.api.models.product_type.*;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.model.ProductModel;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import io.vrap.rmf.base.client.ApiHttpHeaders;
import io.vrap.rmf.base.client.ApiHttpResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RetailerImportServiceTest {

    @Mock
    private S3Client s3Client;

    @Mock
    private ProjectApiRoot commercetoolsClient;

    @Mock
    private ProductDeletionService productDeletionService;

    @Mock
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @Mock
    EtlQueueStatusData statusData;

    @Mock
    private ByProjectKeyProductProjectionsRequestBuilder productProjectionsRequestBuilder;

    @Mock
    private ByProjectKeyProductProjectionsGet productProjectionsGet;

    @Mock
    private ByProjectKeyProductTypesRequestBuilder productTypesRequestBuilder;

    @Mock
    private ByProjectKeyProductTypesKeyByKeyRequestBuilder productTypesKeyByKeyRequestBuilder;

    @Mock
    private ByProjectKeyProductTypesKeyByKeyGet productTypesKeyByKeyGet;

    @InjectMocks
    private RetailerImportService retailerImportService;

    private static final String MARKED_FOR_DELETION = "markedForDeletion";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(retailerImportService, "s3Client", s3Client);
        ReflectionTestUtils.setField(retailerImportService, "commercetoolsClient", commercetoolsClient);
        ReflectionTestUtils.setField(retailerImportService, "threadCount", 2);
        ReflectionTestUtils.setField(retailerImportService, "productDeletionService", productDeletionService);
        ReflectionTestUtils.setField(retailerImportService, "etlQueueStatusDataRepository", etlQueueStatusDataRepository);
        ReflectionTestUtils.setField(retailerImportService, "productTypeKey", "retailerproduct");
        ReflectionTestUtils.setField(retailerImportService, "pathPrefix", "retailers/inbound");
        ReflectionTestUtils.setField(retailerImportService, "pathArchive", "retailers/archive");
        ReflectionTestUtils.setField(retailerImportService, "pathImages", "retailers/images/");
    }

    @Test
    void testImportRetailerData_withValidCSV_shouldProcess() throws Exception {
        String payload = """
            {
              "Records": [{
                "responseElements": {
                        "x-amz-request-id": "RMXBDDWCKWWR0KWT",
                        "x-amz-id-2": "FwOSDq+gSs0QW2op+rfLUi90sMdV1VDd8gNPqZdn1GFIxJOPLqd1SHCQSc0i56loZM2ehQ/OvpiCXmuD/7WPSQjCpC1H/Zgh"
                },
                "s3": {
                  "bucket": {
                    "name": "test-bucket"
                  },
                  "object": {
                    "key": "retailers/inbound/sample.csv"
                  }
                }
              }]
            }
            """;

        mockProductType();
        mockProductProjectionQuery();
        mockS3CsvResponse();

        retailerImportService.importRetailerData(payload, "msg-id");

        verify(s3Client).getObject(any(GetObjectRequest.class));
    }

    @Test
    void testHandleMissingProduct_shouldInvokeProductDeletion() {
        ProductProjection productProjection = mock(ProductProjection.class);
        EtlQueueStatusData existingStatus = mock(EtlQueueStatusData.class);

        List<ProductProjection> associatedProducts = Collections.emptyList();

        productDeletionService.processMissingProduct(productProjection, associatedProducts,existingStatus);

        verify(productDeletionService).processMissingProduct(eq(productProjection), eq(associatedProducts),eq(existingStatus));
    }

    @Test
    void testImportRetailerData_triggersImageUpload() throws Exception {
        String payload = """
            {
              "Records": [{
                "responseElements": {
                        "x-amz-request-id": "RMXBDDWCKWWR0KWT",
                        "x-amz-id-2": "FwOSDq+gSs0QW2op+rfLUi90sMdV1VDd8gNPqZdn1GFIxJOPLqd1SHCQSc0i56loZM2ehQ/OvpiCXmuD/7WPSQjCpC1H/Zgh"
                },
                "s3": {
                  "bucket": {
                    "name": "test-bucket"
                  },
                  "object": {
                    "key": "retailers/inbound/sample.csv"
                  }
                }
              }]
            }
            """;

        String csv = "retailerName,grandparentID,parentIDs,storeType,feedType,partnerType\n" +
                "RetailerX,GID001,PID001,StoreX,FeedY,PartnerZ";
        InputStream csvStream = new ByteArrayInputStream(csv.getBytes(StandardCharsets.UTF_8));
        ResponseInputStream<GetObjectResponse> csvResponseStream =
                new ResponseInputStream<>(GetObjectResponse.builder().build(), csvStream);
        when(s3Client.getObject((GetObjectRequest) any())).thenReturn(csvResponseStream);

        ProductType productType = ProductTypeBuilder.of()
                .id("productTypeId")
                .key("retailerproduct")
                .name("Retailer Product")
                .version(1L)
                .createdAt(ZonedDateTime.now())
                .lastModifiedAt(ZonedDateTime.now())
                .description("desc")
                .build();
        ApiHttpResponse<ProductType> productTypeResponse = mock(ApiHttpResponse.class);
        when(productTypeResponse.getBody()).thenReturn(productType);
        when(commercetoolsClient.productTypes()).thenReturn(productTypesRequestBuilder);
        when(productTypesRequestBuilder.withKey("retailerproduct")).thenReturn(productTypesKeyByKeyRequestBuilder);
        when(productTypesKeyByKeyRequestBuilder.get()).thenReturn(productTypesKeyByKeyGet);
        when(productTypesKeyByKeyGet.executeBlocking()).thenReturn(productTypeResponse);

        ProductProjectionPagedQueryResponse productResponse = mock(ProductProjectionPagedQueryResponse.class);
        when(productResponse.getResults()).thenReturn(Collections.emptyList());
        ApiHttpResponse<ProductProjectionPagedQueryResponse> projectionsResponse = mock(ApiHttpResponse.class);
        when(projectionsResponse.getBody()).thenReturn(productResponse);
        when(commercetoolsClient.productProjections()).thenReturn(productProjectionsRequestBuilder);
        when(productProjectionsRequestBuilder.get()).thenReturn(productProjectionsGet);
        when(productProjectionsGet.withWhere(anyString())).thenReturn(productProjectionsGet);
        when(productProjectionsGet.withStaged(anyBoolean())).thenReturn(productProjectionsGet);
        when(productProjectionsGet.withLimit(anyInt())).thenReturn(productProjectionsGet);
        when(productProjectionsGet.withOffset(anyInt())).thenReturn(productProjectionsGet);
        when(productProjectionsGet.executeBlocking()).thenReturn(projectionsResponse);

        Product mockProduct = mock(Product.class);
        when(mockProduct.getId()).thenReturn("productId");
        when(mockProduct.getVersion()).thenReturn(1L);

        ByProjectKeyProductsRequestBuilder productsBuilder = mock(ByProjectKeyProductsRequestBuilder.class);
        when(commercetoolsClient.products()).thenReturn(productsBuilder);

        ByProjectKeyProductsPost productsPost = mock(ByProjectKeyProductsPost.class);
        ApiHttpResponse<Product> createResponse = mock(ApiHttpResponse.class);
        when(createResponse.getBody()).thenReturn(mockProduct);
        when(productsBuilder.post(any(ProductDraft.class))).thenReturn(productsPost);
        when(productsPost.executeBlocking()).thenReturn(createResponse);

        ByProjectKeyProductsByIDRequestBuilder productByIdBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDPost publishPost = mock(ByProjectKeyProductsByIDPost.class);
        when(productsBuilder.withId("productId")).thenReturn(productByIdBuilder);
        when(productByIdBuilder.post(any(ProductUpdate.class))).thenReturn(publishPost);
        when(publishPost.executeBlocking()).thenReturn(createResponse);

        ProductCatalogData mockCatalogData = mock(ProductCatalogData.class);
        ProductData mockCurrentData = mock(ProductData.class);
        ProductVariant mockMasterVariant = mock(ProductVariant.class);
        when(mockMasterVariant.getSku()).thenReturn("retailerx");
        when(mockCurrentData.getMasterVariant()).thenReturn(mockMasterVariant);
        when(mockCatalogData.getCurrent()).thenReturn(mockCurrentData);
        when(mockProduct.getMasterData()).thenReturn(mockCatalogData);

        byte[] imageData = "fake-image-content".getBytes(StandardCharsets.UTF_8);
        InputStream imageStream = new ByteArrayInputStream(imageData);
        ResponseInputStream<GetObjectResponse> imageResponseStream =
                new ResponseInputStream<>(GetObjectResponse.builder().build(), imageStream);
        when(s3Client.getObject(argThat((GetObjectRequest req) -> req.key().contains("retailers/images/"))))
                .thenReturn(imageResponseStream);

        ByProjectKeyProductsByIDImagesRequestBuilder imagesBuilder = mock(ByProjectKeyProductsByIDImagesRequestBuilder.class);
        ByProjectKeyProductsByIDImagesPost imagePost = mock(ByProjectKeyProductsByIDImagesPost.class);
        when(productByIdBuilder.images()).thenReturn(imagesBuilder);
        when(imagesBuilder.post(any(File.class))).thenReturn(imagePost);
        when(imagePost.contentType(anyString())).thenReturn(imagePost);
        when(imagePost.withSku(anyString())).thenReturn(imagePost);
        when(imagePost.executeBlocking()).thenReturn(createResponse);

        retailerImportService.importRetailerData(payload, "message-id");

        verify(s3Client, atLeastOnce()).getObject(argThat((GetObjectRequest req) -> req.key().contains("retailers/images/")));
        verify(imagePost).executeBlocking();
    }

    private void mockProductType() throws Exception {
        when(commercetoolsClient.productTypes()).thenReturn(productTypesRequestBuilder);
        when(productTypesRequestBuilder.withKey(anyString())).thenReturn(productTypesKeyByKeyRequestBuilder);
        when(productTypesKeyByKeyRequestBuilder.get()).thenReturn(productTypesKeyByKeyGet);

        ProductType productType = ProductTypeBuilder.of()
                .id("productTypeId")
                .key("retailerproduct")
                .name("Retailer Product")
                .version(1L)
                .createdAt(ZonedDateTime.now())
                .lastModifiedAt(ZonedDateTime.now())
                .description("description")
                .build();

        ApiHttpResponse<ProductType> response = new ApiHttpResponse<>(200, null, productType);
        when(productTypesKeyByKeyGet.executeBlocking()).thenReturn(response);
    }

    private void mockProductProjectionQuery() throws Exception {
        ProductProjectionPagedQueryResponse response = mock(ProductProjectionPagedQueryResponse.class);

        ApiHttpResponse<ProductProjectionPagedQueryResponse> httpResponse =
                new ApiHttpResponse<>(200, mock(ApiHttpHeaders.class), response);

        when(commercetoolsClient.productProjections()).thenReturn(productProjectionsRequestBuilder);
        when(productProjectionsRequestBuilder.get()).thenReturn(productProjectionsGet);

    }

    private void mockS3CsvResponse() {
        String csv = "retailerName,grandparentID,parentIDs,storeType,feedType,partnerType\n" +
                "RetailerA,GID001,PID001,StoreX,FeedY,PartnerZ";
        InputStream csvStream = new ByteArrayInputStream(csv.getBytes(StandardCharsets.UTF_8));
        ResponseInputStream<GetObjectResponse> responseStream = new ResponseInputStream<>(GetObjectResponse.builder().build(), csvStream);

        when(s3Client.getObject(any(GetObjectRequest.class))).thenReturn(responseStream);
    }

    @Test
    void testHandleMissingProduct_invokesProcessMissingProduct() throws Exception {
        ProductProjection product = mock(ProductProjection.class);
        when(product.getKey()).thenReturn("testKey");

        ProductProjectionPagedSearchResponse response = mock(ProductProjectionPagedSearchResponse.class);
        when(response.getResults()).thenReturn(Collections.emptyList());

        ApiHttpResponse<ProductProjectionPagedSearchResponse> apiResponse = mock(ApiHttpResponse.class);
        when(apiResponse.getBody()).thenReturn(response);

        ByProjectKeyProductProjectionsRequestBuilder productProjectionsRequestBuilder = mock(ByProjectKeyProductProjectionsRequestBuilder.class);
        ByProjectKeyProductProjectionsSearchRequestBuilder searchBuilder = mock(ByProjectKeyProductProjectionsSearchRequestBuilder.class);
        ByProjectKeyProductProjectionsSearchGet searchGet = mock(ByProjectKeyProductProjectionsSearchGet.class);

        when(commercetoolsClient.productProjections()).thenReturn(productProjectionsRequestBuilder);
        when(productProjectionsRequestBuilder.search()).thenReturn(searchBuilder);
        when(searchBuilder.get()).thenReturn(searchGet);
        when(searchGet.addQueryParam(eq("filter.query"), anyString())).thenReturn(searchGet);
        when(searchGet.withLimit(anyInt())).thenReturn(searchGet);
        when(searchGet.withStaged(anyBoolean())).thenReturn(searchGet);
        when(searchGet.withOffset(anyInt())).thenReturn(searchGet);
        when(searchGet.executeBlocking()).thenReturn(apiResponse);

        ReflectionTestUtils.invokeMethod(retailerImportService, "handleMissingProduct", product,statusData);

        verify(productDeletionService).processMissingProduct(eq(product), anyList(),any());
    }


    @Test
    void testCreateOrUpdateProduct_createsNewProduct_whenNotExists() throws Exception {
        ProductModel productModel = new ProductModel();
        productModel.setRetailerName("RetailerName");
        productModel.setGrandparentID("GPID");
        productModel.setParentIDs("PID");
        productModel.setStoreType("StoreType");
        productModel.setFeedType("FeedType");
        productModel.setPartnerType("PartnerType");

        String productKey = "retailername";

        Map<String, ProductProjection> emptyExistingProducts = new HashMap<>();

        Product createdProduct = mock(Product.class);
        when(createdProduct.getId()).thenReturn("id");
        when(createdProduct.getVersion()).thenReturn(1L);

        ByProjectKeyProductsRequestBuilder productsBuilder = mock(ByProjectKeyProductsRequestBuilder.class);
        ByProjectKeyProductsPost productsPost = mock(ByProjectKeyProductsPost.class);
        ApiHttpResponse<Product> postResponse = mock(ApiHttpResponse.class);

        when(commercetoolsClient.products()).thenReturn(productsBuilder);
        when(productsBuilder.post(any(ProductDraft.class))).thenReturn(productsPost);
        when(productsPost.executeBlocking()).thenReturn(postResponse);
        when(postResponse.getBody()).thenReturn(createdProduct);

        ByProjectKeyProductsByIDRequestBuilder byIdBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDPost byIdPost = mock(ByProjectKeyProductsByIDPost.class);

        when(productsBuilder.withId("id")).thenReturn(byIdBuilder);
        when(byIdBuilder.post(any(ProductUpdate.class))).thenReturn(byIdPost);
        when(byIdPost.executeBlocking()).thenReturn(postResponse);

        Product result = ReflectionTestUtils.invokeMethod(retailerImportService, "createOrUpdateProduct",
                productKey, productModel, emptyExistingProducts,statusData);

        assertNotNull(result);
        verify(productsBuilder).post(any(ProductDraft.class));
        verify(byIdBuilder).post(any(ProductUpdate.class));
    }

    @Test
    void testCreateOrUpdateProduct_updatesExistingProduct_whenExists() throws Exception {
        String productKey = "retailername";

        ProductModel newProduct = new ProductModel();
        newProduct.setRetailerName("RetailerName");
        newProduct.setGrandparentID("GPID");
        newProduct.setParentIDs("PID");
        newProduct.setStoreType("StoreType");
        newProduct.setFeedType("FeedType");
        newProduct.setPartnerType("PartnerType");

        ProductProjection existing = mock(ProductProjection.class);
        when(existing.getVersion()).thenReturn(1L);
        when(existing.getId()).thenReturn("existingId");
        when(existing.getPublished()).thenReturn(true);

        ProductVariant masterVariant = mock(ProductVariant.class);
        when(masterVariant.getId()).thenReturn(10L);
        ImageDimensions dimensions = ImageDimensionsBuilder.of()
                .w(100)
                .h(100)
                .build();
        Image image = Image.builder()
                .url("http://image.url")
                .dimensions(dimensions)
                .build();
        when(masterVariant.getImages()).thenReturn(Collections.singletonList(image));
        when(existing.getMasterVariant()).thenReturn(masterVariant);


        Map<String, ProductProjection> existingMap = new HashMap<>();
        existingMap.put(productKey, existing);

        Product updatedProduct = mock(Product.class);

        ByProjectKeyProductsRequestBuilder productsBuilder = mock(ByProjectKeyProductsRequestBuilder.class);
        ByProjectKeyProductsByIDRequestBuilder byIdBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDPost post = mock(ByProjectKeyProductsByIDPost.class);
        ApiHttpResponse<Product> apiResponse = mock(ApiHttpResponse.class);

        when(commercetoolsClient.products()).thenReturn(productsBuilder);
        when(productsBuilder.withId("existingId")).thenReturn(byIdBuilder);
        when(byIdBuilder.post(any(ProductUpdate.class))).thenReturn(post);
        when(post.executeBlocking()).thenReturn(apiResponse);
        when(apiResponse.getBody()).thenReturn(updatedProduct);

        ReflectionTestUtils.setField(retailerImportService, "productDeletionService", productDeletionService);
        Product result = ReflectionTestUtils.invokeMethod(retailerImportService, "createOrUpdateProduct",
                productKey, newProduct, existingMap,statusData);

        assertNotNull(result);
        verify(productsBuilder).withId("existingId");
        verify(byIdBuilder).post(any(ProductUpdate.class));
    }

    @Test
    void testCreateOrUpdateProduct_withMarkedForDeletionAttribute() throws Exception {
        String productKey = "retailername";

        ProductModel newProduct = new ProductModel();
        newProduct.setRetailerName("RetailerName");
        newProduct.setGrandparentID("GPID");
        newProduct.setParentIDs("PID");
        newProduct.setStoreType("StoreType");
        newProduct.setFeedType("FeedType");
        newProduct.setPartnerType("PartnerType");

        ProductProjection existing = mock(ProductProjection.class);
        when(existing.getVersion()).thenReturn(1L);
        when(existing.getId()).thenReturn("existingId");
        when(existing.getPublished()).thenReturn(false);

        ProductVariant masterVariant = mock(ProductVariant.class);
        when(masterVariant.getId()).thenReturn(10L);

        Attribute deletionAttribute = mock(Attribute.class);
        when(deletionAttribute.getName()).thenReturn(MARKED_FOR_DELETION);

        when(masterVariant.getAttributes()).thenReturn(Collections.singletonList(deletionAttribute));
        when(existing.getMasterVariant()).thenReturn(masterVariant);

        Map<String, ProductProjection> existingMap = new HashMap<>();
        existingMap.put(productKey, existing);
        Product updatedProduct = mock(Product.class);

        ByProjectKeyProductsRequestBuilder productsBuilder = mock(ByProjectKeyProductsRequestBuilder.class);
        ByProjectKeyProductsByIDRequestBuilder byIdBuilder = mock(ByProjectKeyProductsByIDRequestBuilder.class);
        ByProjectKeyProductsByIDPost post = mock(ByProjectKeyProductsByIDPost.class);
        ApiHttpResponse<Product> apiResponse = mock(ApiHttpResponse.class);

        when(commercetoolsClient.products()).thenReturn(productsBuilder);
        when(productsBuilder.withId("existingId")).thenReturn(byIdBuilder);
        when(byIdBuilder.post(any(ProductUpdate.class))).thenReturn(post);
        when(post.executeBlocking()).thenReturn(apiResponse);
        when(apiResponse.getBody()).thenReturn(updatedProduct);

        ReflectionTestUtils.setField(retailerImportService, "productDeletionService", productDeletionService);
        Product result = ReflectionTestUtils.invokeMethod(retailerImportService, "createOrUpdateProduct",
                productKey, newProduct, existingMap,statusData);

        assertNotNull(result);
        verify(productsBuilder).withId("existingId");
        verify(byIdBuilder).post(any(ProductUpdate.class));
    }

    @Test
    void testImportRetailerData_whenUpdateStatusQueueThrowsException_shouldLogError() throws Exception {
        String messageId = "test-message-id";
        String payload = """
        {
          "Records": [{
            "responseElements": {
              "x-amz-request-id": "RMXBDDWCKWWR0KWT"
            },
            "s3": {
              "bucket": {
                "name": "test-bucket"
              },
              "object": {
                "key": "retailers/inbound/sample.csv"
              }
            }
          }]
        }
        """;

        when(etlQueueStatusDataRepository.findByEtlIDAndSqsMessageDataAndEtlStep(
                eq("RMXBDDWCKWWR0KWT"), eq(messageId), eq(EtlQueueStatusData.EtlStep.RETAILER_IMPORT)))
                .thenThrow(new RuntimeException("Simulated DB failure"));

        retailerImportService.importRetailerData(payload, messageId);

        verify(etlQueueStatusDataRepository).findByEtlIDAndSqsMessageDataAndEtlStep(
                "RMXBDDWCKWWR0KWT", messageId, EtlQueueStatusData.EtlStep.RETAILER_IMPORT);
    }

    @Test
    void testImportRetailerData_whenExistingQueueStatusPresent_shouldUpdateAndSave() {

        String messageId = "test-message-id";
        String requestId = "RMXBDDWCKWWR0KWT";
        String payload = """
        {
          "Records": [ {
            "responseElements": {
              "x-amz-request-id": "RMXBDDWCKWWR0KWT"
            },
            "s3": {
              "bucket": {
                "name": "test-bucket"
              },
              "object": {
                "key": "retailers/inbound/sample.csv"
              }
            }
          }]
        }
        """;

        EtlQueueStatusData existingStatus = mock(EtlQueueStatusData.class);
        when(etlQueueStatusDataRepository.findByEtlIDAndSqsMessageDataAndEtlStep(
                eq(requestId), eq(messageId), eq(EtlQueueStatusData.EtlStep.RETAILER_IMPORT)))
                .thenReturn(Optional.of(existingStatus));

        retailerImportService.importRetailerData(payload, messageId);

        verify(existingStatus).setStatus(EtlQueueStatusData.Status.STARTED);
        verify(existingStatus, times(2)).setLastUpdate(any());
        verify(etlQueueStatusDataRepository, times(2)).save(existingStatus);
    }

    @Test
    void testGetEnumOptionsForAttribute_returnsEnumMap() {
        String attributeName = "color";

        AttributePlainEnumValue enumValue1 = AttributePlainEnumValue.builder()
                .key("RED")
                .label("Red Label")
                .build();
        AttributePlainEnumValue enumValue2 = AttributePlainEnumValue.builder()
                .key("BLUE")
                .label("Blue Label")
                .build();

        AttributeEnumType enumType = mock(AttributeEnumType.class);
        when(enumType.getValues()).thenReturn(List.of(enumValue1, enumValue2));

        AttributeDefinition attributeDefinition = mock(AttributeDefinition.class);
        when(attributeDefinition.getName()).thenReturn(attributeName);
        when(attributeDefinition.getType()).thenReturn(enumType);

        ProductType productType = mock(ProductType.class);
        when(productType.getAttributes()).thenReturn(List.of(attributeDefinition));

        when(commercetoolsClient.productTypes()).thenReturn(productTypesRequestBuilder);
        when(productTypesRequestBuilder.withKey("retailerproduct")).thenReturn(productTypesKeyByKeyRequestBuilder);
        when(productTypesKeyByKeyRequestBuilder.get()).thenReturn(productTypesKeyByKeyGet);

        ApiHttpResponse<ProductType> response = mock(ApiHttpResponse.class);
        when(response.getBody()).thenReturn(productType);
        when(productTypesKeyByKeyGet.executeBlocking()).thenReturn(response);

        Map<String, String> result = retailerImportService.getEnumOptionsForAttribute(attributeName);

        assertEquals(2, result.size());
        assertEquals("Red Label", result.get("red"));
        assertEquals("Blue Label", result.get("blue"));

        Map<String, String> cachedResult = retailerImportService.getEnumOptionsForAttribute(attributeName);
        assertSame(result, cachedResult);

        verify(commercetoolsClient.productTypes(), times(1)).withKey(anyString());
        verify(productTypesKeyByKeyGet, times(1)).executeBlocking();
    }

    @Test
    void testGetEnumOptionsForAttribute_noEnumAttribute_returnsEmpty() {
        String attributeName = "nonEnumAttribute";

        AttributeDefinition attributeDefinition = mock(AttributeDefinition.class);
        when(attributeDefinition.getName()).thenReturn(attributeName);
        when(attributeDefinition.getType()).thenReturn(mock(AttributeType.class)); // some other type

        ProductType productType = mock(ProductType.class);
        when(productType.getAttributes()).thenReturn(List.of(attributeDefinition));

        when(commercetoolsClient.productTypes()).thenReturn(productTypesRequestBuilder);
        when(productTypesRequestBuilder.withKey("retailerproduct")).thenReturn(productTypesKeyByKeyRequestBuilder);
        when(productTypesKeyByKeyRequestBuilder.get()).thenReturn(productTypesKeyByKeyGet);

        ApiHttpResponse<ProductType> response = mock(ApiHttpResponse.class);
        when(response.getBody()).thenReturn(productType);
        when(productTypesKeyByKeyGet.executeBlocking()).thenReturn(response);

        Map<String, String> result = retailerImportService.getEnumOptionsForAttribute(attributeName);

        assertTrue(result.isEmpty());
    }

    @Test
    void testGetEnumOptionsForAttribute_whenException_returnsEmpty() {
        String attributeName = "color";

        when(commercetoolsClient.productTypes()).thenReturn(productTypesRequestBuilder);
        when(productTypesRequestBuilder.withKey("retailerproduct")).thenReturn(productTypesKeyByKeyRequestBuilder);
        when(productTypesKeyByKeyRequestBuilder.get()).thenReturn(productTypesKeyByKeyGet);

        when(productTypesKeyByKeyGet.executeBlocking()).thenThrow(new RuntimeException("Simulated failure"));

        Map<String, String> result = retailerImportService.getEnumOptionsForAttribute(attributeName);

        assertTrue(result.isEmpty());
    }

}
