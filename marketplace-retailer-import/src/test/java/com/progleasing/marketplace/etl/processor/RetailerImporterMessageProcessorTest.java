package com.progleasing.marketplace.etl.processor;

import com.progleasing.marketplace.etl.service.RetailerImportService;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Mockito.*;

@SpringJUnitConfig
@TestPropertySource(properties = "aws.sqs.concurrent-message:5")
public class RetailerImporterMessageProcessorTest {

    @Mock
    private RetailerImportService retailerImportService;

    @InjectMocks
    private RetailerImporterMessageProcessor retailerImporterMessageProcessor;

    @Mock
    private AcknowledgementCallback acknowledgementCallback;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        retailerImporterMessageProcessor = new RetailerImporterMessageProcessor(retailerImportService);
        Field field = RetailerImporterMessageProcessor.class.getDeclaredField("maxConcurrentMessages");
        field.setAccessible(true);
        field.set(retailerImporterMessageProcessor, 5);
    }

    @Test
    public void testHandle_withValidMessages_shouldProcessSuccessfully() throws Exception {
        Message<String> mockMessage = createMockMessage("testPayload");

        doNothing().when(retailerImportService).importRetailerData(anyString(), anyString());
        when(acknowledgementCallback.onAcknowledge(mockMessage)).thenReturn(CompletableFuture.completedFuture(null));

        retailerImporterMessageProcessor.handle(Collections.singletonList(mockMessage));
        verify(retailerImportService, times(1)).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, times(1)).onAcknowledge(mockMessage);  // Acknowledge after successful processing
    }

    @Test
    public void testHandle_withExceptionInMessageProcessing_shouldLogError() throws Exception {
        Message<String> mockMessage = createMockMessage("testPayload");
        doThrow(new RuntimeException("Processing error")).when(retailerImportService).importRetailerData(anyString(), anyString());
        when(acknowledgementCallback.onAcknowledge(mockMessage)).thenReturn(CompletableFuture.completedFuture(null));

        retailerImporterMessageProcessor.handle(Collections.singletonList(mockMessage));
        verify(retailerImportService, times(1)).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, never()).onAcknowledge(mockMessage);  // No acknowledgment if there's an error
    }

    @Test
    public void testHandle_withConcurrentProcessing_shouldLimitConcurrency() throws Exception {
        Message<String> mockMessage1 = createMockMessage("testPayload1");
        Message<String> mockMessage2 = createMockMessage("testPayload2");

        doNothing().when(retailerImportService).importRetailerData(anyString(), anyString());
        when(acknowledgementCallback.onAcknowledge(any(Message.class))).thenReturn(CompletableFuture.completedFuture(null));

        retailerImporterMessageProcessor.handle(Arrays.asList(mockMessage1, mockMessage2));
        verify(retailerImportService, times(2)).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, times(2)).onAcknowledge(any(Message.class));  // Verify acknowledgment for each message
    }

    @Test
    public void testHandle_withMultipleMessages_shouldProcessAllMessages() throws Exception {
        Message<String> mockMessage1 = createMockMessage("testPayload1");
        Message<String> mockMessage2 = createMockMessage("testPayload2");
        Message<String> mockMessage3 = createMockMessage("testPayload3");

        doNothing().when(retailerImportService).importRetailerData(anyString(), anyString());
        when(acknowledgementCallback.onAcknowledge(any(Message.class))).thenReturn(CompletableFuture.completedFuture(null));

        retailerImporterMessageProcessor.handle(Arrays.asList(mockMessage1, mockMessage2, mockMessage3));

        verify(retailerImportService, times(3)).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, times(3)).onAcknowledge(any(Message.class));
    }

    @Test
    public void testHandle_withEmptyMessageList_shouldNotProcessAnything() throws Exception {
        retailerImporterMessageProcessor.handle(Collections.emptyList());

        verify(retailerImportService, never()).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, never()).onAcknowledge(any(Message.class));
    }

    @Test
    public void testHandle_withMessageProcessingFailure_shouldNotAcknowledge() throws Exception {
        Message<String> mockMessage = createMockMessage("testPayload");

        doThrow(new RuntimeException("Processing error")).when(retailerImportService).importRetailerData(anyString(), anyString());
        when(acknowledgementCallback.onAcknowledge(mockMessage)).thenReturn(CompletableFuture.completedFuture(null));

        retailerImporterMessageProcessor.handle(Collections.singletonList(mockMessage));

        verify(retailerImportService, times(1)).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, never()).onAcknowledge(mockMessage);
    }

    @Test
    public void testHandle_withInvalidMessage_shouldHandleGracefully() throws Exception {
        Message<String> mockMessage = createMockMessage("invalidPayload");

        doThrow(new IllegalArgumentException("Invalid data")).when(retailerImportService).importRetailerData(anyString(), anyString());

        retailerImporterMessageProcessor.handle(Collections.singletonList(mockMessage));

        verify(retailerImportService, times(1)).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, never()).onAcknowledge(mockMessage);
    }

    @Test
    public void testHandle_withHighConcurrency_shouldNotExceedMaxConcurrency() throws Exception {
        List<Message<String>> messages = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            messages.add(createMockMessage("testPayload" + i));
        }

        doNothing().when(retailerImportService).importRetailerData(anyString(), anyString());
        when(acknowledgementCallback.onAcknowledge(any(Message.class))).thenReturn(CompletableFuture.completedFuture(null));

        retailerImporterMessageProcessor.handle(messages);

        verify(retailerImportService, times(10)).importRetailerData(anyString(), anyString());
        verify(acknowledgementCallback, times(10)).onAcknowledge(any(Message.class));
    }

    private Message<String> createMockMessage(String payload) {
        UUID messageId = UUID.randomUUID();
        Map<String, Object> headersMap = new HashMap<>();
        headersMap.put("AcknowledgementCallback", acknowledgementCallback);

        MessageHeaderAccessor messageHeaderAccessor = new MessageHeaderAccessor();
        messageHeaderAccessor.copyHeaders(headersMap);
        return MessageBuilder.withPayload(payload)
                .setHeaders(messageHeaderAccessor)
                .build();
    }

}
