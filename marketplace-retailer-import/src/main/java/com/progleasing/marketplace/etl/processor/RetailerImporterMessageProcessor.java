package com.progleasing.marketplace.etl.processor;

import com.progleasing.marketplace.etl.service.RetailerImportService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Service
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.retailer-importer.listener.enabled", havingValue = "true", matchIfMissing = true)
public class RetailerImporterMessageProcessor {

    private final RetailerImportService retailerImportService;

    @Value("${aws.sqs.concurrent-message:10}")
    private Integer maxConcurrentMessages;

    public RetailerImporterMessageProcessor(RetailerImportService retailerImportService) {
        this.retailerImportService = retailerImportService;
    }


    @SqsListener(value = "${aws.sqs.retailer-importer.queue-name}",
            maxConcurrentMessages = "${aws.sqs.concurrent-message}", acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL
    )
    public void handle(List<Message<String>> messages) {
        log.info("Messages received: {}", messages.size());

        ExecutorService executorService = Executors.newFixedThreadPool(maxConcurrentMessages);
        List<Future<Void>> futures = new ArrayList<>();

        for (Message<String> message : messages) {
            futures.add(executorService.submit(() -> {
                String messageId = message.getHeaders().get("id").toString();
                try {
                    String payload = message.getPayload();
                    log.info("Processing message with payload: {} and id {}", payload, messageId);

                    retailerImportService.importRetailerData(payload, messageId);

                    Acknowledgement.acknowledge(message);
                } catch (Exception e) {
                    log.error("Error processing message {}", messageId, e);
                }
                return null;
            }));
        }

        for (Future<Void> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception while processing message batch", e);
                Thread.currentThread().interrupt();
            }
        }

        executorService.shutdown();
    }
}
