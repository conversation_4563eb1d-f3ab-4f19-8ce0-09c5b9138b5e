package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.product.*;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.progleasing.marketplace.etl.repositories.ProductDataRepository;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;

@Service
@Slf4j
public class ProductDeletionService {

    @Value("${market.place.retailer.thread.count}")
    private int threadCount;

    @Value("${market.place.retailer.grace.days}")
    private int gracePeriodDays;

    @Qualifier("ctApiClient")
    @Autowired
    private ProjectApiRoot commercetoolsClient;

    @Autowired
    private ProductDataRepository productDataRepository;

    @Autowired
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    private static final String MARKED_FOR_DELETION = "markedForDeletion";

    public void processMissingProduct(ProductProjection retailerProduct,
                                      List<ProductProjection> associatedProducts, EtlQueueStatusData statusData) {

        try {
            if (gracePeriodDays < 0) {
                log.error("Grace period days must be 0 or greater.");
                return;
            }

            Optional<Attribute> markedDateAttr = retailerProduct.getMasterVariant().getAttributes().stream()
                    .filter(attr -> MARKED_FOR_DELETION.equals(attr.getName()))
                    .findFirst();

            LocalDate today = LocalDate.now();

            if (markedDateAttr.isEmpty()) {
                handleInitialMarkForDeletion(retailerProduct, associatedProducts,statusData);
                return;
            }

            String dateStr = markedDateAttr.get().getValue().toString().replace("\"", "");
            LocalDate markedDate = LocalDate.parse(dateStr);

            if (markedDate.isBefore(today)) {
                deleteProduct(retailerProduct,statusData);
                sendEmailNotification(retailerProduct.getKey(), "Deleted after grace period");
            } else {
                sendEmailNotification(retailerProduct.getKey(), "Still within grace period");
            }

        } catch (Exception e) {
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
            log.error("Error processing missing product {}: {}", retailerProduct.getKey(), e.getMessage(), e);
        }
    }


    @Transactional
    private void handleInitialMarkForDeletion(ProductProjection retailerProduct,
                                              List<ProductProjection> associatedProducts,EtlQueueStatusData statusData) {
        try {
            LocalDate today = LocalDate.now();
            LocalDate deletionDate = today.plusDays(gracePeriodDays);

            ProductSetAttributeAction setAttrAction = ProductSetAttributeAction.builder()
                    .name(MARKED_FOR_DELETION)
                    .value(deletionDate)
                    .variantId(retailerProduct.getMasterVariant().getId())
                    .build();

            ProductUpdate update = ProductUpdateBuilder.of()
                    .version(retailerProduct.getVersion())
                    .actions(
                            ProductUnpublishAction.of(),
                            setAttrAction
                    )
                    .build();

            Product updatedRetailerProduct = commercetoolsClient.products()
                    .withId(retailerProduct.getId())
                    .post(update)
                    .executeBlocking()
                    .getBody();

            List<Product> products = unpublishAssociatedProducts(associatedProducts,statusData);
            log.info("Unpublished {} leaseable products for retailer {}", products.size(),retailerProduct.getKey());
            log.info("Marked retailer product {} for deletion on {}", retailerProduct.getKey(), deletionDate);
            sendEmailNotification(retailerProduct.getKey(), "Marked for deletion");

            List<String> productKeysToMark = new ArrayList<>(associatedProducts.stream().map(ProductProjection::getKey).toList());
            int records = productDataRepository.markProductsForDeletion(productKeysToMark, Timestamp.from(Instant.now()));
            log.info("Marked {} leaseable products flag in DB for deletion", records);

            if (gracePeriodDays == 0) {
                deleteProduct(updatedRetailerProduct,statusData);
            }

        } catch (Exception e) {
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
            log.error("Error marking product {} for deletion: {}", retailerProduct.getKey(), e.getMessage(), e);
        }
    }

    private Product unpublishProduct(ProductProjection product,EtlQueueStatusData statusData) {
        if (!Boolean.TRUE.equals(product.getPublished())) {
            log.info("Product {} already unpublished", product.getKey());
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
            return null;
        }

        try {
            LocalDate today = LocalDate.now();
            LocalDate deletionDate = today.plusDays(gracePeriodDays);
            ProductUpdate unpublishUpdate = ProductUpdateBuilder.of()
                    .version(product.getVersion())
                    .actions(ProductUnpublishAction.of(),
                            ProductSetAttributeAction.builder()
                                    .name(MARKED_FOR_DELETION)
                                    .value(deletionDate)
                                    .variantId(product.getMasterVariant().getId())
                                    .build())
                    .build();

            Product updatedProduct = commercetoolsClient.products()
                    .withId(product.getId())
                    .post(unpublishUpdate)
                    .executeBlocking().getBody();

            log.info("Unpublished product: {}", product.getKey());
            return updatedProduct;
        } catch (Exception e) {
            log.error("Failed to unpublish product {}: {}", product.getKey(), e.getMessage(), e);
            return null;
        }
    }

    private List<Product> unpublishAssociatedProducts(List<ProductProjection> associatedProducts,EtlQueueStatusData statusData) {
        int totalItems = associatedProducts.size();
        int batchSize = (int) Math.ceil((double) totalItems / threadCount);
        List<List<? extends ProductProjection>> batches = partitionList(associatedProducts, batchSize);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<Future<List<Product>>> futures = new ArrayList<>();
        List<Product> updatedProducts = Collections.synchronizedList(new ArrayList<>());

        for (List<? extends ProductProjection> batch : batches) {
            futures.add(executor.submit(() -> {
                List<Product> batchResults = new ArrayList<>();
                for (ProductProjection product : batch) {
                    Product updated = unpublishProduct(product,statusData);
                    if (updated != null) batchResults.add(updated);
                }
                return batchResults;
            }));
        }

        for (Future<List<Product>> future : futures) {
            try {
                updatedProducts.addAll(future.get());
            } catch (Exception e) {
                log.error("Unpublish batch task failed: {}", e.getMessage(), e);
                updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
            }
        }

        executor.shutdown();
        return updatedProducts;
    }

    private <T extends ProductProjection> List<List<? extends T>> partitionList(List<? extends T> list, int batchSize) {
        List<List<? extends T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    private void deleteProduct(ProductProjection retailerProduct,EtlQueueStatusData statusData) {
        try {
            commercetoolsClient.products().withId(retailerProduct.getId()).delete().withVersion(retailerProduct.getVersion()).executeBlocking();
            log.info("Deleted retailer product {}", retailerProduct.getKey());
        } catch (Exception e) {
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
            log.error("Error deleting retailer product {}: {}", retailerProduct.getKey(), e.getMessage(), e);
        }

    }

    private void deleteProduct(Product retailerProduct,EtlQueueStatusData statusData) {
        try {
            commercetoolsClient.products()
                    .withId(retailerProduct.getId())
                    .delete()
                    .withVersion(retailerProduct.getVersion())
                    .executeBlocking();
            log.info("Deleted retailer product {}", retailerProduct.getKey());
        } catch (Exception e) {
            log.error("Error deleting retailer product {}: {}", retailerProduct.getKey(), e.getMessage(), e);
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
        }

    }

    @Transactional
    public void republishAssociatedProductsInParallel(String retailerKey) {
        String filter = String.format("variants.attributes.retailerProductKey:\"%s\"", retailerKey);
        int limit = 500;
        int offset = 0;
        boolean more = true;

        List<ProductProjection> unpublishedAssociatedProducts = new ArrayList<>();

        while (more) {
            List<ProductProjection> batch = commercetoolsClient.productProjections()
                    .search()
                    .get()
                    .addQueryParam("filter.query", filter)
                    .withStaged(true)
                    .withLimit(limit)
                    .withOffset(offset)
                    .executeBlocking()
                    .getBody()
                    .getResults();

            for (ProductProjection prod : batch) {
                if (!Boolean.TRUE.equals(prod.getPublished())) {
                    unpublishedAssociatedProducts.add(prod);
                }
            }

            offset += limit;
            more = batch.size() == limit;
        }

        if (unpublishedAssociatedProducts.isEmpty()) {
            log.info("No unpublished associated products found for retailerKey: {}", retailerKey);
            return;
        }

        int total = unpublishedAssociatedProducts.size();
        int batchSize = (int) Math.ceil((double) total / threadCount);
        List<List<? extends ProductProjection>> partitions = partitionList(unpublishedAssociatedProducts, batchSize);

        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<Future<List<String>>> futures = new ArrayList<>();

        for (List<? extends ProductProjection> batch : partitions) {
            futures.add(executor.submit(() -> {
                List<String> republishedKeys = new ArrayList<>();
                for (ProductProjection prod : batch) {
                    try {
                        List<ProductUpdateAction> actions = new ArrayList<>();

                        if (!Boolean.TRUE.equals(prod.getPublished()) &&
                                prod.getMasterVariant().getAttributes().stream()
                                        .anyMatch(attr -> MARKED_FOR_DELETION.equals(attr.getName()))) {

                            actions.add(ProductSetAttributeAction.builder()
                                    .name(MARKED_FOR_DELETION)
                                    .value(null)
                                    .variantId(prod.getMasterVariant().getId())
                                    .build());
                        }
                        actions.add(ProductPublishAction.of());
                        commercetoolsClient.products()
                                .withId(prod.getId())
                                .post(ProductUpdate.builder()
                                        .version(prod.getVersion())
                                        .actions(actions)
                                        .build())
                                .executeBlocking();
                        log.info("Republished associated product {}", prod.getKey());
                        republishedKeys.add(prod.getKey());
                    } catch (Exception e) {
                        log.error("Failed to republish associated product {}: {}", prod.getKey(), e.getMessage(), e);
                    }
                }
                return republishedKeys;
            }));
        }

        List<String> allRepublishedKeys = new ArrayList<>();
        for (Future<List<String>> future : futures) {
            try {
                allRepublishedKeys.addAll(future.get());
            } catch (Exception e) {
                log.error("Error in republish thread: {}", e.getMessage(), e);
            }
        }

        executor.shutdown();

        if (!allRepublishedKeys.isEmpty()) {
            int records = productDataRepository.clearMarkedForDeletion(allRepublishedKeys);
            log.info("Cleared markedForDeletion flag in DB for {} products", records);
        }
    }

    private void sendEmailNotification(String productKey, String message) {
        String logMessage = String.format("TriggerLogMonitorMessage:RetailerImport: [%s] for product: %s", message, productKey);
        log.info(logMessage);
    }

    private void updateStatus(EtlQueueStatusData statusData, EtlQueueStatusData.Status status) {
        try {
            statusData.setStatus(status);
            statusData.setLastUpdate(Timestamp.from(Instant.now()));
            etlQueueStatusDataRepository.save(statusData);
            log.info("Successfully updated status to COMPLETED for {}", statusData.getEtlID());
        } catch (Exception e) {
            log.error("Failed to update status to COMPLETED for {}: {}", statusData.getEtlID(), e.getMessage(), e);
        }
    }
}
