package com.progleasing.marketplace.etl.service;

import com.commercetools.api.client.ProjectApiRoot;
import com.commercetools.api.models.common.Image;
import com.commercetools.api.models.common.LocalizedString;
import com.commercetools.api.models.product.*;
import com.commercetools.api.models.product_type.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.model.ProductModel;

import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import io.micrometer.common.util.StringUtils;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import org.springframework.beans.factory.annotation.Value;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RetailerImportService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final S3Client s3Client;
    private final ProductDeletionService productDeletionService;

    private final EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @Qualifier("ctApiClient")
    @Autowired
    private ProjectApiRoot commercetoolsClient;

    @Value("${market.place.commercetools.projectKey}")
    private String projectKey;

    @Value("${market.place.commercetools.clientId}")
    private String clientId;

    @Value("${market.place.commercetools.clientSecret}")
    private String clientSecret;

    @Value("${market.place.retailer.thread.count}")
    private int threadCount;

    @Value("${market.place.retailer.productType.key}")
    private String productTypeKey;

    @Value("${market.place.retailer.path.prefix}")
    private String pathPrefix;

    @Value("${market.place.retailer.path.archive}")
    private String pathArchive;

    @Value("${market.place.retailer.path.images}")
    private String pathImages;

    private static final String MARKED_FOR_DELETION = "markedForDeletion";

    private final Map<String, Map<String, String>> enumOptionsCache = new ConcurrentHashMap<>();

    @Autowired
    public RetailerImportService(ProductDeletionService productDeletionService, EtlQueueStatusDataRepository etlQueueStatusDataRepository, S3Client s3Client) {
        this.s3Client = s3Client;
        this.productDeletionService = productDeletionService;
        this.etlQueueStatusDataRepository = etlQueueStatusDataRepository;
    }

    private Map<String, ProductProjection> preloadRetailerProducts() {
        Map<String, ProductProjection> productMap = new HashMap<>();
        try {
            String productTypeId = commercetoolsClient.productTypes()
                    .withKey(productTypeKey)
                    .get()
                    .executeBlocking()
                    .getBody()
                    .getId();

            int limit = 500;
            int offset = 0;
            boolean more = true;

            while (more) {
                List<ProductProjection> products = commercetoolsClient.productProjections()
                        .get()
                        .withWhere(String.format("productType(id=\"%s\")", productTypeId))
                        .withStaged(true)
                        .withLimit(limit)
                        .withOffset(offset)
                        .executeBlocking()
                        .getBody()
                        .getResults();

                for (ProductProjection product : products) {
                    productMap.put(product.getKey(), product);
                }

                offset += limit;
                more = products.size() == limit;
            }
        } catch (Exception e) {
            log.error("Error preloading products: " + e.getMessage());
        }
        return productMap;
    }

    public void importRetailerData(String payload, String messageId) {
        try {
            Map<String, ProductProjection> allRetailerProducts = preloadRetailerProducts();
            JsonNode root = objectMapper.readTree(payload);
            JsonNode records = root.get("Records");

            if (records != null && records.isArray()) {
                for (JsonNode record : records) {
                    String requestId = null;
                    JsonNode responseElements = record.get("responseElements");
                    if (responseElements != null && responseElements.has("x-amz-request-id")) {
                        requestId = responseElements.get("x-amz-request-id").asText();
                        log.info("x-amz-request-id: {}", requestId);
                    }
                    JsonNode s3 = record.get("s3");
                    if (s3 != null) {
                        String bucketName = s3.get("bucket").get("name").asText();
                        String objectKey = s3.get("object").get("key").asText();

                        if (objectKey.startsWith(pathPrefix) && objectKey.endsWith(".csv")) {
                            log.info("Processing file: " + objectKey);
                            EtlQueueStatusData statusData = updateStatusQueue(requestId,messageId,EtlQueueStatusData.Status.STARTED);
                            processS3File(bucketName, objectKey, allRetailerProducts,statusData);
                        } else {
                            log.info("Skipping non-CSV or irrelevant file: " + objectKey);
                        }
                    }
                }
            } else {
                log.error("No valid S3 records found in payload.");
            }
        } catch (Exception e) {
            log.error("Error parsing payload: " + e.getMessage());
        }
    }

    private void processS3File(String bucketName, String key, Map<String, ProductProjection> allRetailerProducts,EtlQueueStatusData statusData) {
        Set<String> seenProductKeys = new HashSet<>();
        try (ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(
                GetObjectRequest.builder().bucket(bucketName).key(key).build());
             BufferedReader reader = new BufferedReader(new InputStreamReader(s3Object))) {

            CSVFormat format = CSVFormat.Builder.create(CSVFormat.DEFAULT)
                    .setHeader()
                    .setSkipHeaderRecord(true)
                    .build();

            Iterable<CSVRecord> csvRecords = format.parse(reader);

            List<CSVRecord> recordList = new ArrayList<>();
            for (CSVRecord csv : csvRecords) {
                recordList.add(csv);
            }

            int batchSize = (int) Math.ceil((double) recordList.size() / threadCount);

            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            List<Future<Void>> futures = new ArrayList<>();

            for (int i = 0; i < recordList.size(); i += batchSize) {
                int toIndex = Math.min(i + batchSize, recordList.size());
                List<CSVRecord> batch = recordList.subList(i, toIndex);

                futures.add(executor.submit(() -> {
                    for (CSVRecord csv : batch) {
                        try {
                            ProductModel newProduct = new ProductModel();
                            newProduct.setRetailerName(csv.get("retailerName").trim());
                            newProduct.setGrandparentID(csv.get("grandparentID").trim());
                            newProduct.setParentIDs(csv.get("parentIDs").trim());
                            newProduct.setStoreType(csv.get("storeType").toLowerCase().trim());
                            newProduct.setFeedType(csv.get("feedType").toLowerCase().trim());
                            newProduct.setPartnerType(csv.get("partnerType").toLowerCase().trim());

                            String productKey = newProduct.getRetailerName().toLowerCase().replaceAll("[^a-z0-9]", "");

                            synchronized (seenProductKeys) {
                                seenProductKeys.add(productKey);
                            }

                            Product product = createOrUpdateProduct(productKey, newProduct, allRetailerProducts,statusData);
                            if (product != null) {
                                uploadRetailerImage(product, bucketName, productKey, statusData);
                            }
                        } catch (Exception e) {
                            log.error("Error processing CSV record in batch: " + e);
                            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
                        }
                    }
                    return null;
                }));
            }

            for (Future<Void> future : futures) {
                try {
                    future.get();
                } catch (Exception e) {
                    log.error("Error in thread execution: " + e.getMessage());
                    updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
                }
            }
            executor.shutdown();

            for (Map.Entry<String, ProductProjection> entry : allRetailerProducts.entrySet()) {
                if (!seenProductKeys.contains(entry.getKey())) {
                    handleMissingProduct(entry.getValue(), statusData);
                }
            }

            String destinationKey = key.replace(pathPrefix, pathArchive);
            s3Client.copyObject(builder -> builder.sourceBucket(bucketName).sourceKey(key).destinationBucket(bucketName).destinationKey(destinationKey));
            s3Client.deleteObject(builder -> builder.bucket(bucketName).key(key));
            updateStatus(statusData, EtlQueueStatusData.Status.COMPLETED);
        } catch (Exception e) {
            log.error("Error processing file from S3: " + e.getMessage());
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
        }
    }

    private void handleMissingProduct(ProductProjection product,EtlQueueStatusData statusData) {
        try {
            String retailerKey = product.getKey();

            String filter = String.format("variants.attributes.retailerProductKey:\"%s\"", retailerKey);

            int offset = 0;
            int limit = 500;
            List<ProductProjection> associatedProducts = new ArrayList<>();
            boolean more = true;

            while (more) {
                List<ProductProjection> batch = commercetoolsClient.productProjections().search()
                        .get()
                        .addQueryParam("filter.query", filter)
                        .withLimit(limit)
                        .withStaged(true)
                        .withOffset(offset)
                        .executeBlocking()
                        .getBody()
                        .getResults();

                associatedProducts.addAll(batch);
                offset += limit;
                more = batch.size() == limit;
            }

            productDeletionService.processMissingProduct(product, associatedProducts,statusData);

        } catch (Exception e) {
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
            log.error("Error handling missing product: " + e.getMessage());
        }
    }

    private Product createOrUpdateProduct(String productKey, ProductModel newProduct, Map<String, ProductProjection> existingRetailerProducts,EtlQueueStatusData statusData) {
        List<Attribute> attributes = new ArrayList<>();
        if (!newProduct.getGrandparentID().isEmpty())
            attributes.add(AttributeBuilder.of().name("retailerGrandparentID").value(newProduct.getGrandparentID()).build());
        if (!newProduct.getParentIDs().isEmpty())
            attributes.add(AttributeBuilder.of().name("retailerParentIDs").value(newProduct.getParentIDs()).build());
        if (!newProduct.getStoreType().isEmpty())
            attributes.add(AttributeBuilder.of()
                    .name("retailerStoreType")
                    .value(createEnumValue("retailerStoreType", newProduct.getStoreType()))
                    .build());

        if (!newProduct.getFeedType().isEmpty())
            attributes.add(AttributeBuilder.of()
                    .name("retailerFeedType")
                    .value(createEnumValue("retailerFeedType", newProduct.getFeedType()))
                    .build());

        if (!newProduct.getPartnerType().isEmpty())
            attributes.add(AttributeBuilder.of()
                    .name("retailerPartnerType")
                    .value(createEnumValue("retailerPartnerType", newProduct.getPartnerType()))
                    .build());

        try {
            if (existingRetailerProducts.containsKey(productKey)) {
                ProductProjection existing = existingRetailerProducts.get(productKey);
                List<ProductUpdateAction> actions = new ArrayList<>();

                for (Image image : existing.getMasterVariant().getImages()) {
                    actions.add(ProductRemoveImageAction.builder()
                            .variantId(existing.getMasterVariant().getId())
                            .imageUrl(image.getUrl())
                            .build());
                }

                if (!Boolean.TRUE.equals(existing.getPublished())
                        && existing.getMasterVariant().getAttributes().stream().anyMatch(attr -> MARKED_FOR_DELETION.equals(attr.getName()))) {

                    actions.add(ProductSetAttributeAction.builder()
                            .name(MARKED_FOR_DELETION)
                            .variantId(existing.getMasterVariant().getId())
                            .build());
                }

                for (Attribute attr : attributes) {
                    actions.add(ProductSetAttributeAction.builder()
                            .variantId(existing.getMasterVariant().getId())
                            .name(attr.getName())
                            .value(attr.getValue())
                            .build());
                }

                actions.add(ProductChangeNameAction.builder()
                        .name(LocalizedString.of(Locale.forLanguageTag("en-US"), newProduct.getRetailerName()))
                        .build());

                actions.add(ProductPublishAction.of());

                ProductUpdate update = ProductUpdate.builder()
                        .version(existing.getVersion())
                        .actions(actions)
                        .build();

                Product updatedRetailerProduct = commercetoolsClient.products()
                        .withId(existing.getId())
                        .post(update)
                        .executeBlocking()
                        .getBody();

                if (!Boolean.TRUE.equals(existing.getPublished())) {
                    productDeletionService.republishAssociatedProductsInParallel(productKey);
                }

                return updatedRetailerProduct;
            }

            ProductDraft draft = ProductDraftBuilder.of()
                    .key(productKey)
                    .name(LocalizedString.of(Locale.forLanguageTag("en-US"), newProduct.getRetailerName()))
                    .slug(LocalizedString.of(Locale.forLanguageTag("en-US"), productKey))
                    .productType(ProductTypeResourceIdentifierBuilder.of().key(productTypeKey).build())
                    .masterVariant(ProductVariantDraftBuilder.of()
                            .key(productKey + "_retailer")
                            .sku(productKey + "_retailer")
                            .attributes(attributes)
                            .build())
                    .build();

            Product createdProduct = commercetoolsClient.products()
                    .post(draft)
                    .executeBlocking()
                    .getBody();

            commercetoolsClient.products()
                    .withId(createdProduct.getId())
                    .post(ProductUpdate.builder()
                            .version(createdProduct.getVersion())
                            .actions(ProductPublishAction.of())
                            .build())
                    .executeBlocking();

            return createdProduct;

        } catch (Exception e) {
            log.error("Error creating/updating product " + productKey + ": " + e.getMessage());
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
            return null;
        }
    }

    private void uploadRetailerImage(Product product, String bucket, String retailerKey,EtlQueueStatusData statusData) {
        String key = pathImages + retailerKey + ".png";
        File tempFile = null;
        try {

            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucket)
                    .key(key)
                    .build();
            ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);

            tempFile = File.createTempFile("s3image-", ".png");
            Files.copy(s3Object, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

            String contentType = Files.probeContentType(tempFile.toPath());
            if (contentType == null) contentType = "application/octet-stream";

            String sku = product.getMasterData().getCurrent().getMasterVariant().getSku();
            Product updatedProduct = commercetoolsClient.products()
                    .withId(product.getId())
                    .images()
                    .post(tempFile)
                    .contentType(contentType)
                    .withSku(sku)
                    .executeBlocking()
                    .getBody();

            commercetoolsClient.products()
                    .withId(updatedProduct.getId())
                    .post(ProductUpdate.builder()
                            .version(updatedProduct.getVersion())
                            .actions(ProductPublishAction.of())
                            .build())
                    .executeBlocking().getBody();

        } catch (Exception e) {
            log.error("Failed to upload image: " + key + " - " + e.getMessage());
            updateStatus(statusData, EtlQueueStatusData.Status.ERROR_RESTARTING);
        } finally {
            if (tempFile != null && tempFile.exists()) tempFile.delete();
        }
    }

    public Map<String, String> getEnumOptionsForAttribute(String attributeName) {
        return enumOptionsCache.computeIfAbsent(attributeName, (String attr) -> {
            try {
                ProductType pt = commercetoolsClient.productTypes()
                        .withKey(productTypeKey)
                        .get()
                        .executeBlocking()
                        .getBody();

                return pt.getAttributes().stream()
                        .filter(a -> a.getName().equals(attr))
                        .findFirst()
                        .flatMap(attribute -> {
                            if (attribute.getType() instanceof AttributeEnumType enumType) {
                                List<AttributePlainEnumValue> values = enumType.getValues();
                                return Optional.of(values.stream()
                                        .collect(Collectors.toMap(
                                                v -> v.getKey().toLowerCase(Locale.ROOT),
                                                AttributePlainEnumValue::getLabel
                                        )));
                            }
                            return Optional.empty();
                        })
                        .orElse(Collections.emptyMap());
            } catch (Exception e) {
                log.error("Failed loading enum options for '{}': {}", attr, e.getMessage());
                return Collections.emptyMap();
            }
        });
    }

    AttributePlainEnumValue createEnumValue(String attributeName, String rawKey) {
        if (StringUtils.isEmpty(rawKey)) {
            rawKey = "na";
        }
        Map<String, String> enumOptions = getEnumOptionsForAttribute(attributeName);
        String normalizedKey = rawKey.trim().toLowerCase(Locale.ROOT);

        String keyToUse = enumOptions.keySet().stream()
                .map(String::toLowerCase)
                .filter(option -> option.contains(normalizedKey) || normalizedKey.contains(option))
                .findFirst()
                .orElse("na");
        String labelToUse = enumOptions.getOrDefault(keyToUse, "N/A");

        return AttributePlainEnumValueBuilder.of()
                .key(keyToUse)
                .label(labelToUse)
                .build();
    }

    @Transactional
    private EtlQueueStatusData updateStatusQueue(String requestId, String messageId, EtlQueueStatusData.Status status) {
        String messageData = String.format(
                "%s",
                messageId
        );
        EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.RETAILER_IMPORT;
        Optional<EtlQueueStatusData> existing =
                etlQueueStatusDataRepository.findByEtlIDAndSqsMessageDataAndEtlStep(
                        requestId,
                        messageData,
                        etlStep
                );
        EtlQueueStatusData etlQueueStatusData;
        if (existing.isPresent()) {
            etlQueueStatusData = existing.get();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        } else {
            etlQueueStatusData = new EtlQueueStatusData();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setEtlStep(etlStep);
            etlQueueStatusData.setEtlID(requestId);
            etlQueueStatusData.setSqsMessageData(messageData);
            if (status == EtlQueueStatusData.Status.STARTED) {
                etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
            }
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        }
        etlQueueStatusDataRepository.save(etlQueueStatusData);
        return etlQueueStatusData;
    }

    private void updateStatus(EtlQueueStatusData statusData, EtlQueueStatusData.Status status) {
        try {
            statusData.setStatus(status);
            statusData.setLastUpdate(Timestamp.from(Instant.now()));
            etlQueueStatusDataRepository.save(statusData);
            log.info("Successfully updated status to COMPLETED for {}", statusData.getEtlID());
        } catch (Exception e) {
            log.error("Failed to update status to COMPLETED for {}: {}", statusData.getEtlID(), e.getMessage(), e);
        }
    }
}
