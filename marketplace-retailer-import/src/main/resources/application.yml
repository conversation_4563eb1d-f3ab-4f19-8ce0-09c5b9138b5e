spring:
  application:
    name: progressive.leasing
  datasource:
    url: ${DB_URL:****************************************}
    username: ${DB-USER_NAME:admin}
    password: ${DB_PASSWORD:admin123}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-east-1}
      credentials:
        access-key: ${AWS_ACCESS_KEY}
        secret-key: ${AWS_SECRET_KEY}

market:
  place:
    commercetools:
      projectKey: ${PROJECT_KEY}
      clientId: ${CLIENT_ID}
      clientSecret: ${CLIENT_SECRET}
      apiUrl: ${API_URL}
      authUrl: ${AUTH_URL}
      importApiUrl: ${IMPORT_API_URL}
      scopes: ${SCOPES}

    retailer:
      thread:
        count: ${THREAD_COUNT}
      grace:
        days: ${GRACE_DAYS}

aws:
  sqs:
    retailer-importer:
      listener:
        enabled: ${RETAILER_IMPORTER_LISTENER_ENABLED:true}
      queue-name: ${RETAILER_IMPORTER_QUEUE:retailer-importer-queue}
    concurrent-message: ${AMAZON_CONCURRENT_MESSAGE:10}


