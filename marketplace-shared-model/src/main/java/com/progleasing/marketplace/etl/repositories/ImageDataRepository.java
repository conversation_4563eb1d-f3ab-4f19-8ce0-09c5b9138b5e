package com.progleasing.marketplace.etl.repositories;

import com.progleasing.marketplace.etl.dto.ImageUrlsByProductId;
import com.progleasing.marketplace.etl.entity.ImageData;
import com.progleasing.marketplace.etl.entity.ImageDataId;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ImageDataRepository extends JpaRepository<ImageData, ImageDataId> {
    @Query("SELECT i.id AS id, i.imageUrl AS imageUrl FROM ImageData i WHERE i.id IN :ids")
    List<ImageUrlsByProductId> findImageUrlsByProductIds(@Param("ids") List<String> ids);

    @Transactional
    @Modifying
    @Query("DELETE FROM ImageData i WHERE i.id IN :ids")
    void deleteAllByIdIn(@Param("ids") List<String> ids);
}
