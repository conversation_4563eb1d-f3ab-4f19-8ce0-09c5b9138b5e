package com.progleasing.marketplace.etl.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Entity
@Table(name = "etl_queue_status",
        uniqueConstraints = @UniqueConstraint(columnNames = {"etl_id", "retailer_search",
                "etl_step", "message_id"}),
        indexes = {
        @Index(name = "idx_etl_sqs", columnList = "etlID, sqsMessageData")
})
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EtlQueueStatusData {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String etlID;

    private String messageId;

    @Column
    private Timestamp etlStart;

    private String retailerSearch;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private EtlStep etlStep;


    @Column(nullable = false, length = 1000)
    private String sqsMessageData;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PollingStatus processed;

    @Column
    private Timestamp lastUpdate;

    public enum EtlStep {
        PRODUCT_IMPORT,
        PRODUCT_SEARCH,
        PRODUCT_LEASIBILITY_EXPORT,
        PRODUCT_LEASIBILITY_IMPORT,
        PRODUCT_EXPORT,
        IMAGE_UPLOAD,
        RETAILER_IMPORT
    }

    public enum Status {
        NOT_STARTED,
        STARTED,
        COMPLETED,
        ERROR_RESTARTING
    }

    public enum PollingStatus {
        ERROR,
        SUCCESS,
        IN_PROGRESS
    }
}
