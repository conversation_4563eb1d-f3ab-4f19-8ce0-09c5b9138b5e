package com.progleasing.marketplace.etl.repositories;

import com.progleasing.marketplace.etl.entity.ProductData;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface ProductDataRepository extends JpaRepository<ProductData, String> {

    @Query("""
    SELECT c FROM ProductData c WHERE FUNCTION('DATE', c.lastModified) >= FUNCTION('DATE', :start)
    AND FUNCTION('DATE', c.lastModified) <= FUNCTION('DATE', :end) AND c.leasable = true AND c.markedForDeletion IS NULL
    """)
    Page<ProductData> findByLastModified(
            @Param("start") Timestamp start,
            @Param("end") Timestamp end,
            Pageable pageable
    );

    @Query(
            value = "SELECT * FROM pl_product_data p WHERE DATE(p.marked_for_deletion) = :currentDate",
            nativeQuery = true
    )
    List<ProductData> findMarkedForDeletion(@Param("currentDate") LocalDate currentDate);

    @Query("SELECT p FROM ProductData p WHERE p.searchTerm = :searchTerm AND p.feedSource = :feedSource")
    List<ProductData> findProductsBySearchTerm(@Param("searchTerm") String searchTerm, @Param("feedSource") ProductData.FeedSource feedSource);

    @Modifying
    @Transactional
    @Query("UPDATE ProductData p SET p.markedForDeletion = :timestamp WHERE p.id IN :productIds")
    void markForDeletion(@Param("productIds") List<String> productIds, @Param("timestamp") Timestamp timestamp);
    @Query("SELECT p FROM ProductData p WHERE p.markedForDeletion = :start AND p.lastModified >= :start AND p.lastModified < :end")
    List<ProductData> findMarkedForDeletion(@Param("start") Timestamp start,
                                            @Param("end") Timestamp end);

    @Query("SELECT p FROM ProductData p WHERE p.markedForDeletion IS NULL")
    Page<ProductData> findAllNotMarkedForDeletion(Pageable pageable);

    @Query("SELECT COUNT(p) FROM ProductData p WHERE p.markedForDeletion IS NULL")
    long countByMarkedForDeletionIsNull();

    @Transactional
    @Modifying
    @Query("UPDATE ProductData p SET p.markedForDeletion = :date WHERE p.id IN :productKeys")
    int markProductsForDeletion(@Param("productKeys") List<String> productKeys,
                                @Param("date") Timestamp date);

    @Transactional
    @Modifying
    @Query("UPDATE ProductData p SET p.markedForDeletion = NULL WHERE p.id IN :productKeys")
    int clearMarkedForDeletion(@Param("productKeys") List<String> productKeys);

}
