package com.progleasing.marketplace.etl.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "pl_retailer_partner_data")
@Data
public class RetailerPartnerData {

    @Id
    private Long id;

    @Column(name = "retailer_name", columnDefinition = "TEXT")
    private String retailerName;

    @Column(name = "partner_id", columnDefinition = "TEXT")
    private String partnerId;

}