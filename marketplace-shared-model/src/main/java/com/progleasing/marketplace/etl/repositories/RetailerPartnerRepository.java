package com.progleasing.marketplace.etl.repositories;

import com.progleasing.marketplace.etl.entity.RetailerPartnerData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RetailerPartnerRepository extends JpaRepository<RetailerPartnerData, Long> {
    List<RetailerPartnerData> findByRetailerNameIn(List<String> names);
}