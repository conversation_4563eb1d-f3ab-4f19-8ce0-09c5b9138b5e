package com.progleasing.marketplace.etl.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Entity
@Data
@Table(name = "pl_price_data")
public class PriceData {
    @Id
    private String id;

    private BigDecimal price;

    @Column(name = "sale_price")
    private BigDecimal salePrice;

    @Column(name = "last_modified")
    private Timestamp lastModified;

    @Column(name = "last_sent_to_CT")
    private Timestamp lastSentToCT;
}