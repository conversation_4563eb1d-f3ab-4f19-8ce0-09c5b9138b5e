package com.progleasing.marketplace.etl.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ETLMessageDTO {
    private String categoryKey;
    private String l1CategoryName;
    private String l2CategoryName;
    private String searchTerm;
    private String retailerName;
    private String retailerKey;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private String referenceId;
}
