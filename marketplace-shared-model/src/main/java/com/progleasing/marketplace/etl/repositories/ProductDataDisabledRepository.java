package com.progleasing.marketplace.etl.repositories;

import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.entity.ProductDataDisabled;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ProductDataDisabledRepository extends JpaRepository<ProductDataDisabled, String> {

    @Query("SELECT p FROM ProductDataDisabled p WHERE p.searchTerm = :searchTerm AND p.l1CategoryName = :l1CategoryName AND p.l2CategoryName = :l2CategoryName AND p.feedSource = :feedSource")
    List<ProductDataDisabled> findProductsByFeedSource(@Param("searchTerm") String searchTerm,
                                                       @Param("l1CategoryName") String l1CategoryName,
                                                       @Param("l2CategoryName") String l2CategoryName,
                                                       @Param("feedSource") ProductData.FeedSource feedSource);
}
