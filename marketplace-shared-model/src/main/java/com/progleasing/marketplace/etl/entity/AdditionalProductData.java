package com.progleasing.marketplace.etl.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Entity
@Table(name = "pl_extra_product_data")
@Data
public class AdditionalProductData {
    @Id
    private String id;

    private String color;

    private String size;

    @Column(name = "features", columnDefinition = "TEXT")
    private String features;

    @Column(name = "technical_info", columnDefinition = "TEXT")
    private String technicalInfo;

    @Column(name = "other_info", columnDefinition = "TEXT")
    private String otherInfo;

    @Column(name = "item_part_number")
    private String itemPartNumber;

    private String model;

    @Column(name = "last_modified")
    private Timestamp lastModified;

    @Column(name = "last_sent_to_CT")
    private Timestamp lastSentToCT;

}