package com.progleasing.marketplace.etl.repositories;

import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

@Repository
public interface EtlQueueStatusDataRepository extends JpaRepository<EtlQueueStatusData, Long> {


    Optional<EtlQueueStatusData> findByEtlIDAndSqsMessageDataAndEtlStep(
                    String etlID,
            String sqsMessageData,
            EtlQueueStatusData.EtlStep etlStep
    );

    Optional<EtlQueueStatusData> findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(
            String etlID,
            String retailerSearch,
            EtlQueueStatusData.EtlStep etlStep,
            String messageId
    );

    Optional<EtlQueueStatusData> findByEtlIDAndEtlStep(
            String etlID,
            EtlQueueStatusData.EtlStep etlStep
    );

    List<EtlQueueStatusData> findAllByEtlIDAndEtlStep(
            String etlID,
            EtlQueueStatusData.EtlStep etlStep
    );

    Optional<EtlQueueStatusData> findAllByEtlIDAndMessageId(
            String etlID,
            String messageId
    );

    List<EtlQueueStatusData> findByEtlID(String etlId);

    // Define the method to delete records older than a certain timestamp
    void deleteByLastUpdateBefore(Timestamp cutoffTime);

    @Query("SELECT e FROM EtlQueueStatusData e WHERE e.etlID = :etlID AND e.retailerSearch IN ('impact', 'google', 'sovrn', 'cj', 'amazon')")
    List<EtlQueueStatusData> findByEtlIDAndRetailerSearch(@Param("etlID") String etlId);

    @Query("SELECT e FROM EtlQueueStatusData e " +
            "WHERE e.etlID = :etlID AND e.etlStep = :etlStep AND e.status = :status " +
            "AND e.lastUpdate > :etlStart")
    Optional<EtlQueueStatusData> findAllByEtlIDAndEtlStepAndStatusAndLastUpdateAfter(
            @Param("etlID") String etlId,
            @Param("etlStep") EtlQueueStatusData.EtlStep etlStep,
            @Param("status") EtlQueueStatusData.Status status,
            @Param("etlStart") Timestamp etlStart
    );


    @Query("SELECT e FROM EtlQueueStatusData e " +
            "WHERE e.etlStep = :etlStep AND e.status = :status " +
            "AND e.lastUpdate > :etlStart")
    List<EtlQueueStatusData> findAllByEtlStepAndStatusAndLastUpdateAfter(
            @Param("etlStep") EtlQueueStatusData.EtlStep etlStep,
            @Param("status") EtlQueueStatusData.Status status,
            @Param("etlStart") Timestamp etlStart
    );

    @Query(value = """
    SELECT * FROM etl_queue_status WHERE EXTRACT(EPOCH FROM (last_update - etl_start)) > :thresholdInSeconds
    AND etl_start >= :startAfter AND status <> 'COMPLETED'""", nativeQuery = true)
    List<EtlQueueStatusData> findLongRunningProcesses(
            @Param("thresholdInSeconds") long thresholdInSeconds,
            @Param("startAfter") Timestamp startAfter
    );

    // Method to find records by etlId, status, and etlStep
    List<EtlQueueStatusData> findByEtlIDAndStatusAndEtlStepIn(String etlID, EtlQueueStatusData.Status status, List<EtlQueueStatusData.EtlStep> etlSteps);
}
