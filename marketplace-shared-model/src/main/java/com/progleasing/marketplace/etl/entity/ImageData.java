package com.progleasing.marketplace.etl.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.sql.Timestamp;

@Entity
@Table(name = "pl_image_data")
@Data
@IdClass(ImageDataId.class)
public class ImageData {
    @Id
    @Column(name = "id", nullable = false, length = 255)
    private String id;

    @Id
    @Column(name = "image_url", nullable = false, length = 512)
    private String imageUrl;


    @Column(name = "last_uploaded")
    private Timestamp lastUploaded;

    @Column(name = "last_modified")
    private Timestamp lastModified;

    @Column(name = "last_sent_to_CT")
    private Timestamp lastSentToCT;
}
