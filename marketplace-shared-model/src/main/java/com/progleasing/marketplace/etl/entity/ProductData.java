package com.progleasing.marketplace.etl.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Entity
@Table(name = "pl_product_data")
@Data
public class ProductData {
    @Id
    private String id;

    @Column(length = 500)
    private String name;

    @Column(length = 500)
    private String description;

    @Column(name = "long_description", columnDefinition = "TEXT")
    private String longDescription;

    @Column(name = "retailer_name")
    private String retailerName;

    @Column(name = "retailer_key")
    private String retailerKey;

    private String brand;

    @Column(name = "rating_average")
    private BigDecimal ratingAverage;

    @Column(name = "rating_count")
    private int ratingCount;

    @Column(name = "product_tracking_url",  columnDefinition = "TEXT")
    private String productTrackingUrl;

    @Column(name = "affiliate_add_to_cart_url",  columnDefinition = "TEXT")
    private String affiliateAddToCartUrl;

    @Column(name = "last_modified")
    private Timestamp lastModified;

    @Column(name = "last_sent_to_CT")
    private Timestamp lastSentToCT;

    @Column(name = "category_key")
    private String categoryKey;

    @Column(name = "l1_category_name")
    private String l1CategoryName;

    @Column(name = "l2_category_name")
    private String l2CategoryName;

    private boolean leasable;

    @Column(name = "search_term")
    private String searchTerm;

    @Column(name = "marked_for_deletion")
    private Timestamp markedForDeletion;

    @Enumerated(EnumType.STRING)
    @Column(name = "feed_source")
    private ProductData.FeedSource feedSource;


    public enum FeedSource {
        amazon,
        google,
        impact,
        cj,
        sovrn
    }

}