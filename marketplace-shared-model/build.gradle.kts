plugins {
	id("org.springframework.boot") apply false
	id("io.spring.dependency-management")
	id("java")
}

group = "com.progleasing.marketplace"
version = "1.0.1"
description = "Module used for storing common models for ETL"

repositories {
	mavenCentral()
}
val springBootVersion = "3.4.4"
val lombokVersion = "1.18.32"

dependencies {
	implementation("org.springframework.boot:spring-boot-starter:${springBootVersion}")
	implementation("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
	implementation("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}")
	implementation("org.postgresql:postgresql:42.7.3")
	compileOnly("org.projectlombok:lombok:$lombokVersion")
	annotationProcessor("org.projectlombok:lombok:$lombokVersion")
}

java {
	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}
