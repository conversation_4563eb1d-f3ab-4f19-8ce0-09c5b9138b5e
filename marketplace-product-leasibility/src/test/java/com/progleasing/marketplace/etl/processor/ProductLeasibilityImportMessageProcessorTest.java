package com.progleasing.marketplace.etl.processor;

import com.progleasing.marketplace.etl.service.ProductLeasibilityImporterService;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.messaging.support.MessageHeaderAccessor;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
public class ProductLeasibilityImportMessageProcessorTest {

    @Mock
    private ProductLeasibilityImporterService service;

    private ProductLeasibilityImportMessageProcessor processor;

    @Mock
    private AcknowledgementCallback acknowledgementCallback;


    @BeforeEach
    void setUp() {
        Executor directExecutor = Runnable::run;
        processor = new ProductLeasibilityImportMessageProcessor(service, directExecutor);
    }

    @Test
    void handle_shouldProcessAllMessagesAndAcknowledge() throws Exception {
        String payload1 = "{\"referenceId\":\"ref-1\"}";
        String payload2 = "{\"referenceId\":\"ref-2\"}";

        Message<String> message1 = createMockMessage(payload1);
        Message<String> message2 = createMockMessage(payload2);

        List<Message<String>> messages = Arrays.asList(message1, message2);

        doNothing().when(service).importLeasibilityData(eq(payload1), anyString());
        doNothing().when(service).importLeasibilityData(eq(payload2), anyString());

        when(acknowledgementCallback.onAcknowledge(any(Message.class))).thenReturn(CompletableFuture.completedFuture(null));

        processor.handle(messages);

        verify(service, times(1)).importLeasibilityData(eq(payload1), anyString());
        verify(service, times(1)).importLeasibilityData(eq(payload2), anyString());

        verify(acknowledgementCallback, times(1)).onAcknowledge(message1);
        verify(acknowledgementCallback, times(1)).onAcknowledge(message2);
    }

    @Test
    void handle_whenServiceThrowsException_shouldContinueProcessingAndOnlyAcknowledgeSuccessful() throws Exception {
        String payload1 = "{\"referenceId\":\"ref-1\"}";
        String payload2 = "{\"referenceId\":\"ref-2\"}";

        Message<String> message1 = createMockMessage(payload1);
        Message<String> message2 = createMockMessage(payload2);

        List<Message<String>> messages = Arrays.asList(message1, message2);

        doThrow(new RuntimeException("Simulated error")).when(service).importLeasibilityData(eq(payload1), anyString());
        doNothing().when(service).importLeasibilityData(eq(payload2), anyString());

        when(acknowledgementCallback.onAcknowledge(message2)).thenReturn(CompletableFuture.completedFuture(null));

        processor.handle(messages);

        verify(service, times(1)).importLeasibilityData(eq(payload1), anyString());
        verify(service, times(1)).importLeasibilityData(eq(payload2), anyString());

        verify(acknowledgementCallback, never()).onAcknowledge(message1);
        verify(acknowledgementCallback, times(1)).onAcknowledge(message2);
    }

    @Test
    void handle_withEmptyMessageList_shouldNotProcessOrAcknowledge() throws Exception {
        processor.handle(Collections.emptyList());

        verifyNoInteractions(service);
        verifyNoInteractions(acknowledgementCallback);
    }

    private Message<String> createMockMessage(String payload) {
        UUID messageId = UUID.randomUUID();

        MessageHeaderAccessor headerAccessor = new MessageHeaderAccessor();
        headerAccessor.setHeader("AcknowledgementCallback", acknowledgementCallback);

        headerAccessor.setLeaveMutable(false);

        return MessageBuilder.withPayload(payload)
                .setHeaders(headerAccessor)
                .build();
    }
}
