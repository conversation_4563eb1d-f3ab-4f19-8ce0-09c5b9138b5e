package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.model.LeasibilityExportMessageDTO;
import com.progleasing.marketplace.etl.repositories.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.*;

import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(org.mockito.junit.jupiter.MockitoExtension.class)
class ProductLeasibilityExporterCsvServiceTest {

    @Mock private ProductDataRepository productDataRepository;
    @Mock private AdditionalProductDataRepository additionalProductDataRepository;
    @Mock private PriceDataRepository priceDataRepository;
    @Mock private S3Client s3Client;
    @Mock private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @InjectMocks
    private ProductLeasibilityExporterCsvService service;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(service, "s3Bucket", "dummy-bucket");
        ReflectionTestUtils.setField(service, "s3Folder", "exports/");
        ReflectionTestUtils.setField(service, "maxRecordsPerFile", 2);
        ReflectionTestUtils.setField(service, "maxParallelism", 2);
    }

    @Test
    void normalizeS3Folder_shouldAppendSlash() {
        ReflectionTestUtils.setField(service, "s3Folder", "folder");

        service.normalizeS3Folder();

        String result = (String) ReflectionTestUtils.getField(service, "s3Folder");
        assertEquals("folder/", result);
    }

    @Test
    void exportProductsToCsvAndUploadToS3_shouldExportSuccessfully() throws Exception {
        LeasibilityExportMessageDTO dto = new LeasibilityExportMessageDTO();
        dto.setReferenceId("ref-1");
        String payload = objectMapper.writeValueAsString(dto);
        String messageId = "msg-123";

        ProductData product = new ProductData();
        product.setId("prod-1");
        product.setName("Test Product");
        product.setRetailerName("Retailer A");
        product.setFeedSource(ProductData.FeedSource.google);

        AdditionalProductData additional = new AdditionalProductData();
        additional.setId("prod-1");
        additional.setModel("Model123");

        PriceData price = new PriceData();
        price.setId("prod-1");
        price.setPrice(new BigDecimal("100.00"));
        price.setSalePrice(new BigDecimal("80.00"));

        Page<ProductData> firstPage = new PageImpl<>(List.of(product), PageRequest.of(0, 2,Sort.by("id")), 1);

        when(productDataRepository.countByMarkedForDeletionIsNull()).thenReturn(1L);
        when(productDataRepository.findAllNotMarkedForDeletion(any(Pageable.class)))
                .thenReturn(firstPage)
                .thenReturn(Page.empty());

        when(additionalProductDataRepository.findAllById(any())).thenReturn(List.of(additional));
        when(priceDataRepository.findAllById(any())).thenReturn(List.of(price));
        when(etlQueueStatusDataRepository.findByEtlIDAndEtlStep(any(), any()))
                .thenReturn(Optional.empty());

        doAnswer(invocation -> {
            RequestBody requestBody = invocation.getArgument(1);
            assertNotNull(requestBody);
            return null;
        }).when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        service.exportProductsToCsvAndUploadToS3(payload, messageId);
        verify(productDataRepository, atLeastOnce()).findAllNotMarkedForDeletion(any(Pageable.class));
        verify(s3Client, atLeastOnce()).putObject(any(PutObjectRequest.class), any(RequestBody.class));
        verify(etlQueueStatusDataRepository, atLeastOnce()).save(any(EtlQueueStatusData.class));
    }

    @Test
    void exportProductsToCsvAndUploadToS3_withNoProducts_shouldCompleteImmediately() throws Exception {
        LeasibilityExportMessageDTO dto = new LeasibilityExportMessageDTO();
        dto.setReferenceId("ref-1");
        String payload = objectMapper.writeValueAsString(dto);

        when(productDataRepository.countByMarkedForDeletionIsNull()).thenReturn(0L);

        service.exportProductsToCsvAndUploadToS3(payload, "msg-123");

        verify(productDataRepository, never()).findAll(any(Pageable.class));
        verify(s3Client, never()).putObject(any(PutObjectRequest.class),  any(RequestBody.class));
        verify(etlQueueStatusDataRepository, atLeastOnce()).save(any());
    }

    @Test
    void exportProductsToCsvAndUploadToS3_withException_shouldUpdateStatusAsFailed() throws Exception {
        LeasibilityExportMessageDTO dto = new LeasibilityExportMessageDTO();
        dto.setReferenceId("ref-fail");
        String payload = objectMapper.writeValueAsString(dto);

        ProductData p = new ProductData();
        p.setId("prod-fail");
        p.setName("Bad Product");
        p.setRetailerName("Retailer B");
        p.setFeedSource(ProductData.FeedSource.cj);

        Page<ProductData> page = new PageImpl<>(List.of(p), PageRequest.of(0, 2,Sort.by("id")), 1);

        when(productDataRepository.countByMarkedForDeletionIsNull()).thenReturn(1L);
        when(productDataRepository.findAllNotMarkedForDeletion(any(Pageable.class))).thenReturn(page).thenReturn(Page.empty());
        when(additionalProductDataRepository.findAllById(any())).thenReturn(Collections.emptyList());
        when(priceDataRepository.findAllById(any())).thenReturn(Collections.emptyList());

        doThrow(new RuntimeException("S3 failed")).when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        when(etlQueueStatusDataRepository.findByEtlIDAndEtlStep(any(), any()))
                .thenReturn(Optional.empty());

        assertThrows(Exception.class,()->service.exportProductsToCsvAndUploadToS3(payload, "msg-err"));

//        verify(etlQueueStatusDataRepository, atLeastOnce()).save(argThat(data ->
//                data.getStatus() == EtlQueueStatusData.Status.ERROR_RESTARTING
//        ));
    }

    @Test
    void generateCsv_shouldSkipInvalidProduct() throws Exception {
        ProductData invalid = new ProductData();
        invalid.setId("invalid");
        List<ProductData> list = List.of(invalid);

        var method = ProductLeasibilityExporterCsvService.class.getDeclaredMethod(
                "generateCsv",
                List.class,
                String.class,
                Map.class,
                Map.class
        );
        method.setAccessible(true);

        File file = (File) method.invoke(service, list, "test.csv", Map.of(), Map.of());

        assertTrue(file.exists());
        assertTrue(file.delete());
    }

    @Test
    void exportProductsToCsvAndUploadToS3_shouldExportSuccessfully_withExistingStatus() throws Exception {
        LeasibilityExportMessageDTO dto = new LeasibilityExportMessageDTO();
        dto.setReferenceId("ref-1");
        String payload = objectMapper.writeValueAsString(dto);
        String messageId = "msg-123";

        ProductData p = new ProductData();
        p.setId("prod-1");
        p.setName("Test Product");
        p.setRetailerName("Retailer A");
        p.setFeedSource(ProductData.FeedSource.google);

        AdditionalProductData a = new AdditionalProductData();
        a.setId("prod-1");
        a.setModel("Model123");

        PriceData price = new PriceData();
        price.setId("prod-1");
        price.setPrice(new BigDecimal("100.00"));
        price.setSalePrice(new BigDecimal("80.00"));

        Page<ProductData> page1 = new PageImpl<>(List.of(p), PageRequest.of(0, 1,Sort.by("id")), 1);
        Page<ProductData> emptyPage = Page.empty();

        EtlQueueStatusData existingStatus = new EtlQueueStatusData();
        existingStatus.setEtlID("ref-1");
        existingStatus.setSqsMessageData("msg-123");
        existingStatus.setEtlStep(EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT);
        when(etlQueueStatusDataRepository.findByEtlIDAndEtlStep(any(), any()))
                .thenReturn(Optional.of(existingStatus));

        when(productDataRepository.countByMarkedForDeletionIsNull()).thenReturn(1L);
        when(productDataRepository.findAllNotMarkedForDeletion(any(Pageable.class))).thenReturn(page1).thenReturn(emptyPage);
        when(additionalProductDataRepository.findAllById(any())).thenReturn(List.of(a));
        when(priceDataRepository.findAllById(any())).thenReturn(List.of(price));
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(PutObjectResponse.builder().eTag("dummy-etag").build());

        service.exportProductsToCsvAndUploadToS3(payload, messageId);

        verify(etlQueueStatusDataRepository, atLeastOnce()).save(any(EtlQueueStatusData.class));
        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }

    @Test
    @MockitoSettings(strictness = Strictness.LENIENT)
    void exportProductsToCsvAndUploadToS3_shouldHandleInterruptedException() throws Exception {
        LeasibilityExportMessageDTO dto = new LeasibilityExportMessageDTO();
        dto.setReferenceId("ref-1");
        String payload = objectMapper.writeValueAsString(dto);
        String messageId = "msg-123";

        ProductData p = new ProductData();
        p.setId("prod-1");
        p.setName("Test Product");
        p.setRetailerName("Retailer A");
        p.setFeedSource(ProductData.FeedSource.google);

        AdditionalProductData a = new AdditionalProductData();
        a.setId("prod-1");
        a.setModel("Model123");

        PriceData price = new PriceData();
        price.setId("prod-1");
        price.setPrice(new BigDecimal("100.00"));
        price.setSalePrice(new BigDecimal("80.00"));

        Page<ProductData> page1 = new PageImpl<>(List.of(p), PageRequest.of(0, 1,Sort.by("id")), 1);
        Page<ProductData> emptyPage = Page.empty();

        when(etlQueueStatusDataRepository.findByEtlIDAndEtlStep(any(), any()))
                .thenReturn(Optional.empty());

        when(productDataRepository.countByMarkedForDeletionIsNull()).thenReturn(1L);
        when(productDataRepository.findAllNotMarkedForDeletion(any(Pageable.class))).thenReturn(page1).thenReturn(emptyPage);
        when(additionalProductDataRepository.findAllById(any())).thenReturn(List.of(a));
        when(priceDataRepository.findAllById(any())).thenReturn(List.of(price));

        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(mock(PutObjectResponse.class));
        Thread.currentThread().interrupt();
        try {
           assertThrows(Exception.class,()-> service.exportProductsToCsvAndUploadToS3(payload, messageId));
        } finally {
            Thread.interrupted();
        }
        verify(etlQueueStatusDataRepository, atLeastOnce())
                .save(argThat(status -> status.getStatus() == EtlQueueStatusData.Status.ERROR_RESTARTING));
    }

}
