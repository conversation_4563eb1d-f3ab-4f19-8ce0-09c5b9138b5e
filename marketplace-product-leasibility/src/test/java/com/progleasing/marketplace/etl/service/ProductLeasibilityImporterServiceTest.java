package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import com.progleasing.marketplace.etl.repositories.ProductDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;
import java.util.stream.StreamSupport;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ProductLeasibilityImporterServiceTest {

    @InjectMocks
    private ProductLeasibilityImporterService importerService;

    @Mock
    private S3Client s3Client;

    @Mock
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @Mock
    private ProductDataRepository productDataRepository;

    @Captor
    private ArgumentCaptor<EtlQueueStatusData> statusCaptor;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        importerService = new ProductLeasibilityImporterService(
                s3Client,
                etlQueueStatusDataRepository,
                productDataRepository
        );


        ReflectionTestUtils.setField(importerService, "bucket", "import-bucket");
        ReflectionTestUtils.setField(importerService, "leasibilityInputPrefix", "incoming/");
        ReflectionTestUtils.setField(importerService, "dbChunkSize", 500);
        ReflectionTestUtils.setField(importerService, "markForDeletionGraceDays", 3);
    }

    @Test
    void testImportLeasibilityData_withDetailFilepath_processesSuccessfully() throws Exception {
        String payload = """
                        {
                          "version": "0",
                          "id": "event-id",
                          "detail-type": "D2C",
                          "source": "feedloader.service",
                          "account": "**********",
                          "time": "2025-07-10T15:39:18Z",
                          "region": "us-west-2",
                          "resources": [],
                          "detail": {
                            "data": {
                                "filepath": "incoming/file_d2c_test.csv"
                            },
                           "plsourceid": "c_leasability_service_feed_loader",
                           "subject": "FLE-1d7eb8ed-61e5-497e-83fc-ea94b2635279",
                           "specversion": "1.0",
                           "id": "784f8df9-67eb-4b20-a63e-033a4435b7cf",
                           "source": "urn:pl:test:s_leasability_service",
                           "time": "2025-07-15T11:56:54.545447Z",
                           "type": "pl.lease.product.feed.processed.v1"
                          }
                        }
                        """;

        String csvData = "id,leasable,processStatus,errorOnRecord\nP123,true,complete,false\n";
        mockS3WithCsv(csvData);
        ProductData product = new ProductData();
        product.setId("P123");
        when(productDataRepository.findAllById(any())).thenReturn(List.of(product));
        importerService.importLeasibilityData(payload, "msg-event-001");
        verify(productDataRepository).saveAll(argThat(products ->
                StreamSupport.stream(products.spliterator(), false)
                        .anyMatch(p -> p.getId().equals("P123"))
        ));
        verify(etlQueueStatusDataRepository, atLeastOnce()).save(any());
    }

    @Test
    void testImportLeasibilityData_withDetailFilepath_nonCsvFile_skipsProcessing() throws JsonProcessingException {
        String payload = """
            {
              "detail": {
              "data":{
                "filepath": "incoming/readme.txt"
              }
              },
              "id":"1234"
            }
            """;
        importerService.importLeasibilityData(payload, "msg-event-002");
        verify(etlQueueStatusDataRepository).save(argThat(data ->
                data.getStatus() == EtlQueueStatusData.Status.ERROR_RESTARTING
        ));
    }

    @Test
    void testImportLeasibilityData_withMissingDetailNode_logsError() throws JsonProcessingException {
        String payload = """
            {
              "version": "0",
              "id": "event-id"
            }
            """;

        importerService.importLeasibilityData(payload, "msg-event-003");
        verify(etlQueueStatusDataRepository, never()).save(any());
    }


    private void mockS3WithCsv(String csvContent) throws Exception {
        InputStream stream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));
        ResponseInputStream<GetObjectResponse> mockResponse = mock(ResponseInputStream.class);
        when(mockResponse.read(any(), anyInt(), anyInt())).thenAnswer(invocation -> stream.read((byte[]) invocation.getArgument(0), invocation.getArgument(1), invocation.getArgument(2)));
        when(mockResponse.read()).thenAnswer(invocation -> stream.read());

        when(s3Client.getObject(any(GetObjectRequest.class))).thenReturn(mockResponse);
    }


    private void invokeProcessS3File(String messageId) throws Exception {
        var method = ProductLeasibilityImporterService.class.getDeclaredMethod("processS3File", String.class, String.class, String.class, String.class);
        method.setAccessible(true);
        method.invoke(importerService, "bucket", "incoming/sample.csv", "req-123", messageId);
    }
}
