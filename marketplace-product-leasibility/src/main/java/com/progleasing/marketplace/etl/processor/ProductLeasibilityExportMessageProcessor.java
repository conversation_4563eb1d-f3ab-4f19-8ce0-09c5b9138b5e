package com.progleasing.marketplace.etl.processor;

import com.progleasing.marketplace.etl.service.ProductLeasibilityExporterCsvService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;

@Service
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.leasibility-exporter.listener.enabled", havingValue = "true", matchIfMissing = true)
public class ProductLeasibilityExportMessageProcessor {


    private final ProductLeasibilityExporterCsvService productLeasibilityExporterCsvService;

    private final Executor executor;

    public ProductLeasibilityExportMessageProcessor(
            ProductLeasibilityExporterCsvService productLeasibilityExporterCsvService,
            @Qualifier("etlTaskExecutor") Executor executor) {
        this.productLeasibilityExporterCsvService = productLeasibilityExporterCsvService;
        this.executor = executor;
    }


    @SqsListener(value = "${aws.sqs.leasibility-exporter.queue-name}",
            maxConcurrentMessages = "${aws.sqs.concurrent-message}", acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL
    )
    public void handle(List<Message<String>> messages) {
        log.info("Messages received: {}", messages.size());
        List<CompletableFuture<Void>> futures = messages.stream().map(message ->
                CompletableFuture.runAsync(() -> {
                    try {
                        String messageId = message.getHeaders().get("id").toString();
                        String payload = message.getPayload();
                        log.info("Received message with payload: {} and id {}", payload, messageId);
                        productLeasibilityExporterCsvService.exportProductsToCsvAndUploadToS3(payload,messageId);
                        Acknowledgement.acknowledge(message);
                    } catch (Exception e) {
                        log.error("Error processing message", e);
                    }
                }, executor)
        ).toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
}
