package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.entity.AdditionalProductData;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.PriceData;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.model.LeasibilityExportMessageDTO;
import com.progleasing.marketplace.etl.repositories.AdditionalProductDataRepository;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import com.progleasing.marketplace.etl.repositories.PriceDataRepository;
import com.progleasing.marketplace.etl.repositories.ProductDataRepository;
import com.opencsv.CSVWriter;
import jakarta.annotation.PostConstruct;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductLeasibilityExporterCsvService {

    private final ProductDataRepository productDataRepository;
    private final AdditionalProductDataRepository additionalProductDataRepository;
    private final PriceDataRepository priceDataRepository;
    private final S3Client s3Client;
    private final EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${product.leasibility.export.s3.bucket}")
    private String s3Bucket;

    @Value("${product.leasibility.export.s3.folder}")
    private String s3Folder;

    @Value("${product.leasibility.export.max.records}")
    private int maxRecordsPerFile;

    @Value("${product.leasibility.export.max.parallelism}")
    private int maxParallelism;

    private static final String[] HEADERS = {
            "name", "feed_source", "retailer", "detailed_description",
            "category", "subcategory", "tertiarycategory", "brand",
            "manufacturer", "model_num", "UPC", "vendor", "id",
            "list_price", "sale_price"
    };

    @PostConstruct
    public void normalizeS3Folder() {
        if (!s3Folder.endsWith("/")) {
            s3Folder += "/";
        }
    }

    public void exportProductsToCsvAndUploadToS3(String payload, String messageId) throws Exception {
        LeasibilityExportMessageDTO dto = objectMapper.readValue(payload, LeasibilityExportMessageDTO.class);
        updateStatusQueue(dto.getReferenceId(), messageId, EtlQueueStatusData.Status.STARTED, Collections.emptySet());

        long totalProducts = productDataRepository.countByMarkedForDeletionIsNull();
        if (totalProducts == 0) {
            log.info("No products found for export.");
            updateStatusQueue(dto.getReferenceId(), messageId, EtlQueueStatusData.Status.COMPLETED, Collections.emptySet());
            return;
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddyyyy_HHmmss"));
        int totalPages = (int) Math.ceil((double) totalProducts / maxRecordsPerFile);
        int concurrency = Math.min(totalPages, maxParallelism);

        ExecutorService executor = Executors.newFixedThreadPool(concurrency);
        AtomicBoolean hasFailure = new AtomicBoolean(false);
        List<Future<?>> tasks = new ArrayList<>();

        Pageable pageable = PageRequest.of(0, maxRecordsPerFile, Sort.by("id"));
        Page<ProductData> page = productDataRepository.findAllNotMarkedForDeletion(pageable);
        int totalProductsExported = 0;
        int fileIndex = 0;
        Set<String> uploadedFiles = new HashSet<>();
        while (page != null && page.hasContent()) {
            List<ProductData> batch = page.getContent();
            List<String> productIds = batch.stream().map(ProductData::getId).collect(Collectors.toList());
            Map<String, AdditionalProductData> additionalMap = additionalProductDataRepository.findAllById(productIds)
                    .stream().collect(Collectors.toMap(AdditionalProductData::getId, Function.identity()));

            Map<String, PriceData> priceMap = priceDataRepository.findAllById(productIds)
                    .stream().collect(Collectors.toMap(PriceData::getId, Function.identity()));

            int currentIndex = fileIndex;
            String fileName = (totalPages == 1)
                    ? String.format("product_d2c_%s.csv", timestamp)
                    : String.format("product_%d_d2c_%s.csv", currentIndex + 1, timestamp);
            totalProductsExported =  totalProductsExported + productIds.size();
            tasks.add(executor.submit(() -> {
                try {
                    File file = generateCsv(batch, fileName, additionalMap, priceMap);
                    uploadToS3(file, s3Folder + fileName);
                    Files.delete(file.toPath());
                    log.info("Completed file: {} for message {}", fileName,dto.getReferenceId());
                } catch (Exception e) {
                    log.error("Failed to process file {}: {}", fileName, dto.getReferenceId(), e);
                    hasFailure.set(true);
                }
            }));
            pageable = pageable.next();
            page = productDataRepository.findAllNotMarkedForDeletion(pageable);
            fileIndex++;
            uploadedFiles.add(fileName);
        }

        for (Future<?> task : tasks) {
            try {
                task.get();
            } catch (Exception e) {
                log.error("Thread error: {}", e.getCause() != null ? e.getCause().getMessage() : e.getMessage(), e);
                hasFailure.set(true);
            }
        }

        executor.shutdown();
        log.info("Total product added in export for etlid {} = totalProductsExported {}",dto.getReferenceId(),totalProductsExported);
        if (hasFailure.get()) {
            log.warn("One or more CSV export threads failed. Marking job as ERROR_RESTARTING.");
            updateStatusQueue(dto.getReferenceId(), messageId, EtlQueueStatusData.Status.ERROR_RESTARTING, uploadedFiles);
            throw new Exception("Error processing leasibility export for "+dto.getReferenceId());
        } else {
            log.info("Export completed successfully. Files generated: {}", fileIndex);
            updateStatusQueue(dto.getReferenceId(), messageId, EtlQueueStatusData.Status.COMPLETED,uploadedFiles);
        }
    }


    private File generateCsv(List<ProductData> products, String fileName,
                             Map<String, AdditionalProductData> additionalMap,
                             Map<String, PriceData> priceMap) throws IOException {
        File file = new File(System.getProperty("java.io.tmpdir"), fileName);
        try (CSVWriter writer = new CSVWriter(new FileWriter(file))) {
            writer.writeNext(HEADERS);

            for (ProductData p : products) {
                if (StringUtils.isBlank(p.getName()) ||
                        p.getFeedSource() == null ||
                        StringUtils.isBlank(p.getRetailerName())) {
                    log.warn("Skipping product ID {} due to missing required fields", p.getId());
                    continue;
                }

                AdditionalProductData additional = additionalMap.get(p.getId());
                PriceData price = priceMap.get(p.getId());

                String name = clean(p.getName());
                String description = clean(p.getLongDescription() != null ? p.getLongDescription() : p.getName());
                String brand = clean(p.getBrand());

                writer.writeNext(new String[]{
                        name,
                        p.getFeedSource().toString(),
                        p.getRetailerName(),
                        description,
                        nullToEmpty(p.getL1CategoryName()),
                        nullToEmpty(p.getL2CategoryName()),
                        "",
                        brand,
                        "",
                        additional != null ? nullToEmpty(additional.getModel()) : "",
                        "",
                        "",
                         nullToEmpty(p.getId()),
                        price != null && price.getPrice() != null ? price.getPrice().toPlainString() : "",
                        price != null && price.getSalePrice() != null ? price.getSalePrice().toPlainString() : ""
                });
            }
        }
        return file;
    }

    private void uploadToS3(File file, String key) {
        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(s3Bucket)
                .key(key)
                .build();

        s3Client.putObject(request, RequestBody.fromFile(file));
        log.info("Uploaded to S3: s3://{}/{}", s3Bucket, key);
    }

//    private String clean(String input) {
//        if (input == null) return "";
//        String cleaned = input.replaceAll("[\\r\\n\\t]", "").replace("\"", "\"\"");
//        return cleaned.contains(",") || cleaned.contains("\"") ? "\"" + cleaned + "\"" : cleaned;
//    }
        private String clean(String input) {
            if (input == null) return "";
            // Remove control characters
            String cleaned = input.replaceAll("[\\r\\n\\t]", "");
            // Escape double quotes with backslash
            cleaned = cleaned.replace("\"", "\\\"");
            return cleaned;
        }

    private String nullToEmpty(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    @Transactional
    private void updateStatusQueue(String requestId, String messageId, EtlQueueStatusData.Status status, Set<String> uploadedFiles) {
        String messageData = String.format(
                "%s|%s",
                messageId,
                uploadedFiles
        );
        EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_EXPORT;
        Optional<EtlQueueStatusData> existing =
                etlQueueStatusDataRepository.findByEtlIDAndEtlStep(
                        requestId,
                        etlStep
                );
        EtlQueueStatusData etlQueueStatusData;
        if (existing.isPresent()) {
            etlQueueStatusData = existing.get();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setSqsMessageData(messageData);
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        } else {
            etlQueueStatusData = new EtlQueueStatusData();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setEtlStep(etlStep);
            etlQueueStatusData.setEtlID(requestId);
            etlQueueStatusData.setMessageId(messageId);
            etlQueueStatusData.setSqsMessageData(messageData);
            if (status == EtlQueueStatusData.Status.STARTED) {
                etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
            }
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        }
        etlQueueStatusDataRepository.save(etlQueueStatusData);
    }
}
