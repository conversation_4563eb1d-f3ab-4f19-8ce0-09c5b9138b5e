package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import com.progleasing.marketplace.etl.repositories.ProductDataRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductLeasibilityImporterService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final S3Client s3Client;
    private final EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private final ProductDataRepository productDataRepository;

    @Value("${product.leasibility.import.s3.bucket}")
    private String bucket;

    @Value("${product.leasibility.import.s3-prefix}")
    private String leasibilityInputPrefix;

    @Value("${product.etl.grace.days}")
    private int markForDeletionGraceDays;

    @Value("${product.leasibility.import.db-chunk.size}")
    private int dbChunkSize;

    public void importLeasibilityData(String payload, String messageId) throws JsonProcessingException {
        JsonNode root = objectMapper.readTree(payload);
        String requestId = root.get("id").asText();
        try {
            JsonNode detail = root.get("detail");
            if (detail != null && detail.has("data")) {
                JsonNode data = detail.get("data");
                if (data != null && data.has("filepath")) {
                    String objectKey = data.get("filepath").asText();
                    log.info("Received event for file: {}", objectKey);

                    if (objectKey.startsWith(leasibilityInputPrefix) && objectKey.endsWith(".csv")) {
                        log.info("Processing leasibility CSV file: {}", objectKey);
                        if (StringUtils.isNotEmpty(requestId)) {
                            updateStatusQueue(requestId, messageId, EtlQueueStatusData.Status.STARTED,objectKey);
                        }
                        processS3File(bucket, objectKey, requestId, messageId);
                    } else {
                        log.info("Skipping non-CSV or irrelevant file: {}", objectKey);
                        updateStatusQueue(requestId, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING,null);

                    }
                }
            }else {
                log.error("Missing or invalid 'detail.data' in event payload for message {}",messageId);
            }
        } catch (Exception e) {
            log.error("Error parsing payload  for message {}",messageId, e);
            if (StringUtils.isNotEmpty(requestId)) {
                updateStatusQueue(requestId, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING,null);
            }
        }
    }

    private void processS3File(String bucketName, String key, String requestId, String messageId) {
        try (ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(
                GetObjectRequest.builder().bucket(bucketName).key(key).build());
             BufferedReader reader = new BufferedReader(new InputStreamReader(s3Object))) {

            CSVFormat format = CSVFormat.Builder.create(CSVFormat.DEFAULT)
                    .setHeader()
                    .setSkipHeaderRecord(true)
                    .build();

            List<CSVRecord> recordList = format.parse(reader).getRecords();
            Map<String, CSVRecord> recordMap = new LinkedHashMap<>();

            for (CSVRecord csv : recordList) {
                recordMap.put(clean(csv.get("id")), csv);
            }

            List<ProductData> productsToUpdate = new ArrayList<>();
            int skippedProducts = 0;
            List<String> allIds = new ArrayList<>(recordMap.keySet());

            while (!allIds.isEmpty()) {
                int endIndex = Math.min(dbChunkSize, allIds.size());
                List<String> chunk = new ArrayList<>(allIds.subList(0, endIndex));
                Map<String, ProductData> productMap = productDataRepository.findAllById(chunk).stream()
                        .collect(Collectors.toMap(ProductData::getId, p -> p));

                for (String id : chunk) {
                    CSVRecord csv = recordMap.get(id);

                    try {
                        boolean leasable = Boolean.parseBoolean(csv.get("leasable"));
                        String processStatus = csv.get("processStatus");
                        boolean errorOnRecord = Boolean.parseBoolean(csv.get("errorOnRecord"));

                        if (errorOnRecord) {
                            log.warn("Record {} has an error and will be skipped.", id);
                            skippedProducts ++;
                            continue;
                        }

                        if (!leasable && "in process".equalsIgnoreCase(processStatus)) {
                            log.info("Product {} is skipped from import process", id);
                            skippedProducts++;
                            continue;
                        }

                        ProductData product = productMap.get(id);
                        if (product != null) {
                            if (leasable) {
                                product.setLeasable(true);
                                product.setMarkedForDeletion(null);
                                product.setLastModified(Timestamp.from(Instant.now()));
                                log.info("Product {} is leasable.", id);
                            } else {
                                product.setLeasable(false);
                                Timestamp markForDeletion = Timestamp.from(Instant.now().plusSeconds(markForDeletionGraceDays * 86400L));
                                product.setMarkedForDeletion(markForDeletion);
                                product.setLastModified(Timestamp.from(Instant.now()));
                                log.info("Product {} is not leaseable.", id);
                            }
                            productsToUpdate.add(product);
                        } else {
                            log.warn("Product with ID {} not found in database.", id);
                        }
                    } catch (Exception e) {
                        log.error("Error processing CSV record: " + e.getMessage(), e);
                    }
                }
                allIds.subList(0, endIndex).clear();
            }
            log.info("Products skipped from leasibility {}", skippedProducts);
            if (!productsToUpdate.isEmpty()) {
                log.info("Number of Products to Update {}", productsToUpdate.size());
                productDataRepository.saveAll(productsToUpdate);
            }
            if (StringUtils.isNotEmpty(requestId)) {
                updateStatusQueue(requestId, messageId, EtlQueueStatusData.Status.COMPLETED, null);
            }

        } catch (Exception e) {
            log.error("Error processing file from S3: " + e.getMessage(), e);
            if (StringUtils.isNotEmpty(requestId)) {
                updateStatusQueue(requestId, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING, null);
            }
        }
    }

    @Transactional
    private void updateStatusQueue(String requestId, String messageId, EtlQueueStatusData.Status status, String objectKey) {
        String messageData = String.format(
                "%s|%s",
                messageId,
                objectKey
        );
        EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_LEASIBILITY_IMPORT;
        Optional<EtlQueueStatusData> existing =
                etlQueueStatusDataRepository.findByEtlIDAndEtlStep(
                        requestId,
                        etlStep
                );
        EtlQueueStatusData etlQueueStatusData;
        if (existing.isPresent()) {
            etlQueueStatusData = existing.get();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        } else {
            etlQueueStatusData = new EtlQueueStatusData();
            etlQueueStatusData.setStatus(status);
            etlQueueStatusData.setEtlStep(etlStep);
            etlQueueStatusData.setEtlID(requestId);
            etlQueueStatusData.setMessageId(messageId);
            etlQueueStatusData.setSqsMessageData(messageData);
            if (status == EtlQueueStatusData.Status.STARTED) {
                etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
            }
            etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
        }
        etlQueueStatusDataRepository.save(etlQueueStatusData);
    }

    private String clean(String input) {
        return input == null ? "" : input.replace("\"", "").trim();
    }
}
