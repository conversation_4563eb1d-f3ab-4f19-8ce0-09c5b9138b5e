plugins {
    id("org.springframework.boot") apply false
    id("io.spring.dependency-management")
    id("java")
}

group = "com.progleasing.marketplace"
version = "1.0.1"
description = "ETL process to export leasibility products to csv"

repositories {
    mavenCentral()
}
val springBootVersion = "3.4.4"
val lombokVersion = "1.18.32"

dependencies {
    implementation("org.springframework.boot:spring-boot-starter:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-json:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}")

    implementation("io.awspring.cloud:spring-cloud-aws-starter-sqs:3.0.4")
    implementation("software.amazon.awssdk:sqs:2.31.38")
    implementation("software.amazon.awssdk:sts:2.31.38")
    implementation("software.amazon.awssdk:s3:2.31.38")

    implementation("com.fasterxml.jackson.core:jackson-databind:2.18.3")

    // Apache Commons CSV
    implementation("org.apache.commons:commons-csv:1.10.0")
    implementation("com.opencsv:opencsv:5.9")

    implementation("com.commercetools.sdk:commercetools-http-client:17.29.0")
    implementation("com.commercetools.sdk:commercetools-sdk-java-api:17.29.0")
    implementation("com.commercetools.sdk:commercetools-sdk-java-importapi:17.29.0")
    implementation(project(":marketplace-shared-model"))

    compileOnly("org.projectlombok:lombok:$lombokVersion")
    annotationProcessor("org.projectlombok:lombok:$lombokVersion")
    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

tasks.test {
    useJUnitPlatform()
}
