package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.PlS3ClientProvider;
import com.progleasing.marketplace.etl.repository.SovrnProductsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.postgresql.copy.CopyManager;
import org.postgresql.core.BaseConnection;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import javax.sql.DataSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.util.zip.GZIPOutputStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class S3ToPostgresLoaderServiceTest {

    private S3ToPostgresLoaderService loaderService;
    private PlS3ClientProvider mockPlS3ClientProvider;
    private S3Client mockS3Client;
    private DataSource mockDataSource;
    private Connection mockConnection;
    private CopyManager mockCopyManager;
    private SovrnProductsRepository mockSovrnProductsRepository;

    @BeforeEach
    public void setUp() throws Exception {
        mockPlS3ClientProvider = mock(PlS3ClientProvider.class);
        mockS3Client = mock(S3Client.class);
        mockDataSource = mock(DataSource.class);
        mockConnection = mock(BaseConnection.class);
        mockCopyManager = mock(CopyManager.class);
        mockSovrnProductsRepository = mock(SovrnProductsRepository.class);

        when(mockPlS3ClientProvider.createPlS3Client()).thenReturn(mockS3Client);
        when(mockDataSource.getConnection()).thenReturn(mockConnection);
        when(mockConnection.unwrap(BaseConnection.class)).thenReturn((BaseConnection) mockConnection);
        when(mockConnection.unwrap(BaseConnection.class).getCopyAPI()).thenReturn(mockCopyManager);

//        loaderService = new S3ToPostgresLoaderService(mockDataSource, mockPlS3ClientProvider, mockSovrnProductsRepository);
    }

    public void testLoadSovrnProductsFromS3() throws Exception {
        String fileName = "test.csv.gz";
        String plKey = "test-key";
        String csvData = "currency,ean,image_url,in_stock,isbn,language,brand,merchant_name,mpn,product_name,domain,retail_price,sale_price,sku,thumbnail_url,upc,affiliated_url,merchant_raw_category\n"
                + "USD,1234567890123,http://example.com/image.jpg,true,978-3-16-148410-0,EN,BrandX,MerchantY,MPN123,ProductName,example.com,100.00,90.00,SKU123,http://example.com/thumb.jpg,012345678905,http://example.com/product,Category";

        // Compress the CSV data using GZIP
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(csvData.getBytes());
        }
        InputStream gzipStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        ResponseInputStream<GetObjectResponse> mockResponseInputStream = mock(ResponseInputStream.class);
        when(mockResponseInputStream.read(any(byte[].class), anyInt(), anyInt())).thenAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            return gzipStream.read(buffer, invocation.getArgument(1), invocation.getArgument(2));
        });

        when(mockS3Client.getObject(any(GetObjectRequest.class))).thenReturn(mockResponseInputStream);
        when(mockCopyManager.copyIn(anyString(), any(InputStream.class))).thenReturn(1L);

        long rowsInserted = loaderService.loadSovrnProductsFromS3(fileName, plKey);

        assertEquals(1L, rowsInserted);
        verify(mockCopyManager, times(1)).copyIn(anyString(), any(InputStream.class));
    }

    @Test
    public void testLoadSovrnProductsFromS3Bucket() throws Exception {
        S3ToPostgresLoaderService mockLoaderService = mock(S3ToPostgresLoaderService.class);
        when(mockLoaderService.loadSovrnProductsFromS3(anyString(), anyString())).thenReturn(1L);

        long rowsInserted = mockLoaderService.loadSovrnProductsFromS3("test.csv.gz", "test-key");
        assertEquals(1L, rowsInserted);
        verify(mockLoaderService, times(1)).loadSovrnProductsFromS3(anyString(), anyString());
    }
}