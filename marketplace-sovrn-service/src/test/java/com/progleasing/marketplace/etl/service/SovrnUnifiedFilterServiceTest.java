package com.progleasing.marketplace.etl.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for SovrnUnifiedFilterService
 * Tests both folder-level and file-level filtering strategies
 */
@ExtendWith(MockitoExtension.class)
class SovrnUnifiedFilterServiceTest {

    private SovrnFolderLevelFilterService folderLevelFilterService;
    private SovrnFileLevelFilterService fileLevelFilterService;
    private SovrnUnifiedFilterService unifiedFilterService;

    @BeforeEach
    void setUp() {
        // Create services with test configuration
        folderLevelFilterService = new SovrnFolderLevelFilterService();
        fileLevelFilterService = new SovrnFileLevelFilterService();
        
        // Set test configuration values using reflection or create test-specific setup
        setTestConfiguration();
        
        // Initialize services
        folderLevelFilterService.init();
        fileLevelFilterService.init();
        
        // Create unified service
        unifiedFilterService = new SovrnUnifiedFilterService(folderLevelFilterService, fileLevelFilterService);
    }

    private void setTestConfiguration() {
        // This would normally be done through @TestPropertySource or similar
        // For now, we'll test the logic with mock data
    }

    @Test
    @DisplayName("Test Wayfair folder-level filtering")
    void testWayfairFolderLevelFiltering() {
        System.out.println("\n=== WAYFAIR FOLDER-LEVEL FILTERING TEST ===");
        
        List<String> wayfairS3Keys = Arrays.asList(
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_11480/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_48541/products.csv.gz"
        );

        for (String s3Key : wayfairS3Keys) {
            SovrnUnifiedFilterService.FilteringInfo info = unifiedFilterService.getFilteringInfo(s3Key);
            System.out.println(String.format("%s: %s", 
                info.shouldProcess ? "PROCESS" : "SKIP", 
                s3Key));
            System.out.println(String.format("   Strategy: %s, Retailer: %s, Reason: %s", 
                info.strategy, info.retailerType, info.reason));
            
            assertEquals("wayfair", info.retailerType);
            assertEquals("FOLDER_LEVEL", info.strategy);
        }
    }

    @Test
    @DisplayName("Test Walmart file-level filtering")
    void testWalmartFileLevelFiltering() {
        System.out.println("\n=== WALMART FILE-LEVEL FILTERING TEST ===");
        
        List<String> walmartS3Keys = Arrays.asList(
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36616.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36703.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/99999.csv.gz"
        );

        for (String s3Key : walmartS3Keys) {
            SovrnUnifiedFilterService.FilteringInfo info = unifiedFilterService.getFilteringInfo(s3Key);
            System.out.println(String.format("%s: %s", 
                info.shouldProcess ? "PROCESS" : "SKIP", 
                s3Key));
            System.out.println(String.format("   Strategy: %s, Retailer: %s, Reason: %s", 
                info.strategy, info.retailerType, info.reason));
            
            assertEquals("walmart", info.retailerType);
            assertEquals("FILE_LEVEL", info.strategy);
        }
    }

    @Test
    @DisplayName("Test Target file-level filtering")
    void testTargetFileLevelFiltering() {
        System.out.println("\n=== TARGET FILE-LEVEL FILTERING TEST ===");
        
        List<String> targetS3Keys = Arrays.asList(
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/11551.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/12345.csv.gz"
        );

        for (String s3Key : targetS3Keys) {
            SovrnUnifiedFilterService.FilteringInfo info = unifiedFilterService.getFilteringInfo(s3Key);
            System.out.println(String.format("%s: %s", 
                info.shouldProcess ? "PROCESS" : "SKIP", 
                s3Key));
            System.out.println(String.format("   Strategy: %s, Retailer: %s, Reason: %s", 
                info.strategy, info.retailerType, info.reason));
            
            assertEquals("target", info.retailerType);
            assertEquals("FILE_LEVEL", info.strategy);
        }
    }

    @Test
    @DisplayName("Test other retailers (no filtering)")
    void testOtherRetailersNoFiltering() {
        System.out.println("\n=== OTHER RETAILERS (NO FILTERING) TEST ===");
        
        List<String> otherS3Keys = Arrays.asList(
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/williams-sonoma_38699/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/best_buy_u.s_39734/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/adorama_11359/products.csv.gz"
        );

        for (String s3Key : otherS3Keys) {
            SovrnUnifiedFilterService.FilteringInfo info = unifiedFilterService.getFilteringInfo(s3Key);
            System.out.println(String.format("%s: %s", 
                info.shouldProcess ? "PROCESS" : "SKIP", 
                s3Key));
            System.out.println(String.format("   Strategy: %s, Retailer: %s, Reason: %s", 
                info.strategy, info.retailerType, info.reason));
            
            assertEquals("other", info.retailerType);
            assertEquals("NO_FILTERING", info.strategy);
            assertTrue(info.shouldProcess); // All other retailers should be processed
        }
    }

    @Test
    @DisplayName("Test comprehensive filtering scenarios")
    void testComprehensiveFilteringScenarios() {
        System.out.println("\n=== COMPREHENSIVE FILTERING SCENARIOS ===");
        
        List<String> allS3Keys = Arrays.asList(
            // Wayfair - folder level filtering
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_11480/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_48541/products.csv.gz",
            
            // Walmart - file level filtering
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36616.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/99999.csv.gz",
            
            // Target - file level filtering
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/11551.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/12345.csv.gz",
            
            // Other retailers - no filtering
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/williams-sonoma_38699/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/best_buy_u.s_39734/products.csv.gz"
        );

        int processCount = 0;
        int skipCount = 0;

        System.out.println("\nFILTERING RESULTS:");
        System.out.println("=".repeat(80));

        for (String s3Key : allS3Keys) {
            SovrnUnifiedFilterService.FilteringInfo info = unifiedFilterService.getFilteringInfo(s3Key);
            
            if (info.shouldProcess) {
                processCount++;
            } else {
                skipCount++;
            }

            System.out.println(String.format("%s: %s", 
                info.shouldProcess ? "PROCESS" : "SKIP   ", 
                s3Key));
            System.out.println(String.format("   Strategy: %-12s | Retailer: %-8s | Reason: %s", 
                info.strategy, info.retailerType, info.reason));
        }

        System.out.println("\nSUMMARY:");
        System.out.println("=".repeat(50));
        System.out.println("Total files: " + allS3Keys.size());
        System.out.println("Will be PROCESSED: " + processCount);
        System.out.println("Will be SKIPPED: " + skipCount);
        System.out.printf("Processing rate: %.1f%%\n", (processCount * 100.0 / allS3Keys.size()));

        // Assertions
        assertTrue(processCount > 0, "At least some files should be processed");
        assertEquals(allS3Keys.size(), processCount + skipCount, "All files should be accounted for");
    }

    @Test
    @DisplayName("Test filtering strategy detection")
    void testFilteringStrategyDetection() {
        // Test strategy detection
        assertEquals("FOLDER_LEVEL", unifiedFilterService.getFilteringStrategy(
            "feed_exports/.../wayfair_north_america_11480/products.csv.gz"));
        
        assertEquals("FILE_LEVEL", unifiedFilterService.getFilteringStrategy(
            "feed_exports/.../walmart_affiliate_program_11946/36616.csv.gz"));
        
        assertEquals("FILE_LEVEL", unifiedFilterService.getFilteringStrategy(
            "feed_exports/.../target_10238/11551.csv.gz"));
        
        assertEquals("NO_FILTERING", unifiedFilterService.getFilteringStrategy(
            "feed_exports/.../williams-sonoma_38699/products.csv.gz"));
    }

    @Test
    @DisplayName("Test retailer type detection")
    void testRetailerTypeDetection() {
        // Test retailer type detection
        assertEquals("wayfair", unifiedFilterService.getRetailerType(
            "feed_exports/.../wayfair_north_america_11480/products.csv.gz"));
        
        assertEquals("walmart", unifiedFilterService.getRetailerType(
            "feed_exports/.../walmart_affiliate_program_11946/36616.csv.gz"));
        
        assertEquals("target", unifiedFilterService.getRetailerType(
            "feed_exports/.../target_10238/11551.csv.gz"));
        
        assertEquals("other", unifiedFilterService.getRetailerType(
            "feed_exports/.../williams-sonoma_38699/products.csv.gz"));
    }
}
