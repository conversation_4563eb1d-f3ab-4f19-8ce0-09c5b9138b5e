package com.progleasing.marketplace.etl.config;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Comprehensive test for all 22 retailer folders to check processing vs skipping
 * Tests the new filtering logic with folder-level and file-level filtering
 */
public class ComprehensiveRetailerFilteringTest {

    // Configuration from application.yml
    private static final String WAYFAIR_ALLOWED_FOLDER_IDS = "11480";  // Folder-level filtering
    private static final String WALMART_ALLOWED_FILE_IDS = "36616,36703,36669,36849,36704,36859,36631,36636";  // File-level filtering
    private static final String TARGET_ALLOWED_FILE_IDS = "11551";  // File-level filtering

    private Set<String> wayfairAllowedFolderIds;
    private Set<String> walmartAllowedFileIds;
    private Set<String> targetAllowedFileIds;

    public static void main(String[] args) {
        ComprehensiveRetailerFilteringTest test = new ComprehensiveRetailerFilteringTest();
        test.init();
        test.runComprehensiveTest();
    }

    private void init() {
        wayfairAllowedFolderIds = parseCommaSeparatedIds(WAYFAIR_ALLOWED_FOLDER_IDS);
        walmartAllowedFileIds = parseCommaSeparatedIds(WALMART_ALLOWED_FILE_IDS);
        targetAllowedFileIds = parseCommaSeparatedIds(TARGET_ALLOWED_FILE_IDS);
    }

    private void runComprehensiveTest() {
        System.out.println("================================================================================");
        System.out.println("COMPREHENSIVE RETAILER FILTERING TEST - ALL 22 RETAILERS");
        System.out.println("================================================================================");
        
        // All 22 retailer folders you provided
        List<String> allRetailerFolders = Arrays.asList(
            "williams-sonoma_38699/",
            "west_elm_us_38693/",
            "wayfair_north_america_48541/",
            "wayfair_north_america_11480/",
            "walmart_affiliate_program_11946/",
            "the_tire_rack_46739/",
            "the_tire_rack_24608/",
            "the_home_depot_10334/",
            "target_10238/",
            "sur_la_table_89909/",
            "mattress_firm_47236/",
            "lowe's_25447/",
            "hp_us_106183/",
            "golf_galaxy_82062/",
            "gamestop,_inc._43939/",
            "dyson_inc._11127/",
            "dell_home_&_home_office_814/",
            "cost_plus_world_market_10203/",
            "best_buy_u.s_39734/",
            "ashley_44337/",
            "adorama_11359/"
        );

        showConfiguration();
        testFolderLevelProcessing(allRetailerFolders);
        testFileLevelProcessing();
        showFinalSummary(allRetailerFolders);
    }

    private void showConfiguration() {
        System.out.println();
        System.out.println("FILTERING CONFIGURATION:");
        System.out.println("--------------------------------------------------");
        System.out.println("FOLDER-LEVEL FILTERING (Wayfair only):");
        System.out.println("  Wayfair allowed folder IDs: " + wayfairAllowedFolderIds);
        System.out.println();
        System.out.println("FILE-LEVEL FILTERING (Walmart & Target):");
        System.out.println("  Walmart allowed file IDs: " + walmartAllowedFileIds);
        System.out.println("  Target allowed file IDs: " + targetAllowedFileIds);
        System.out.println();
        System.out.println("NO FILTERING: All other retailers process everything");
    }

    private void testFolderLevelProcessing(List<String> allRetailerFolders) {
        System.out.println();
        System.out.println("FOLDER-LEVEL PROCESSING ANALYSIS:");
        System.out.println("================================================================================");
        
        int processedFolders = 0;
        int skippedFolders = 0;
        
        Map<String, List<String>> categorizedFolders = new HashMap<>();
        categorizedFolders.put("PROCESSED", new ArrayList<>());
        categorizedFolders.put("SKIPPED", new ArrayList<>());

        for (String folder : allRetailerFolders) {
            String folderId = extractFolderIdFromPath(folder);
            String retailerType = getRetailerTypeFromFolder(folder);
            boolean shouldProcessFolder = shouldProcessFolderLevel(folder, retailerType, folderId);
            
            if (shouldProcessFolder) {
                processedFolders++;
                categorizedFolders.get("PROCESSED").add(folder);
            } else {
                skippedFolders++;
                categorizedFolders.get("SKIPPED").add(folder);
            }
        }

        // Show processed folders
        System.out.println("FOLDERS THAT WILL BE PROCESSED (" + processedFolders + "/" + allRetailerFolders.size() + "):");
        System.out.println("--------------------------------------------------");
        for (String folder : categorizedFolders.get("PROCESSED")) {
            String folderId = extractFolderIdFromPath(folder);
            String retailerType = getRetailerTypeFromFolder(folder);
            String strategy = getFilteringStrategy(retailerType);
            System.out.printf("PROCESS: %-35s (ID: %-6s, Type: %-8s, Strategy: %s)%n", 
                folder, folderId, retailerType, strategy);
        }

        // Show skipped folders
        System.out.println();
        System.out.println("FOLDERS THAT WILL BE SKIPPED (" + skippedFolders + "/" + allRetailerFolders.size() + "):");
        System.out.println("--------------------------------------------------");
        for (String folder : categorizedFolders.get("SKIPPED")) {
            String folderId = extractFolderIdFromPath(folder);
            String retailerType = getRetailerTypeFromFolder(folder);
            String reason = getSkipReason(retailerType, folderId);
            System.out.printf("SKIP:    %-35s (ID: %-6s, Type: %-8s, Reason: %s)%n", 
                folder, folderId, retailerType, reason);
        }
    }

    private void testFileLevelProcessing() {
        System.out.println();
        System.out.println("FILE-LEVEL PROCESSING EXAMPLES:");
        System.out.println("================================================================================");
        
        // Sample files for Walmart and Target to show file-level filtering
        List<String> sampleFiles = Arrays.asList(
            // Walmart files (folder is processed, but files are filtered)
            "feed_exports/.../walmart_affiliate_program_11946/36616.csv.gz",  // Allowed
            "feed_exports/.../walmart_affiliate_program_11946/36703.csv.gz",  // Allowed
            "feed_exports/.../walmart_affiliate_program_11946/99999.csv.gz",  // Not allowed
            
            // Target files (folder is processed, but files are filtered)
            "feed_exports/.../target_10238/11551.csv.gz",  // Allowed
            "feed_exports/.../target_10238/12345.csv.gz"   // Not allowed
        );

        System.out.println("Sample file-level filtering for Walmart and Target:");
        System.out.println("--------------------------------------------------");
        
        for (String s3Key : sampleFiles) {
            String retailerType = getRetailerTypeFromS3Key(s3Key);
            String fileId = extractFileIdFromS3Key(s3Key);
            boolean shouldProcessFile = shouldProcessFileLevel(s3Key, retailerType, fileId);
            
            System.out.printf("%s: %s (File ID: %s)%n", 
                shouldProcessFile ? "PROCESS FILE" : "SKIP FILE", 
                s3Key, fileId);
        }
    }

    private void showFinalSummary(List<String> allRetailerFolders) {
        System.out.println();
        System.out.println("FINAL SUMMARY:");
        System.out.println("================================================================================");
        
        // Count by retailer type
        Map<String, Integer> retailerCounts = new HashMap<>();
        Map<String, Integer> processedCounts = new HashMap<>();
        
        for (String folder : allRetailerFolders) {
            String retailerType = getRetailerTypeFromFolder(folder);
            String folderId = extractFolderIdFromPath(folder);
            boolean shouldProcess = shouldProcessFolderLevel(folder, retailerType, folderId);
            
            retailerCounts.put(retailerType, retailerCounts.getOrDefault(retailerType, 0) + 1);
            if (shouldProcess) {
                processedCounts.put(retailerType, processedCounts.getOrDefault(retailerType, 0) + 1);
            }
        }

        System.out.println("BREAKDOWN BY RETAILER TYPE:");
        System.out.println("--------------------------------------------------");
        for (String retailerType : Arrays.asList("wayfair", "walmart", "target", "other")) {
            int total = retailerCounts.getOrDefault(retailerType, 0);
            int processed = processedCounts.getOrDefault(retailerType, 0);
            int skipped = total - processed;
            
            if (total > 0) {
                System.out.printf("%-8s: %d total, %d processed, %d skipped (%.1f%% processed)%n", 
                    retailerType.toUpperCase(), total, processed, skipped, 
                    (processed * 100.0 / total));
            }
        }

        // Overall summary
        int totalFolders = allRetailerFolders.size();
        int totalProcessed = processedCounts.values().stream().mapToInt(Integer::intValue).sum();
        int totalSkipped = totalFolders - totalProcessed;

        System.out.println();
        System.out.println("OVERALL SUMMARY:");
        System.out.println("--------------------------------------------------");
        System.out.println("Total retailer folders: " + totalFolders);
        System.out.println("Folders that will be PROCESSED: " + totalProcessed);
        System.out.println("Folders that will be SKIPPED: " + totalSkipped);
        System.out.printf("Overall processing rate: %.1f%%%n", (totalProcessed * 100.0 / totalFolders));
        
        System.out.println();
        System.out.println("FILTERING STRATEGY BREAKDOWN:");
        System.out.println("--------------------------------------------------");
        System.out.println("- FOLDER-LEVEL filtering: Wayfair (1 retailer)");
        System.out.println("- FILE-LEVEL filtering: Walmart, Target (2 retailers)");
        System.out.println("- NO filtering: All other retailers (" + retailerCounts.getOrDefault("other", 0) + " retailers)");
    }

    // Helper methods
    private boolean shouldProcessFolderLevel(String folder, String retailerType, String folderId) {
        // Wayfair - folder-level filtering
        if ("wayfair".equals(retailerType)) {
            return wayfairAllowedFolderIds.contains(folderId);
        }
        
        // Walmart, Target, and all others - process folders (file-level filtering happens later)
        return true;
    }

    private boolean shouldProcessFileLevel(String s3Key, String retailerType, String fileId) {
        if ("walmart".equals(retailerType)) {
            return walmartAllowedFileIds.contains(fileId);
        }
        if ("target".equals(retailerType)) {
            return targetAllowedFileIds.contains(fileId);
        }
        return true; // Other retailers and wayfair don't have file-level filtering
    }

    private String getFilteringStrategy(String retailerType) {
        if ("wayfair".equals(retailerType)) {
            return "FOLDER_LEVEL";
        } else if ("walmart".equals(retailerType) || "target".equals(retailerType)) {
            return "FILE_LEVEL";
        } else {
            return "NO_FILTERING";
        }
    }

    private String getSkipReason(String retailerType, String folderId) {
        if ("wayfair".equals(retailerType)) {
            return "Folder ID " + folderId + " not in allowed list";
        }
        return "Should not be skipped";
    }

    private String getRetailerTypeFromFolder(String folder) {
        if (folder == null) return "unknown";
        String lowerName = folder.toLowerCase();
        if (lowerName.contains("wayfair")) return "wayfair";
        if (lowerName.contains("walmart")) return "walmart";
        if (lowerName.contains("target")) return "target";
        return "other";
    }

    private String getRetailerTypeFromS3Key(String s3Key) {
        if (s3Key == null) return "unknown";
        String lowerKey = s3Key.toLowerCase();
        if (lowerKey.contains("wayfair")) return "wayfair";
        if (lowerKey.contains("walmart")) return "walmart";
        if (lowerKey.contains("target")) return "target";
        return "other";
    }

    private String extractFolderIdFromPath(String folderPath) {
        if (folderPath == null || folderPath.trim().isEmpty()) {
            return null;
        }
        String cleanPath = folderPath.endsWith("/") ? 
            folderPath.substring(0, folderPath.length() - 1) : folderPath;
        String[] parts = cleanPath.split("_");
        return parts[parts.length - 1];
    }

    private String extractFileIdFromS3Key(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            return null;
        }
        String[] pathParts = s3Key.split("/");
        if (pathParts.length > 0) {
            String filename = pathParts[pathParts.length - 1];
            if (filename.contains(".csv")) {
                return filename.substring(0, filename.indexOf(".csv"));
            }
        }
        return null;
    }

    private Set<String> parseCommaSeparatedIds(String commaSeparatedIds) {
        if (commaSeparatedIds == null || commaSeparatedIds.trim().isEmpty()) {
            return Collections.emptySet();
        }
        return Arrays.stream(commaSeparatedIds.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toSet());
    }
}
