package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.repository.CustomSovrnProductsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
class PsqlCopyRunnerWithTransformTest {

    @Mock
    private CustomSovrnProductsRepository customSovrnProductsRepository;

    private PsqlCopyRunnerWithTransform psqlCopyRunnerWithTransform;

    @BeforeEach
    void setUp() {
        psqlCopyRunnerWithTransform = new PsqlCopyRunnerWithTransform(customSovrnProductsRepository);
    }

    @Test
    void testRetailerKeyTransformation() {
        // Test Wayfair folder name transformation (numbers removed)
        assertEquals("wayfair", psqlCopyRunnerWithTransform.transformRetailerKey("wayfair_north_america_11480"));
        assertEquals("wayfair", psqlCopyRunnerWithTransform.transformRetailerKey("wayfair_north_america_11480/"));

        // Test Walmart folder name transformation (numbers removed)
        assertEquals("walmart", psqlCopyRunnerWithTransform.transformRetailerKey("walmart_affiliate_program_11946"));
        assertEquals("walmart", psqlCopyRunnerWithTransform.transformRetailerKey("walmart_affiliate_program_11946/"));

        // Test Target folder name transformation (numbers removed)
        assertEquals("target", psqlCopyRunnerWithTransform.transformRetailerKey("target_10238"));
        assertEquals("target", psqlCopyRunnerWithTransform.transformRetailerKey("target_10238/"));

        // Test Tractor Supply folder name transformation (numbers removed)
        assertEquals("tractorsupply", psqlCopyRunnerWithTransform.transformRetailerKey("tractor_supply_19274"));
        assertEquals("tractorsupply", psqlCopyRunnerWithTransform.transformRetailerKey("tractor_supply_19274/"));

        // Test special case mappings
        assertEquals("bestbuy", psqlCopyRunnerWithTransform.transformRetailerKey("bestbuy_us_123"));
        assertEquals("hp", psqlCopyRunnerWithTransform.transformRetailerKey("hp_us_456"));
        assertEquals("westelm", psqlCopyRunnerWithTransform.transformRetailerKey("west_elm_us_789"));
        assertEquals("homedepot", psqlCopyRunnerWithTransform.transformRetailerKey("the_home_depot_123"));
        assertEquals("tirerack", psqlCopyRunnerWithTransform.transformRetailerKey("the_tire_rack_456"));

        // Test folder with special characters and numbers (all removed, only letters kept)
        assertEquals("bestbuy", psqlCopyRunnerWithTransform.transformRetailerKey("Best-Buy_123!"));
        assertEquals("amazon", psqlCopyRunnerWithTransform.transformRetailerKey("Amazon@456#"));

        // Test null input
        assertEquals("unknown", psqlCopyRunnerWithTransform.transformRetailerKey(null));

        // Test empty input
        assertEquals("unknown", psqlCopyRunnerWithTransform.transformRetailerKey(""));

        // Test whitespace input
        assertEquals("unknown", psqlCopyRunnerWithTransform.transformRetailerKey("   "));
    }

    @Test
    void testRetailerKeyTransformationFormat() {
        // Test that the transformation follows the required format:
        // retailerName.toLowerCase().replaceAll("[^a-z]", "") - only alphabets allowed

        // Test mixed case with underscores and numbers (numbers removed)
        assertEquals("wayfair", psqlCopyRunnerWithTransform.transformRetailerKey("Wayfair_North_America_11480"));

        // Test with special characters (special chars removed)
        assertEquals("walmart", psqlCopyRunnerWithTransform.transformRetailerKey("Walmart-Affiliate@Program!"));

        // Test with spaces and mixed characters (numbers and spaces removed)
        assertEquals("tractorsupply", psqlCopyRunnerWithTransform.transformRetailerKey("Tractor Supply 123"));

        // Test all lowercase already (numbers removed)
        assertEquals("target", psqlCopyRunnerWithTransform.transformRetailerKey("target_10238"));

        // Test with only special characters and numbers (should result in empty)
        assertEquals("", psqlCopyRunnerWithTransform.transformRetailerKey("!@#$%^&*()123"));

        // Test with only numbers (should result in empty)
        assertEquals("", psqlCopyRunnerWithTransform.transformRetailerKey("123456"));
    }
}
