package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.RetailerKeyMappingProvider;
import com.progleasing.marketplace.etl.config.SovrnRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.ProductDataDisabled;
import com.progleasing.marketplace.etl.entity.SovrnProducts;
import com.progleasing.marketplace.etl.repositories.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class SovrnDataServiceTest {

    @InjectMocks
    SovrnDataService sovrnDataService;

    @Mock
    SovrnRequestConfig sovrnRequestConfig;
    @Mock ProductDataRepository productDataRepository;
    @Mock PriceDataRepository priceDataRepository;
    @Mock ImageDataRepository imageDataRepository;
    @Mock EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    @Mock ProductDataDisabledRepository productDataDisabledRepository;
    @Mock RetailerKeyMappingProvider retailerKeyMappingProvider;

    private SovrnProducts sovrnProduct;
    private ETLMessageDTO messageDTO;

    @BeforeEach
    void setUp() {
        sovrnProduct = new SovrnProducts();
        sovrnProduct.setSku("sku123");
        sovrnProduct.setProductName("Test Product");
        sovrnProduct.setRetailerKey("retKey");
        sovrnProduct.setMerchantName("retName");
        sovrnProduct.setBrand("TestBrand");
        sovrnProduct.setAffiliatedUrl("http://affiliate.url");
        sovrnProduct.setImageUrl("http://image.com");
        sovrnProduct.setThumbnailUrl("http://thumb.com");
        sovrnProduct.setRetailPrice(new BigDecimal("50.00"));
        sovrnProduct.setSalePrice(new BigDecimal("40.00"));

        messageDTO = new ETLMessageDTO();
        messageDTO.setSearchTerm("laptops");
        messageDTO.setCategoryKey("cat123");
        messageDTO.setL1CategoryName("electronics");
        messageDTO.setL2CategoryName("computers");
        messageDTO.setRetailerKey("retKey");
        messageDTO.setRetailerName("retName");
        messageDTO.setReferenceId("ref-123");
    }

    @Test
    void testPopulateProductData_savesDataCorrectly() {
        List<SovrnProducts> input = List.of(sovrnProduct);
        List<String> productIds = new ArrayList<>(List.of("sku123", "sku999"));

        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        when(productDataRepository.findById("sku123"))
                .thenReturn(Optional.empty());
        when(priceDataRepository.findById("sku123"))
                .thenReturn(Optional.empty());
        when(sovrnRequestConfig.isDeleteProducts()).thenReturn(true);
        when(sovrnRequestConfig.getGracePeriodDays()).thenReturn(3);

        sovrnDataService.populateProductData(input, messageDTO, productIds);

        verify(productDataRepository).saveAll(anyList());
        verify(priceDataRepository).saveAll(anyList());
        verify(imageDataRepository).saveAll(anyList());
        verify(productDataRepository).markForDeletion(eq(List.of("sku999")), any());
    }

    @Test
    void testPopulateProductData_skipsDisabledProducts() {
        ProductDataDisabled disabled = new ProductDataDisabled();
        disabled.setId("sku123");

        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any()))
                .thenReturn(List.of(disabled));

        sovrnDataService.populateProductData(List.of(sovrnProduct), messageDTO, new ArrayList<>());

        verify(productDataRepository, never()).saveAll(anyList());
    }

    @Test
    void testPopulateProductData_emptyInput() {
        sovrnDataService.populateProductData(new ArrayList<>(), messageDTO, new ArrayList<>());
        verify(productDataRepository, never()).saveAll(any());
    }

    @Test
    void testPopulateProductData_throwsException() {
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any()))
                .thenThrow(new RuntimeException("DB error"));

        assertThrows(RuntimeException.class, () ->
                sovrnDataService.populateProductData(List.of(sovrnProduct), messageDTO, new ArrayList<>())
        );
    }

    @Test
    void testUpdateStatusQueue_createsNewEntry() {
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(any(), any(), any(), any()))
                .thenReturn(Optional.empty());

        sovrnDataService.updateStatusQueue(messageDTO, "msg-001", EtlQueueStatusData.Status.STARTED);

        verify(etlQueueStatusDataRepository).save(any());
        verify(etlQueueStatusDataRepository).flush();
    }

    @Test
    void testUpdateStatusQueue_updatesExisting() {
        EtlQueueStatusData existing = new EtlQueueStatusData();
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(any(), any(), any(), any()))
                .thenReturn(Optional.of(existing));

        sovrnDataService.updateStatusQueue(messageDTO, "msg-002", EtlQueueStatusData.Status.COMPLETED);

        verify(etlQueueStatusDataRepository).save(existing);
    }






}
