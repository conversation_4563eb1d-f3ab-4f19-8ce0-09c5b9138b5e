package com.progleasing.marketplace.etl.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for SovrnFolderFilterConfig
 * Tests folder ID filtering functionality to ensure only configured folders are processed
 */
@ExtendWith(MockitoExtension.class)
class SovrnFolderFilterConfigTest {

    private SovrnFolderFilterConfig folderFilterConfig;

    @BeforeEach
    void setUp() {
        folderFilterConfig = new SovrnFolderFilterConfig();
        
        // Set up test configuration matching application.yml
        folderFilterConfig.setWalmart("36616,36703,36669,36849,36704,36859,36631,36636");
        folderFilterConfig.setWayfair("11480");
        folderFilterConfig.setTarget("11551");
        
        // Initialize the configuration (calls @PostConstruct method)
        folderFilterConfig.init();
    }

    @Test
    void testInitialization_shouldParseAllFolderIds() {
        // Verify Walmart folder IDs
        Set<String> walmartIds = folderFilterConfig.getFolderIdsByRetailer("walmart");
        assertEquals(8, walmartIds.size());
        assertTrue(walmartIds.contains("36616"));
        assertTrue(walmartIds.contains("36703"));
        assertTrue(walmartIds.contains("36669"));
        assertTrue(walmartIds.contains("36849"));
        assertTrue(walmartIds.contains("36704"));
        assertTrue(walmartIds.contains("36859"));
        assertTrue(walmartIds.contains("36631"));
        assertTrue(walmartIds.contains("36636"));

        // Verify Wayfair folder IDs
        Set<String> wayfairIds = folderFilterConfig.getFolderIdsByRetailer("wayfair");
        assertEquals(1, wayfairIds.size());
        assertTrue(wayfairIds.contains("11480"));

        // Verify Target folder IDs
        Set<String> targetIds = folderFilterConfig.getFolderIdsByRetailer("target");
        assertEquals(1, targetIds.size());
        assertTrue(targetIds.contains("11551"));

        // Verify total count
        Set<String> allIds = folderFilterConfig.getAllAllowedFolderIds();
        assertEquals(10, allIds.size());
    }

    @Test
    void testShouldProcessFolder_allowedFolderIds() {
        // Test Walmart ALLOWED folder IDs (should return true - processed)
        assertTrue(folderFilterConfig.shouldProcessFolder("36616"));
        assertTrue(folderFilterConfig.shouldProcessFolder("36703"));
        assertTrue(folderFilterConfig.shouldProcessFolder("36669"));
        assertTrue(folderFilterConfig.shouldProcessFolder("36849"));
        assertTrue(folderFilterConfig.shouldProcessFolder("36704"));
        assertTrue(folderFilterConfig.shouldProcessFolder("36859"));
        assertTrue(folderFilterConfig.shouldProcessFolder("36631"));
        assertTrue(folderFilterConfig.shouldProcessFolder("36636"));

        // Test Wayfair ALLOWED folder ID (should return true - processed)
        assertTrue(folderFilterConfig.shouldProcessFolder("11480"));

        // Test Target ALLOWED folder ID (should return true - processed)
        assertTrue(folderFilterConfig.shouldProcessFolder("11551"));
    }

    @Test
    void testShouldProcessFolder_disallowedFolderIds() {
        // Test folder IDs that should NOT be processed (not in allowed list)
        assertFalse(folderFilterConfig.shouldProcessFolder("12345"));
        assertFalse(folderFilterConfig.shouldProcessFolder("99999"));
        assertFalse(folderFilterConfig.shouldProcessFolder("11479")); // Close to Wayfair but not exact
        assertFalse(folderFilterConfig.shouldProcessFolder("11481")); // Close to Wayfair but not exact
        assertFalse(folderFilterConfig.shouldProcessFolder("11550")); // Close to Target but not exact
        assertFalse(folderFilterConfig.shouldProcessFolder("11552")); // Close to Target but not exact
        assertFalse(folderFilterConfig.shouldProcessFolder("36615")); // Close to Walmart but not exact
        assertFalse(folderFilterConfig.shouldProcessFolder("36617")); // Close to Walmart but not exact
    }

    @Test
    void testShouldProcessFolder_edgeCases() {
        // Test null and empty cases (should not process by default)
        assertFalse(folderFilterConfig.shouldProcessFolder(null));
        assertFalse(folderFilterConfig.shouldProcessFolder(""));
        assertFalse(folderFilterConfig.shouldProcessFolder("   "));

        // Test whitespace handling (allowed IDs should still be allowed even with whitespace)
        assertTrue(folderFilterConfig.shouldProcessFolder("  36616  "));
        assertTrue(folderFilterConfig.shouldProcessFolder(" 11480 "));
        assertTrue(folderFilterConfig.shouldProcessFolder("11551   "));
    }

    @Test
    void testExtractFolderIdFromS3Key_walmartPaths() {
        // Test Walmart affiliate program paths (actual structure from logs)
        String s3Key1 = "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36616.csv.gz";
        assertEquals("36616", folderFilterConfig.extractFolderIdFromS3Key(s3Key1));

        String s3Key2 = "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36703.csv.gz";
        assertEquals("36703", folderFilterConfig.extractFolderIdFromS3Key(s3Key2));

        String s3Key3 = "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36669.csv.gz";
        assertEquals("36669", folderFilterConfig.extractFolderIdFromS3Key(s3Key3));
    }

    @Test
    void testExtractFolderIdFromS3Key_wayfairPaths() {
        // Test Wayfair paths (actual structure from logs)
        String s3Key1 = "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_11480/14551.csv.gz";
        assertEquals("14551", folderFilterConfig.extractFolderIdFromS3Key(s3Key1));

        String s3Key2 = "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_48541/50424.csv.gz";
        assertEquals("50424", folderFilterConfig.extractFolderIdFromS3Key(s3Key2));
    }

    @Test
    void testExtractFolderIdFromS3Key_targetPaths() {
        // Test Target paths (actual structure from logs)
        String s3Key1 = "feed_exports/ea3e5537553c6cf739af818331fa97f2/target_affiliate_program_11551/11551.csv.gz";
        assertEquals("11551", folderFilterConfig.extractFolderIdFromS3Key(s3Key1));

        String s3Key2 = "feed_exports/ea3e5537553c6cf739af818331fa97f2/target_affiliate_program_11549/11549.csv.gz";
        assertEquals("11549", folderFilterConfig.extractFolderIdFromS3Key(s3Key2));
    }

    @Test
    void testExtractFolderIdFromS3Key_invalidPaths() {
        // Test paths that should return null
        assertNull(folderFilterConfig.extractFolderIdFromS3Key(null));
        assertNull(folderFilterConfig.extractFolderIdFromS3Key(""));
        assertNull(folderFilterConfig.extractFolderIdFromS3Key("invalid/path"));
        assertNull(folderFilterConfig.extractFolderIdFromS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/unknown_program/12345/file.csv.gz"));
        assertNull(folderFilterConfig.extractFolderIdFromS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/file.csv.gz")); // No folder structure
    }

    @Test
    void testShouldProcessS3Key_allowedPaths() {
        // Test S3 keys that should be processed (actual structure from logs)

        // Allowed Walmart folder IDs
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36616.csv.gz"));
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36703.csv.gz"));

        // Allowed Wayfair folder IDs
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_11480/11480.csv.gz"));

        // Allowed Target folder IDs
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/11551.csv.gz"));

        // Other retailers (should be processed without filtering)
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/adorama_11359/35134.csv.gz"));
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/ashley_44337/47577.csv.gz"));
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/best_buy_u.s_39734/83326.csv.gz"));
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/dell_home_&_home_office_814/13622.csv.gz"));
        assertTrue(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/hp_us_106183/79185.csv.gz"));
    }

    @Test
    void testShouldProcessS3Key_disallowedPaths() {
        // Test S3 keys that should NOT be processed (only for filtered retailers)

        // Non-allowed Walmart folder IDs (should be skipped)
        assertFalse(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/12345.csv.gz"));
        assertFalse(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/99999.csv.gz"));
        assertFalse(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36617.csv.gz"));

        // Non-allowed Wayfair folder IDs (should be skipped)
        assertFalse(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_11480/11479.csv.gz"));
        assertFalse(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_48541/50424.csv.gz"));

        // Non-allowed Target folder IDs (should be skipped)
        assertFalse(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/11550.csv.gz"));
        assertFalse(folderFilterConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/11549.csv.gz"));

        // Invalid paths (should be skipped only if null or empty)
        assertTrue(folderFilterConfig.shouldProcessS3Key("invalid/path/structure")); // Other retailer, should process
        assertFalse(folderFilterConfig.shouldProcessS3Key(null)); // Null should be skipped
        assertFalse(folderFilterConfig.shouldProcessS3Key("")); // Empty should be skipped
    }

    @Test
    void testGetRetailerTypeFromS3Key() {
        // Test retailer type detection
        assertEquals("walmart", folderFilterConfig.getRetailerTypeFromS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36616.csv.gz"));
        assertEquals("wayfair", folderFilterConfig.getRetailerTypeFromS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_11480/11480.csv.gz"));
        assertEquals("target", folderFilterConfig.getRetailerTypeFromS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/11551.csv.gz"));
        assertEquals("other", folderFilterConfig.getRetailerTypeFromS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/adorama_11359/35134.csv.gz"));
        assertEquals("other", folderFilterConfig.getRetailerTypeFromS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/ashley_44337/47577.csv.gz"));
        assertEquals("unknown", folderFilterConfig.getRetailerTypeFromS3Key(null));
        assertEquals("unknown", folderFilterConfig.getRetailerTypeFromS3Key(""));
    }

    @Test
    void testIsFilteredRetailer() {
        // Test filtered retailer detection
        assertTrue(folderFilterConfig.isFilteredRetailer("walmart"));
        assertTrue(folderFilterConfig.isFilteredRetailer("wayfair"));
        assertTrue(folderFilterConfig.isFilteredRetailer("target"));
        assertFalse(folderFilterConfig.isFilteredRetailer("other"));
        assertFalse(folderFilterConfig.isFilteredRetailer("unknown"));
        assertFalse(folderFilterConfig.isFilteredRetailer("adorama"));
        assertFalse(folderFilterConfig.isFilteredRetailer(null));
    }

    @Test
    void testGetRetailerNameByFolderId() {
        // Test Walmart folder IDs
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36616"));
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36703"));
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36669"));
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36849"));
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36704"));
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36859"));
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36631"));
        assertEquals("Walmart", folderFilterConfig.getRetailerNameByFolderId("36636"));

        // Test Wayfair folder ID
        assertEquals("Wayfair", folderFilterConfig.getRetailerNameByFolderId("11480"));

        // Test Target folder ID
        assertEquals("Target", folderFilterConfig.getRetailerNameByFolderId("11551"));

        // Test unknown folder IDs
        assertEquals("Unknown", folderFilterConfig.getRetailerNameByFolderId("12345"));
        assertEquals("Unknown", folderFilterConfig.getRetailerNameByFolderId("99999"));
        assertEquals("Unknown", folderFilterConfig.getRetailerNameByFolderId(null));
        assertEquals("Unknown", folderFilterConfig.getRetailerNameByFolderId(""));
    }

    @Test
    void testGetFolderIdsByRetailer_caseInsensitive() {
        // Test case insensitive retailer names
        assertEquals(8, folderFilterConfig.getFolderIdsByRetailer("WALMART").size());
        assertEquals(8, folderFilterConfig.getFolderIdsByRetailer("walmart").size());
        assertEquals(8, folderFilterConfig.getFolderIdsByRetailer("Walmart").size());

        assertEquals(1, folderFilterConfig.getFolderIdsByRetailer("WAYFAIR").size());
        assertEquals(1, folderFilterConfig.getFolderIdsByRetailer("wayfair").size());
        assertEquals(1, folderFilterConfig.getFolderIdsByRetailer("Wayfair").size());

        assertEquals(1, folderFilterConfig.getFolderIdsByRetailer("TARGET").size());
        assertEquals(1, folderFilterConfig.getFolderIdsByRetailer("target").size());
        assertEquals(1, folderFilterConfig.getFolderIdsByRetailer("Target").size());

        // Test unknown retailer
        assertTrue(folderFilterConfig.getFolderIdsByRetailer("unknown").isEmpty());
        assertTrue(folderFilterConfig.getFolderIdsByRetailer(null).isEmpty());
    }

    @Test
    void testConfigurationWithEmptyValues() {
        // Test configuration with empty values
        SovrnFolderFilterConfig emptyConfig = new SovrnFolderFilterConfig();
        emptyConfig.setWalmart("");
        emptyConfig.setWayfair(null);
        emptyConfig.setTarget("   ");
        emptyConfig.init();

        assertTrue(emptyConfig.getAllAllowedFolderIds().isEmpty());
        assertFalse(emptyConfig.shouldProcessFolder("36616")); // Should not process when no allowed folders
        assertFalse(emptyConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/36616.csv.gz"));

        // But other retailers should still be processed
        assertTrue(emptyConfig.shouldProcessS3Key("feed_exports/ea3e5537553c6cf739af818331fa97f2/adorama_11359/35134.csv.gz"));
    }

    @Test
    void testConfigurationWithWhitespaceInIds() {
        // Test configuration with whitespace in folder IDs
        SovrnFolderFilterConfig whitespaceConfig = new SovrnFolderFilterConfig();
        whitespaceConfig.setWalmart(" 36616 , 36703 ,36669,  36849  ");
        whitespaceConfig.setWayfair("  11480  ");
        whitespaceConfig.setTarget("11551,  ,"); // Include empty values
        whitespaceConfig.init();

        // Should handle whitespace correctly (these should be allowed)
        assertTrue(whitespaceConfig.shouldProcessFolder("36616"));
        assertTrue(whitespaceConfig.shouldProcessFolder("36703"));
        assertTrue(whitespaceConfig.shouldProcessFolder("36669"));
        assertTrue(whitespaceConfig.shouldProcessFolder("36849"));
        assertTrue(whitespaceConfig.shouldProcessFolder("11480"));
        assertTrue(whitespaceConfig.shouldProcessFolder("11551"));

        // Should have correct count (empty values filtered out)
        assertEquals(6, whitespaceConfig.getAllAllowedFolderIds().size());
    }

    @Test
    @DisplayName("Test specific folder list - which folders are processed vs skipped")
    void testSpecificFolderListProcessing() {
        System.out.println("\n=== TESTING SPECIFIC FOLDER LIST ===");

        // Your provided folder list
        List<String> testFolders = Arrays.asList(
            "williams-sonoma_38699/",
            "west_elm_us_38693/",
            "wayfair_north_america_48541/",
            "wayfair_north_america_11480/",
            "walmart_affiliate_program_11946/",
            "the_tire_rack_46739/",
            "the_tire_rack_24608/",
            "the_home_depot_10334/",
            "target_10238/",
            "sur_la_table_89909/",
            "mattress_firm_47236/",
            "lowe's_25447/",
            "hp_us_106183/",
            "golf_galaxy_82062/",
            "gamestop,_inc._43939/",
            "dyson_inc._11127/",
            "dell_home_&_home_office_814/",
            "cost_plus_world_market_10203/",
            "best_buy_u.s_39734/",
            "ashley_44337/",
            "adorama_11359/"
        );

        System.out.println("\nFOLDERS THAT WILL BE PROCESSED:");
        System.out.println("=".repeat(50));

        int processedCount = 0;
        int skippedCount = 0;

        for (String folder : testFolders) {
            String folderId = extractFolderIdFromFolderName(folder);
            String retailerType = getRetailerTypeFromFolderName(folder);
            boolean shouldProcess = shouldProcessFolderBasedOnType(folder, folderId, retailerType);

            if (shouldProcess) {
                processedCount++;
                String retailerName = folderFilterConfig.getRetailerNameByFolderId(folderId);
                System.out.println(String.format("PROCESSING: %s (ID: %s, Type: %s, Retailer: %s)",
                    folder, folderId, retailerType, retailerName));
            }
        }

        System.out.println("\nFOLDERS THAT WILL BE SKIPPED:");
        System.out.println("=".repeat(50));

        for (String folder : testFolders) {
            String folderId = extractFolderIdFromFolderName(folder);
            String retailerType = getRetailerTypeFromFolderName(folder);
            boolean shouldProcess = shouldProcessFolderBasedOnType(folder, folderId, retailerType);

            if (!shouldProcess) {
                skippedCount++;
                String reason = getSkipReason(folderId, retailerType);
                System.out.println(String.format("SKIPPING: %s (ID: %s, Type: %s, Reason: %s)",
                    folder, folderId, retailerType, reason));
            }
        }

        System.out.println("\nPROCESSING SUMMARY:");
        System.out.println("=".repeat(50));
        System.out.println("Total folders: " + testFolders.size());
        System.out.println("Will be PROCESSED: " + processedCount);
        System.out.println("Will be SKIPPED: " + skippedCount);
        System.out.println("Processing rate: " + String.format("%.1f%%", (processedCount * 100.0 / testFolders.size())));

        // Assertions
        assertTrue(processedCount > 0, "At least some folders should be processed");
        assertEquals(testFolders.size(), processedCount + skippedCount, "All folders should be accounted for");
    }

    @Test
    @DisplayName("Test S3 key processing for specific folder list")
    void testS3KeyProcessingForSpecificFolders() {
        System.out.println("\n=== S3 KEY PROCESSING FOR SPECIFIC FOLDERS ===");

        // Create sample S3 keys based on your folder list
        List<String> sampleS3Keys = Arrays.asList(
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/williams-sonoma_38699/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/west_elm_us_38693/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_48541/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/wayfair_north_america_11480/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/walmart_affiliate_program_11946/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/the_tire_rack_46739/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/target_10238/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/best_buy_u.s_39734/products.csv.gz",
            "feed_exports/ea3e5537553c6cf739af818331fa97f2/adorama_11359/products.csv.gz"
        );

        System.out.println("\nS3 KEY PROCESSING RESULTS:");
        System.out.println("=".repeat(80));

        for (String s3Key : sampleS3Keys) {
            boolean shouldProcess = folderFilterConfig.shouldProcessS3Key(s3Key);
            String folderId = folderFilterConfig.extractFolderIdFromS3Key(s3Key);
            String retailerType = folderFilterConfig.getRetailerTypeFromS3Key(s3Key);

            System.out.println(String.format("%s %s",
                shouldProcess ? "PROCESS:" : "SKIP:",
                s3Key));
            System.out.println(String.format("   Folder ID: %s, Retailer Type: %s", folderId, retailerType));
        }
    }

    /**
     * Helper method to extract folder ID from folder name
     * Example: "walmart_affiliate_program_11946/" -> "11946"
     */
    private String extractFolderIdFromFolderName(String folderName) {
        if (folderName == null || folderName.trim().isEmpty()) {
            return null;
        }

        // Remove trailing slash if present
        String cleanName = folderName.endsWith("/") ? folderName.substring(0, folderName.length() - 1) : folderName;

        // Extract the number at the end of the folder name
        String[] parts = cleanName.split("_");
        String lastPart = parts[parts.length - 1];

        return lastPart;
    }

    /**
     * Helper method to determine retailer type from folder name
     */
    private String getRetailerTypeFromFolderName(String folderName) {
        if (folderName == null) return "unknown";

        String lowerName = folderName.toLowerCase();
        if (lowerName.contains("walmart")) return "walmart";
        if (lowerName.contains("wayfair")) return "wayfair";
        if (lowerName.contains("target")) return "target";

        return "other";
    }

    /**
     * Helper method to determine if a folder should be processed based on retailer type and configuration
     */
    private boolean shouldProcessFolderBasedOnType(String folderName, String folderId, String retailerType) {
        // For filtered retailers (walmart, wayfair, target), check if folder ID is allowed
        if (folderFilterConfig.isFilteredRetailer(retailerType)) {
            return folderFilterConfig.shouldProcessFolder(folderId);
        }

        // For other retailers, always process
        return true;
    }

    /**
     * Helper method to get the reason why a folder is being skipped
     */
    private String getSkipReason(String folderId, String retailerType) {
        if (folderFilterConfig.isFilteredRetailer(retailerType)) {
            return "Folder ID not in allowed list for " + retailerType;
        }
        return "Should not be skipped"; // This shouldn't happen for "other" retailers
    }
}
