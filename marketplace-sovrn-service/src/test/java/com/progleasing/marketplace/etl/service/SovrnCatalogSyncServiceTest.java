package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.SovrnRequestConfig;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.entity.SovrnProducts;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.repository.SovrnProductsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class SovrnCatalogSyncServiceTest {
    @Mock
    private SovrnRequestConfig sovrnRequestConfig;

    @Spy
    private ObjectMapper objectMapper;

    @InjectMocks
    SovrnCatalogSyncService sovrnCatalogSyncService;

    @Mock
    private SovrnProductsRepository sovrnProductsRepository;
    @Mock
    private ProductDataRepository productDataRepository;

    @Mock
    private SovrnDataService sovrnDataService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        this.sovrnCatalogSyncService = new SovrnCatalogSyncService(this.sovrnRequestConfig,this.objectMapper,this.sovrnProductsRepository,
                this.productDataRepository,this.sovrnDataService);

    }

    @Test
    void testSyncCatalogItems_noItemsInSearch() throws Exception {
        String messageBody = "{" +
                "\"categoryKey\":\"golf\"," +
                "\"searchTerm\":\"push cart\"," +
                "\"retailerName\":\"Game Stop\"," +
                "\"retailerKey\":\"pga\"," +
                "\"minPrice\":100," +
                "\"maxPrice\":1000," +
                "\"referenceId\":\"ref-123456789\"" +
                "}";
        String messageId = "msg123";
        ProductData existingProduct = new ProductData();
        existingProduct.setId("123");
        when(sovrnRequestConfig.getMaxProductsToImport()).thenReturn(100);
        Page<SovrnProducts> emptyPage = new PageImpl<>(Collections.emptyList());
        when(sovrnProductsRepository.findAll( any(Specification.class), any(Pageable.class)))
                .thenReturn(emptyPage);
        sovrnCatalogSyncService.syncCatalogItems(messageBody, messageId);
        verify(sovrnDataService, never()).populateProductData(any(),any(),anyList());
    }

    @Test
    void testSyncCatalogItems_itemsPresentInSearchResult() throws Exception {
        String messageBody = "{" +
                "\"categoryKey\":\"golf\"," +
                "\"searchTerm\":\"push cart\"," +
                "\"retailerName\":\"Game Stop\"," +
                "\"retailerKey\":\"pga\"," +
                "\"minPrice\":100," +
                "\"maxPrice\":1000," +
                "\"referenceId\":\"ref-123456789\"" +
                "}";
        String messageId = "msg123";
        ProductData existingProduct = new ProductData();
        existingProduct.setId("123");
        when(sovrnRequestConfig.getMaxProductsToImport()).thenReturn(100);
        Page<SovrnProducts> sovrnProducts = new PageImpl<>(createSovrnProductsList());
        when(sovrnProductsRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(sovrnProducts);
        sovrnCatalogSyncService.syncCatalogItems(messageBody, messageId);
        verify(sovrnDataService, times(1)).populateProductData(any(),any(),anyList());
    }


    public static List<SovrnProducts> createSovrnProductsList() {
        List<SovrnProducts> list = new ArrayList<>();

        for (int i = 1; i <= 3; i++) {
            SovrnProducts product = new SovrnProducts();
            product.setSku("sku-" + i);
            product.setRetailerKey("retailer" + i);
            product.setEan("ean-" + i);
            product.setIsbn("isbn-" + i);
            product.setMpn("mpn-" + i);
            product.setUpc("upc-" + i);
            product.setProductName("Test Product " + i);
            product.setBrand("Brand" + i);
            product.setLanguage("en");
            product.setInStock("yes");
            product.setMerchantName("Merchant " + i);
            product.setMerchantRawCategory("Category " + i);
            product.setRetailPrice(BigDecimal.valueOf(100 + i));
            product.setSalePrice(BigDecimal.valueOf(90 + i));
            product.setImageUrl("http://example.com/image" + i + ".jpg");
            product.setThumbnailUrl("http://example.com/thumb" + i + ".jpg");
            product.setAffiliatedUrl("http://example.com/product" + i);
            product.setDomain("example.com");
            list.add(product);
        }
        return list;
    }
}