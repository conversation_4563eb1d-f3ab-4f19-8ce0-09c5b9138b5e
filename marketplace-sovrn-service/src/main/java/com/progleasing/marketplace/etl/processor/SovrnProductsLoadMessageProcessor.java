package com.progleasing.marketplace.etl.processor;



import com.progleasing.marketplace.etl.repository.SovrnProductsRepository;
import com.progleasing.marketplace.etl.sovrnProductsLoadService.SovrnS3CopyService;
import com.progleasing.marketplace.etl.sovrnProductsLoadService.SovrnFileProcessorService;
import com.progleasing.marketplace.etl.sovrnProductsLoadService.SovrnSelectiveRetailerProcessingService;
import com.progleasing.marketplace.etl.config.SovrnFolderFilterConfig;
import com.progleasing.marketplace.etl.repository.SovrnProductsRepository;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.sovrn-products-load.listener.enabled", havingValue = "true", matchIfMissing = true)
public class SovrnProductsLoadMessageProcessor {

    private final SovrnS3CopyService sovrnS3CopyService;
    private final SovrnFileProcessorService sovrnFileProcessorService;
    private final SovrnSelectiveRetailerProcessingService selectiveRetailerProcessingService;
    private final SovrnProductsRepository sovrnProductsRepository;
    private final SovrnFolderFilterConfig folderFilterConfig;
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);

    // Pattern to extract allowed-retailers from payload
    private static final Pattern ALLOWED_RETAILERS_PATTERN = Pattern.compile("\"allowed-retailers\"\\s*:\\s*\"(\\d+(?:,\\d+)*)\"");

    public SovrnProductsLoadMessageProcessor(SovrnS3CopyService sovrnS3CopyService,
                                           SovrnFileProcessorService sovrnFileProcessorService,
                                           SovrnSelectiveRetailerProcessingService selectiveRetailerProcessingService,
                                           SovrnProductsRepository sovrnProductsRepository,
                                           SovrnFolderFilterConfig folderFilterConfig) {
        this.sovrnS3CopyService = sovrnS3CopyService;
        this.sovrnFileProcessorService = sovrnFileProcessorService;
        this.selectiveRetailerProcessingService = selectiveRetailerProcessingService;
        this.sovrnProductsRepository = sovrnProductsRepository;
        this.folderFilterConfig = folderFilterConfig;
    }

    @SqsListener(value = "${aws.sqs.sovrn-products-load.queue-name}",
            maxConcurrentMessages = "${aws.sqs.concurrent-message}", acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL
    )
    public void handle(List<Message<String>> messages) {
        log.info("Messages received: {} (count: {})", LocalDate.now(), messages.size());

        if (!isProcessing.compareAndSet(false, true)) {
            log.info("Processing already in progress. Acknowledging {} messages without processing.", messages.size());
            for (Message<String> message : messages) {
                try {
                    Acknowledgement.acknowledge(message);
                    UUID messageId = (UUID) message.getHeaders().get("id");
                    log.info("Message acknowledged (skipped processing): {}", messageId.toString());
                } catch (Exception e) {
                    log.error("Error acknowledging message: {}", e.getMessage(), e);
                }
            }
            return;
        }

        try {
            for (Message<String> message : messages) {
                try {
                    UUID messageId = (UUID) message.getHeaders().get("id");
                    String payload = message.getPayload();
                    log.info("Received message with payload: {} and id {}", payload, messageId.toString());

                    Acknowledgement.acknowledge(message);
                    log.info("Message acknowledged immediately: {}", messageId.toString());

                    if (message == messages.get(0)) { // Only process for the first message
                        // Extract allowed-retailers from payload
                        String allowedRetailers = extractAllowedRetailersFromPayload(payload);

                        if (allowedRetailers != null) {
                            long selectiveStartTime = System.currentTimeMillis();
                            String[] retailerIds = allowedRetailers.trim().split(",");
                            if (retailerIds.length == 1) {
                                log.info("Starting selective retailer processing for retailer ID: {}", allowedRetailers);
                            } else {
                                log.info("Starting selective retailer processing for {} retailer IDs: {}", retailerIds.length, allowedRetailers);
                            }

                            // Use selective retailer processing service
                            selectiveRetailerProcessingService.processRetailersSelectively(allowedRetailers);

                            long selectiveEndTime = System.currentTimeMillis();
                            long selectiveDuration = selectiveEndTime - selectiveStartTime;
                            if (retailerIds.length == 1) {
                                log.info("Selective retailer processing completed for retailer ID: {} in {} ms ({} minutes)",
                                        allowedRetailers, selectiveDuration, selectiveDuration / 60000.0);
                            } else {
                                log.info("Selective retailer processing completed for {} retailer IDs: {} in {} ms ({} minutes)",
                                        retailerIds.length, allowedRetailers, selectiveDuration, selectiveDuration / 60000.0);
                            }
                        } else {
                            long endToEndStartTime = System.currentTimeMillis();
                            log.info("Starting complete Sovrn processing pipeline (no specific retailer requested)...");

                            // Step 0: Delete records only for configured retailers (preserves other retailer data)
                            log.info("Step 0: Deleting records for configured retailers only (preserving non-configured retailer data)...");
                            long deleteStartTime = System.currentTimeMillis();
                            deleteConfiguredRetailersData();
                            long deleteEndTime = System.currentTimeMillis();
                            log.info("Step 0 completed: Configured retailer records deleted in {} ms", (deleteEndTime - deleteStartTime));

                            // Step 1: Copy files from Sovrn S3 to PL S3
                            log.info("Step 1: Copying files from Sovrn S3 to PL S3...");
                            long copyStartTime = System.currentTimeMillis();
                            sovrnS3CopyService.copyFilesFromSovrnToPL();
                            long copyEndTime = System.currentTimeMillis();
                            log.info("Step 1 completed: Files copied to PL S3 in {} ms ({} minutes)",
                                    (copyEndTime - copyStartTime), (copyEndTime - copyStartTime) / 60000.0);

                            // Step 2: Process files from PL S3 and import to database
                            log.info("Step 2: Processing files from PL S3 and importing to database...");
                            long processStartTime = System.currentTimeMillis();
                            sovrnFileProcessorService.processFilesFromPLS3();
                            long processEndTime = System.currentTimeMillis();
                            log.info("Step 2 completed: Files processed and imported to database in {} ms ({} minutes)",
                                    (processEndTime - processStartTime), (processEndTime - processStartTime) / 60000.0);

                            // Log total end-to-end time
                            long endToEndEndTime = System.currentTimeMillis();
                            long endToEndDuration = endToEndEndTime - endToEndStartTime;
                            log.info("Complete Sovrn processing pipeline finished successfully in {} ms ({} minutes)",
                                    endToEndDuration, endToEndDuration / 60000.0);
                        }
                    }

                } catch (Exception e) {
                    log.error("Error processing message: {}", e.getMessage(), e);
                }
            }
        } finally {
            // Reset the processing flag
            isProcessing.set(false);
            log.info("Processing flag reset. Ready for next batch of messages.");
        }
    }

    private String extractAllowedRetailersFromPayload(String payload) {
        if (payload == null || payload.trim().isEmpty()) {
            return null;
        }

        try {
            Matcher matcher = ALLOWED_RETAILERS_PATTERN.matcher(payload);
            if (matcher.find()) {
                String allowedRetailers = matcher.group(1);
                log.info("Extracted allowed-retailers from payload: {}", allowedRetailers);
                return allowedRetailers;
            } else {
                log.debug("No allowed-retailers found in payload: {}", payload);
                return null;
            }
        } catch (Exception e) {
            log.warn("Error parsing allowed-retailers from payload '{}': {}", payload, e.getMessage());
            return null;
        }
    }

    /**
     * Delete records only for retailers that are configured in allowed-folder-ids and allowed-file-ids
     * This dynamically reads the configuration and deletes only configured retailers
     */
    private void deleteConfiguredRetailersData() {
        try {
            log.info("=== DELETING CONFIGURED RETAILER DATA ===");
            log.info("Reading configuration to determine which retailers to delete...");

            // Get all configured retailer keys from the filtering configuration
            Set<String> configuredRetailerKeys = getConfiguredRetailerKeys();

            if (configuredRetailerKeys.isEmpty()) {
                log.warn("No retailers configured in allowed-folder-ids or allowed-file-ids - no data will be deleted");
                return;
            }

            log.info("Found {} configured retailer(s): {}", configuredRetailerKeys.size(), configuredRetailerKeys);

            int totalDeleted = 0;
            for (String retailerKey : configuredRetailerKeys) {
                log.info("Deleting existing records for retailer_key = '{}'...", retailerKey);
                int deleted = sovrnProductsRepository.deleteByRetailerKey(retailerKey);
                log.info("Deleted {} records for retailer_key = '{}'", deleted, retailerKey);
                totalDeleted += deleted;
            }

            log.info("=== DELETION SUMMARY ===");
            log.info("Total records deleted for configured retailers: {}", totalDeleted);
            log.info("Records for non-configured retailers are preserved");

        } catch (Exception e) {
            log.error("Failed to delete configured retailer data: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to delete configured retailer data", e);
        }
    }

    /**
     * Get all retailer keys that are configured in allowed-folder-ids and allowed-file-ids
     * This dynamically discovers ALL configured retailers from the application configuration
     * Supports all 318+ retailers in Sovrn S3, not just a hardcoded subset
     */
    private Set<String> getConfiguredRetailerKeys() {
        Set<String> retailerKeys = new HashSet<>();

        try {
            log.info("=== DISCOVERING CONFIGURED RETAILERS ===");
            log.info("Scanning SovrnFolderFilterConfig for ALL configured retailers...");

            // Get all configured folder IDs from the configuration
            Set<String> allConfiguredFolderIds = folderFilterConfig.getAllAllowedFolderIds();
            log.info("Found {} total configured folder IDs: {}", allConfiguredFolderIds.size(), allConfiguredFolderIds);

            // For each configured folder ID, determine which retailer it belongs to
            for (String folderId : allConfiguredFolderIds) {
                String retailerName = folderFilterConfig.getRetailerNameByFolderId(folderId);
                if (retailerName != null && !retailerName.equals("Unknown")) {
                    String retailerKey = retailerName.toLowerCase(); // Convert to database key format
                    retailerKeys.add(retailerKey);
                    log.info("Folder ID {} belongs to retailer '{}' -> retailer_key = '{}'",
                            folderId, retailerName, retailerKey);
                }
            }

            log.info("=== CONFIGURATION DISCOVERY COMPLETE ===");
            log.info("Total configured retailer keys: {} -> {}", retailerKeys.size(), retailerKeys);
            log.info("These retailers will be deleted and refreshed during processing");
            log.info("All other retailers (out of 318+ in Sovrn S3) will be skipped and preserved");

        } catch (Exception e) {
            log.error("Error reading configured retailer keys: {}", e.getMessage(), e);
            log.warn("Falling back to empty retailer set - no retailers will be deleted");
        }

        return retailerKeys;
    }
}
