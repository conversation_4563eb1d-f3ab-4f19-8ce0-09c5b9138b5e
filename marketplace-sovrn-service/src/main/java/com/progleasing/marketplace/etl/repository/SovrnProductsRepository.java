package com.progleasing.marketplace.etl.repository;

import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.entity.SovrnProducts;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SovrnProductsRepository
        extends JpaRepository<SovrnProducts, Long>, JpaSpecificationExecutor<SovrnProducts> {

    @Query("SELECT s FROM SovrnProducts s " +
            "WHERE (LOWER(s.productName) LIKE LOWER(CONCAT('%', :text, '%')) " +
            "OR LOWER(s.merchantRawCategory) LIKE LOWER(CONCAT('%', :text, '%'))) " +
            "AND s.retailerKey IN :retailers")
    Page<SovrnProducts> searchByText(@Param("text") String text,
                                     @Param("retailers") List<String> retailers,
                                     Pageable pageable);

    @Modifying
    @Transactional
    @Query(value = "TRUNCATE TABLE sovrn_products RESTART IDENTITY", nativeQuery = true)
    void truncateTable();

    @Modifying
    @Transactional
    void deleteByCreationDateBefore(LocalDateTime cutoffDate);

    @Modifying
    @Transactional
    @Query("DELETE FROM SovrnProducts sp WHERE sp.retailerKey = :retailerKey")
    int deleteByRetailerKey(@Param("retailerKey") String retailerKey);
}
