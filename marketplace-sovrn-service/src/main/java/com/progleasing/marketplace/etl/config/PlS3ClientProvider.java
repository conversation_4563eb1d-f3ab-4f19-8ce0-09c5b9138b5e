package com.progleasing.marketplace.etl.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.core.retry.RetryPolicy;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;

import java.time.Duration;


@Component("pl-client-provider")
public class PlS3ClientProvider {

    @Value("${product.pl.s3.folder}")
    private String plS3Folder;

    @Value("${product.pl.s3.bucket}")
    private String plS3BucketName;

    @Value("${spring.cloud.aws.region.static}")
    private String awsRegion;

    // Connection pool and timeout configuration for large file processing
    @Value("${product.pl.s3.connection.max-connections:100}")
    private int maxConnections;

    @Value("${product.pl.s3.connection.connection-timeout-seconds:60}")
    private int connectionTimeoutSeconds;

    @Value("${product.pl.s3.connection.socket-timeout-minutes:10}")
    private int socketTimeoutMinutes;

    @Value("${product.pl.s3.connection.api-call-timeout-minutes:30}")
    private int apiCallTimeoutMinutes;

    @Value("${product.pl.s3.connection.retry-attempts:3}")
    private int retryAttempts;

    @Autowired
    private S3Client s3Client;

    public S3Client createPlS3Client() {
        // Configure HTTP client with connection pool and timeout settings for large file processing
        ApacheHttpClient httpClient = (ApacheHttpClient) ApacheHttpClient.builder()
                .maxConnections(maxConnections)                                    // Configurable max connections
                .connectionTimeout(Duration.ofSeconds(connectionTimeoutSeconds))  // Configurable connection timeout
                .socketTimeout(Duration.ofMinutes(socketTimeoutMinutes))          // Configurable socket timeout for large files
                .connectionTimeToLive(Duration.ofMinutes(5))                      // Connection TTL: 5 minutes
                .useIdleConnectionReaper(true)                                    // Enable connection reaper
                .build();

        // Configure client override with retry policy
        ClientOverrideConfiguration clientConfig = ClientOverrideConfiguration.builder()
                .apiCallTimeout(Duration.ofMinutes(apiCallTimeoutMinutes))        // Configurable API call timeout
                .apiCallAttemptTimeout(Duration.ofMinutes(apiCallTimeoutMinutes / 2)) // Per attempt timeout: half of total
                .retryPolicy(RetryPolicy.builder()
                        .numRetries(retryAttempts)                                 // Configurable retry attempts
                        .build())
                .build();

        // Create S3 client with enhanced configuration for large file processing
        return S3Client.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .httpClient(httpClient)                        // Use custom HTTP client
                .overrideConfiguration(clientConfig)          // Use custom client configuration
                .serviceConfiguration(S3Configuration.builder()
                        .pathStyleAccessEnabled(false)
                        .chunkedEncodingEnabled(true)
                        .build())
                .build();
    }

    public String getPlBucketName() {
        return plS3BucketName;
    }
}