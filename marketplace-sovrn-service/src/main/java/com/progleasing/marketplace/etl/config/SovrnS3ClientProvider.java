package com.progleasing.marketplace.etl.config;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.S3Client;

@Component("sovrn-client-provider")
public class SovrnS3ClientProvider {

    @Value("${product.sovrn-products-load.s3.s3-uri}")
    private String SOVRN_S3_URI;

    @Qualifier("sovrnS3Client")
    @Autowired
    private S3Client sovrnS3Client;

    public S3Client createSovrnS3Client() {
        return sovrnS3Client;
    }

    public String getSovrnBucketName() {
        return SOVRN_S3_URI.split("/")[2];
    }

    public String getSovrnPrefix() {
        return SOVRN_S3_URI.substring(SOVRN_S3_URI.indexOf("/", 5) + 1);
    }
}
