package com.progleasing.marketplace.etl.sovrnProductsLoadService;

import com.progleasing.marketplace.etl.config.PlS3ClientProvider;
import com.progleasing.marketplace.etl.service.S3ToPostgresLoaderService;
import com.progleasing.marketplace.etl.service.SovrnUnifiedFilterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;

/**
 * Service to process files from PL S3:
 * 1. Download and decompress .gz files
 * 2. Split large CSV files (>250MB) into segments of 50,000 lines
 * 3. Save to local resources folder
 * 4. Import to database using existing COPY command
 * 5. Clean up resources and memory
 */
@Service
@Slf4j
public class SovrnFileProcessorService {

    private final PlS3ClientProvider plS3ClientProvider;
    private final S3ToPostgresLoaderService s3ToPostgresLoaderService;
    private final SovrnUnifiedFilterService unifiedFilterService;
    private final String plS3Folder;
    private final String resourcesPath;
    
    private static final long MAX_FILE_SIZE_BYTES = 250 * 1024 * 1024;
    private static final int LINES_PER_SEGMENT = 100000;
    private static final int BUFFER_SIZE = 100 * 1024 * 1024; // 100MB buffer

    @Autowired
    public SovrnFileProcessorService(@Qualifier("pl-client-provider") PlS3ClientProvider plS3ClientProvider,
                                   S3ToPostgresLoaderService s3ToPostgresLoaderService,
                                   SovrnUnifiedFilterService unifiedFilterService,
                                   @Value("${product.pl.s3.folder}") String plS3Folder) {
        this.plS3ClientProvider = plS3ClientProvider;
        this.s3ToPostgresLoaderService = s3ToPostgresLoaderService;
        this.unifiedFilterService = unifiedFilterService;
        this.plS3Folder = plS3Folder;
        this.resourcesPath = "src/main/resources/temp";
        
        // Create resources temp directory if it doesn't exist
        createResourcesDirectory();
    }

    /**
     * Main method to process all files from PL S3
     */
    public void processFilesFromPLS3() {
        long allRetailersStartTime = System.currentTimeMillis();
        log.info("Starting file processing from PL S3...");
        
        S3Client plS3 = null;
        
        try {
            plS3 = plS3ClientProvider.createPlS3Client();
            String plBucketName = plS3ClientProvider.getPlBucketName();
            
            log.info("Connected to PL S3 bucket: {}", plBucketName);
            logMemoryUsage("Before processing");
            
            // List all files in PL S3 folder
            List<S3Object> allFiles = listAllFilesFromPLS3(plS3, plBucketName);
            log.info("Found {} files in PL S3 to process", allFiles.size());

            // Filter files based on allowed-folder-ids and allowed-file-ids configuration
            List<S3Object> filteredFiles = filterFilesForProcessing(allFiles);
            log.info("After filtering: {} files to process, {} files to skip",
                    filteredFiles.size(), allFiles.size() - filteredFiles.size());

            int successCount = 0;
            int failureCount = 0;
            
            // Process each filtered file one at a time
            for (int i = 0; i < filteredFiles.size(); i++) {
                S3Object file = filteredFiles.get(i);
                String fileName = file.key().substring(file.key().lastIndexOf('/') + 1);
                
                try {
                    log.info("Processing file {}/{}: {} (Size: {} MB)",
                            i + 1, filteredFiles.size(), fileName, file.size() / (1024 * 1024));
                    
                    processSingleFile(plS3, plBucketName, file);
                    successCount++;
                    
                    log.info("Successfully processed file: {}", fileName);
                    
                } catch (Exception e) {
                    log.error("Failed to process file {}: {}", fileName, e.getMessage(), e);
                    failureCount++;
                } finally {
                    // Clean up resources after each file
                    cleanupResourcesDirectory();
                    System.gc();
                    logMemoryUsage("After processing file " + (i + 1));
                }
            }
            
            // Log total processing time for all retailers
            long allRetailersEndTime = System.currentTimeMillis();
            long allRetailersDuration = allRetailersEndTime - allRetailersStartTime;
            log.info("File processing completed. Success: {}, Failures: {} in {} ms ({} minutes)",
                    successCount, failureCount, allRetailersDuration, allRetailersDuration / 60000.0);
            
        } catch (Exception e) {
            log.error("Error during file processing: {}", e.getMessage(), e);
            throw new RuntimeException("File processing failed", e);
        } finally {
            // Cleanup resources (S3 connections managed by connection pool)
            cleanupResourcesDirectory();
            System.gc();
            log.info("Resource cleanup completed");
        }
    }

    /**
     * Process a single file: download, decompress, split if needed, and import to DB
     */
    private void processSingleFile(S3Client plS3, String bucketName, S3Object s3File) throws Exception {
        long singleFileStartTime = System.currentTimeMillis();
        String fileName = s3File.key().substring(s3File.key().lastIndexOf('/') + 1);
        String baseFileName = extractBaseFileName(fileName);

        // Extract retailer key from folder name (not filename)
        String retailerKey = extractRetailerKeyFromS3Path(s3File.key());
        log.info("Extracted retailer key: '{}' from S3 path: {}", retailerKey, s3File.key());

        log.info("Processing file: {} -> baseFileName: {} -> retailerKey: {} (Size: {} MB)",
                fileName, baseFileName, retailerKey, s3File.size() / (1024 * 1024));
        
        // Step 1: Download and decompress file
        log.info("Step 1: Downloading and decompressing {}", fileName);
        File decompressedFile = downloadAndDecompressFile(plS3, bucketName, s3File.key(), baseFileName);
        
        try {
            long fileSizeBytes = decompressedFile.length();
            long fileSizeMB = fileSizeBytes / (1024 * 1024);
            
            log.info("Decompressed file size: {} MB", fileSizeMB);
            
            // Step 2 & 3: Process file with streaming approach (create segment -> process -> delete -> repeat)
            if (fileSizeBytes > MAX_FILE_SIZE_BYTES) {
                log.info("Step 2: File is large ({}MB > 250MB), using streaming segment processing", fileSizeMB);
                processLargeFileWithStreaming(decompressedFile, baseFileName, retailerKey);
            } else {
                log.info("Step 2: File is small ({}MB <= 250MB), processing directly", fileSizeMB);
                log.info("Step 3: Importing file to database: {} (Size: {} MB)",
                        decompressedFile.getName(), fileSizeMB);
                importFileToDatabase(decompressedFile, retailerKey);
                log.info("Successfully imported: {}", decompressedFile.getName());
            }
            
        } finally {
            // Clean up decompressed file
            if (decompressedFile.exists()) {
                decompressedFile.delete();
                log.debug("Deleted decompressed file: {}", decompressedFile.getName());
            }

            // Log total processing time for this file
            long singleFileEndTime = System.currentTimeMillis();
            long singleFileDuration = singleFileEndTime - singleFileStartTime;
            log.info("Completed processing file: {} in {} ms ({} minutes)",
                    fileName, singleFileDuration, singleFileDuration / 60000.0);
        }
    }

    /**
     * Download file from PL S3 and decompress it
     */
    private File downloadAndDecompressFile(S3Client plS3, String bucketName, String s3Key, String baseFileName) throws IOException {
        File outputFile = new File(resourcesPath, baseFileName + ".csv");
        
        GetObjectRequest request = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(s3Key)
                .build();
        
        try (InputStream s3InputStream = plS3.getObject(request);
             GZIPInputStream gzipInputStream = new GZIPInputStream(s3InputStream);
             BufferedInputStream bufferedInputStream = new BufferedInputStream(gzipInputStream, BUFFER_SIZE);
             FileOutputStream fileOutputStream = new FileOutputStream(outputFile);
             BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream, BUFFER_SIZE)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytes = 0;
            
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                bufferedOutputStream.write(buffer, 0, bytesRead);
                totalBytes += bytesRead;
                
                // Log progress for large files
                if (totalBytes % (50 * 1024 * 1024) == 0) { // Every 50MB
                    log.debug("Downloaded and decompressed {} MB", totalBytes / (1024 * 1024));
                }
            }
            
            bufferedOutputStream.flush();
            log.info("Successfully downloaded and decompressed {} bytes to {}", totalBytes, outputFile.getName());
        }
        
        return outputFile;
    }

    /**
     * Process large CSV file with streaming approach: create segment -> process -> delete -> repeat
     * This approach minimizes memory and disk usage by processing one segment at a time
     */
    private void processLargeFileWithStreaming(File largeFile, String baseFileName, String retailerKey) throws IOException {
        long fileStartTime = System.currentTimeMillis();
        log.info("Step 2: Processing large file with streaming: {} (Size: {} MB)",
                largeFile.getName(), largeFile.length() / (1024 * 1024));

        try (BufferedReader reader = new BufferedReader(new FileReader(largeFile), BUFFER_SIZE)) {

            String headerLine = reader.readLine(); // Read header
            if (headerLine == null) {
                throw new IOException("File is empty or has no header: " + largeFile.getName());
            }

            int segmentNumber = 1;
            int lineCount = 0;
            int totalSegments = 0; // Will be calculated dynamically
            String line;
            BufferedWriter currentWriter = null;
            File currentSegmentFile = null;

            log.info("Step 3: Starting streaming processing (segment count will be determined dynamically)");

            try {
                while ((line = reader.readLine()) != null) {
                    // Create new segment file if needed
                    if (lineCount == 0) {
                        currentSegmentFile = new File(resourcesPath, baseFileName + "_segment_" + segmentNumber + ".csv");
                        currentWriter = new BufferedWriter(new FileWriter(currentSegmentFile), BUFFER_SIZE);

                        // Write header to each segment
                        currentWriter.write(headerLine);
                        currentWriter.newLine();

                        log.info("Creating segment {}: {}", segmentNumber, currentSegmentFile.getName());
                    }

                    // Write line to current segment
                    currentWriter.write(line);
                    currentWriter.newLine();
                    lineCount++;

                    // Process current segment if it reaches the limit
                    if (lineCount >= LINES_PER_SEGMENT) {
                        // Close the segment file
                        currentWriter.flush();
                        currentWriter.close();
                        currentWriter = null;

                        long segmentSizeMB = currentSegmentFile.length() / (1024 * 1024);
                        log.info("Completed segment {} with {} lines (Size: {} MB)",
                                segmentNumber, lineCount, segmentSizeMB);

                        // Process the segment immediately
                        processSegmentImmediately(currentSegmentFile, segmentNumber, retailerKey);

                        // Reset for next segment
                        segmentNumber++;
                        lineCount = 0;
                    }
                }

                // Process the last segment if it has data
                if (currentWriter != null && lineCount > 0) {
                    currentWriter.flush();
                    currentWriter.close();
                    currentWriter = null;

                    long segmentSizeMB = currentSegmentFile.length() / (1024 * 1024);
                    log.info("Completed final segment {} with {} lines (Size: {} MB)",
                            segmentNumber, lineCount, segmentSizeMB);

                    // Process the final segment
                    processSegmentImmediately(currentSegmentFile, segmentNumber, retailerKey);

                    // Log final segment count and total time
                    long fileEndTime = System.currentTimeMillis();
                    long fileDuration = fileEndTime - fileStartTime;
                    log.info("File processing completed: {} total segments processed in {} ms ({} minutes)",
                            segmentNumber, fileDuration, fileDuration / 60000.0);
                }

            } finally {
                // Ensure writer is closed in case of exception
                if (currentWriter != null) {
                    try {
                        currentWriter.close();
                    } catch (IOException e) {
                        log.warn("Error closing segment writer: {}", e.getMessage());
                    }

                    // Clean up incomplete segment file
                    if (currentSegmentFile != null && currentSegmentFile.exists()) {
                        currentSegmentFile.delete();
                        log.debug("Cleaned up incomplete segment file: {}", currentSegmentFile.getName());
                    }
                }
            }
        }

        log.info("Completed streaming processing of all segments");
    }

    /**
     * Process a single segment immediately: import to database and delete file
     */
    private void processSegmentImmediately(File segmentFile, int segmentNumber, String retailerKey) {
        long segmentStartTime = System.currentTimeMillis();
        try {
            log.info("Processing segment {}: {} (Size: {} MB)",
                    segmentNumber, segmentFile.getName(),
                    segmentFile.length() / (1024 * 1024));

            // Import segment to database
            importFileToDatabase(segmentFile, retailerKey);

            long segmentEndTime = System.currentTimeMillis();
            long segmentDuration = segmentEndTime - segmentStartTime;
            log.info("Successfully processed segment {}: {} in {} ms ({} seconds)",
                    segmentNumber, segmentFile.getName(), segmentDuration, segmentDuration / 1000.0);

        } catch (Exception e) {
            long segmentEndTime = System.currentTimeMillis();
            long segmentDuration = segmentEndTime - segmentStartTime;
            log.error("Failed to process segment {}: {} after {} ms ({} seconds) - {}",
                    segmentNumber, segmentFile.getName(), segmentDuration, segmentDuration / 1000.0, e.getMessage(), e);
            throw new RuntimeException("Failed to process segment " + segmentNumber, e);
        } finally {
            // Delete segment file immediately to free disk space
            if (segmentFile.exists()) {
                boolean deleted = segmentFile.delete();
                if (deleted) {
                    log.debug("Deleted processed segment: {}", segmentFile.getName());
                } else {
                    log.warn("Failed to delete segment file: {}", segmentFile.getName());
                }
            }
        }
    }



    /**
     * Import file to database using existing COPY command with proper transaction management
     */
    private void importFileToDatabase(File csvFile, String retailerKey) throws Exception {
        log.info("Starting database import for file: {} (Size: {} MB) with retailerKey: '{}'",
                csvFile.getName(), csvFile.length() / (1024 * 1024), retailerKey);

        try {
            // Use existing S3ToPostgresLoaderService to load the file
            // The service expects a fileName (retailerKey) and will handle the transaction
            long recordsLoaded = s3ToPostgresLoaderService.loadSovrnProductsFromLocalFile(csvFile.getAbsolutePath(), retailerKey);

            if (recordsLoaded >= 0) {
                log.info("Successfully imported {} records from file: {} with retailerKey: '{}'", recordsLoaded, csvFile.getName(), retailerKey);
            } else {
                log.warn("Database import returned negative count for file: {} with retailerKey: '{}' - this may indicate an error", csvFile.getName(), retailerKey);
            }

        } catch (Exception e) {
            // Enhanced error logging
            log.error("Database import failed for file {} with retailerKey '{}': {}", csvFile.getName(), retailerKey, e.getMessage());

            // The new implementation handles duplicates gracefully, so most errors should be real issues
            if (e.getMessage().contains("Failed to load data from local file")) {
                log.error("Database operation failed for file: {} with retailerKey: '{}' - check file format and database connection", csvFile.getName(), retailerKey);
            }

            throw new RuntimeException("Database import failed for file: " + csvFile.getName() + " with retailerKey: " + retailerKey, e);
        }
    }

    /**
     * List all files from PL S3 folder
     */
    private List<S3Object> listAllFilesFromPLS3(S3Client plS3, String bucketName) {
        List<S3Object> allFiles = new ArrayList<>();
        String continuationToken = null;

        do {
            ListObjectsV2Request.Builder requestBuilder = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(plS3Folder);

            if (continuationToken != null) {
                requestBuilder.continuationToken(continuationToken);
            }

            ListObjectsV2Response response = plS3.listObjectsV2(requestBuilder.build());

            // Filter only .gz files
            for (S3Object obj : response.contents()) {
                if (obj.key().endsWith(".gz") && obj.size() > 0) {
                    allFiles.add(obj);
                }
            }

            continuationToken = response.nextContinuationToken();

        } while (continuationToken != null);

        return allFiles;
    }

    private void createResourcesDirectory() {
        try {
            Path resourcesDir = Paths.get(resourcesPath);
            if (!Files.exists(resourcesDir)) {
                Files.createDirectories(resourcesDir);
                log.info("Created resources directory: {}", resourcesPath);
            }
        } catch (IOException e) {
            log.error("Failed to create resources directory: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create resources directory", e);
        }
    }

    private void cleanupResourcesDirectory() {
        try {
            Path resourcesDir = Paths.get(resourcesPath);
            if (Files.exists(resourcesDir)) {
                Files.list(resourcesDir)
                        .filter(Files::isRegularFile)
                        .forEach(file -> {
                            try {
                                Files.delete(file);
                                log.debug("Deleted temp file: {}", file.getFileName());
                            } catch (IOException e) {
                                log.warn("Failed to delete temp file {}: {}", file.getFileName(), e.getMessage());
                            }
                        });
                log.info("Cleaned up resources directory");
            }
        } catch (IOException e) {
            log.warn("Error cleaning up resources directory: {}", e.getMessage());
        }
    }




    private String extractBaseFileName(String fileName) {
        // Remove common extensions
        String baseName = fileName;
        if (baseName.endsWith(".csv.gz")) {
            baseName = baseName.substring(0, baseName.length() - 7);
        } else if (baseName.endsWith(".gz")) {
            baseName = baseName.substring(0, baseName.length() - 3);
        } else if (baseName.endsWith(".csv")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }

        log.debug("Extracted base file name: {} -> {}", fileName, baseName);
        return baseName;
    }

    private void logMemoryUsage(String context) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / (1024 * 1024); // MB
        long freeMemory = runtime.freeMemory() / (1024 * 1024); // MB
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory() / (1024 * 1024); // MB

        log.info("Memory Usage [{}]: Used={}MB, Free={}MB, Total={}MB, Max={}MB",
                context, usedMemory, freeMemory, totalMemory, maxMemory);
    }

    private String extractRetailerKeyFromS3Path(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            log.warn("S3 key is null or empty, using 'unknown' as retailer key");
            return "unknown";
        }

        try {
            log.info("Extracting retailer key from S3 path: {}", s3Key);

            String[] pathParts = s3Key.split("/");
            log.info("S3 path parts: {}", String.join(" | ", pathParts));
            if (pathParts.length >= 2) {
                String retailerFolder = pathParts[pathParts.length - 2];
                log.info("Extracted retailer folder '{}' from S3 key: {}", retailerFolder, s3Key);
                log.info("This retailer folder will be transformed by PsqlCopyRunnerWithTransform.transformRetailerKey() to remove numerics/special chars and convert to lowercase");
                return retailerFolder;
            }

            log.warn("Unable to extract retailer folder from S3 key: {} (path parts length: {}) - using 'unknown'", s3Key, pathParts.length);
            return "unknown";

        } catch (Exception e) {
            log.warn("Error extracting retailer folder from S3 key '{}': {} - using 'unknown'", s3Key, e.getMessage());
            return "unknown";
        }
    }

    /**
     * Filter files for processing based on allowed-folder-ids and allowed-file-ids configuration
     * Only files that match the configured filtering criteria will be processed
     */
    private List<S3Object> filterFilesForProcessing(List<S3Object> allFiles) {
        List<S3Object> filteredFiles = new ArrayList<>();
        List<String> skippedFiles = new ArrayList<>();

        log.info("=== APPLYING FOLDER/FILE FILTERING TO PL S3 FILES ===");
        log.info("Configuration-based filtering: Only process retailers configured in allowed-folder-ids and allowed-file-ids");

        for (S3Object file : allFiles) {
            String fileName = file.key().substring(file.key().lastIndexOf('/') + 1);
            long fileSizeKB = file.size() / 1024;

            // Use the unified filtering service to check if file should be processed
            SovrnUnifiedFilterService.FilteringInfo filteringInfo = unifiedFilterService.getFilteringInfo(file.key());

            if (filteringInfo.shouldProcess) {
                filteredFiles.add(file);
                log.info("WILL PROCESS: {} (Size: {} KB) - Retailer: {}, Strategy: {}, Reason: {}",
                        fileName, fileSizeKB, filteringInfo.retailerType.toUpperCase(),
                        filteringInfo.strategy, filteringInfo.reason);
            } else {
                skippedFiles.add(fileName);
                log.info("WILL SKIP: {} (Size: {} KB) - Retailer: {}, Strategy: {}, Reason: {}",
                        fileName, fileSizeKB, filteringInfo.retailerType.toUpperCase(),
                        filteringInfo.strategy, filteringInfo.reason);
            }
        }

        log.info("=== FILTERING SUMMARY ===");
        log.info("Total files found: {}", allFiles.size());
        log.info("Files to process: {}", filteredFiles.size());
        log.info("Files to skip: {}", skippedFiles.size());

        if (!skippedFiles.isEmpty()) {
            log.info("Skipped files: {}", String.join(", ", skippedFiles));
        }

        return filteredFiles;
    }
}
