package com.progleasing.marketplace.etl.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.io.FileReader;
import java.io.Reader;
import java.math.BigDecimal;
import java.sql.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Objects;

// PostgreSQL COPY imports
import org.postgresql.copy.CopyManager;
import org.postgresql.core.BaseConnection;

@Repository
@Transactional
@Slf4j
public class CustomSovrnProductsRepositoryImpl implements CustomSovrnProductsRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Value("${spring.datasource.username}")
    private String dbUser;

    @Value("${spring.datasource.password}")
    private String dbPassword;

    @Value("${spring.datasource.url}")
    private String dbUrl;

    @Value("${sovrn.database.bulk-copy.enabled:true}")
    private boolean bulkCopyEnabled;

    @Override
    public long copyFromCsv(String csvFilePath) {
        String tableName = "sovrn_products";

        log.info("Starting database load for file: {}", csvFilePath);
        long startTime = System.currentTimeMillis();

        try (Connection conn = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {

            if (bulkCopyEnabled) {
                log.info("Attempting BULK COPY operation using existing runJdbcCopy method...");

                String url = dbUrl; // ************************************
                String[] urlParts = url.replace("jdbc:postgresql://", "").split("/");
                String[] hostPort = urlParts[0].split(":");
                String host = hostPort[0];
                String port = hostPort.length > 1 ? hostPort[1] : "5432";
                String database = urlParts[1];
                long bulkResult = com.progleasing.marketplace.etl.service.PsqlCopyRunnerWithTransform.runJdbcCopy(
                    host, port, database, dbUser, dbPassword, csvFilePath, tableName);

                if (bulkResult >= 0) {
                    long endTime = System.currentTimeMillis();
                    long durationMs = endTime - startTime;
                    log.info("BULK COPY SUCCESS: {} rows loaded in {} ms ({} rows/sec)",
                            bulkResult, durationMs,
                            durationMs > 0 ? (bulkResult * 1000 / durationMs) : "N/A");
                    return bulkResult;
                }

                log.warn("Bulk COPY failed, falling back to STAGING TABLE approach with DISTINCT ON + DO UPDATE...");
            } else {
                log.info("Bulk COPY disabled by configuration, using STAGING TABLE approach with DISTINCT ON + DO UPDATE...");
            }

            long stagingResult = performStagingTableProcessing(conn, csvFilePath, tableName);

            long endTime = System.currentTimeMillis();
            long durationMs = endTime - startTime;
            log.info("STAGING TABLE FALLBACK: {} rows loaded in {} ms ({} rows/sec)",
                    stagingResult, durationMs,
                    durationMs > 0 ? (stagingResult * 1000 / durationMs) : "N/A");
            return stagingResult;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long durationMs = endTime - startTime;
            log.error("DATABASE OPERATION FAILED for file {} after {} ms: {}", csvFilePath, durationMs, e.getMessage(), e);
            throw new RuntimeException("Database operation failed for file: " + csvFilePath, e);
        }
    }

    /**
     * High-performance staging table processing with DISTINCT ON (sku) + DO UPDATE approach
     * Used as fallback when primary COPY command fails
     * Creates temporary table without constraints, loads data, then transfers with SKU-based UPSERT
     * Note: Only SKU is the primary key, retailer_key is not part of any constraint
     */
    private long performStagingTableProcessing(Connection conn, String csvFilePath, String tableName) throws Exception {
        log.info("Starting STAGING TABLE processing with DISTINCT ON (sku) + DO UPDATE approach");
        long startTime = System.currentTimeMillis();

        String tempTableName = "temp_sovrn_staging_" + System.currentTimeMillis();
        long rowsLoaded = 0;
        long rowsProcessed = 0;

        try {
            // Step 1: Create temporary staging table WITHOUT any constraints or indexes
            log.info("Creating temporary staging table: {}", tempTableName);
            String createTempTableSql =
                "CREATE TEMP TABLE " + tempTableName + " (" +
                "ean VARCHAR(255), " +
                "image_url TEXT, " +
                "in_stock VARCHAR(10), " +
                "isbn VARCHAR(255), " +
                "language VARCHAR(50), " +
                "brand VARCHAR(255), " +
                "merchant_name VARCHAR(255), " +
                "mpn VARCHAR(255), " +
                "product_name TEXT, " +
                "domain VARCHAR(255), " +
                "retail_price DECIMAL(10,2), " +
                "sale_price DECIMAL(10,2), " +
                "sku VARCHAR(100), " +
                "thumbnail_url TEXT, " +
                "upc VARCHAR(255), " +
                "affiliated_url TEXT, " +
                "merchant_raw_category VARCHAR(255), " +
                "retailer_key VARCHAR(100), " +
                "creation_date TIMESTAMP" +
                ") ON COMMIT PRESERVE ROWS"; // Keep data until explicitly dropped

            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createTempTableSql);
                log.info("Temporary staging table created successfully");
            }

            // Step 2: Bulk load data into staging table using COPY (no constraints = no errors)
            log.info("Loading data into staging table using COPY command...");
            CopyManager copyManager = new CopyManager(conn.unwrap(BaseConnection.class));

            try (Reader reader = new FileReader(csvFilePath)) {
                String copyCommand = "COPY " + tempTableName + " " +
                    "(ean, image_url, in_stock, isbn, language, brand, merchant_name, " +
                    "mpn, product_name, domain, retail_price, sale_price, sku, thumbnail_url, " +
                    "upc, affiliated_url, merchant_raw_category, retailer_key, creation_date) " +
                    "FROM STDIN WITH (FORMAT csv, HEADER true, DELIMITER ',', QUOTE '\"', ENCODING 'UTF8')";

                rowsLoaded = copyManager.copyIn(copyCommand, reader);
                log.info("Successfully loaded {} rows into staging table", rowsLoaded);
            }

            // Step 3: Ensure unique constraint exists for ON CONFLICT
            ensureUniqueConstraintExists(conn, tableName);

            // Step 4: Transfer data with DISTINCT ON + DO UPDATE (the enhanced approach)
            log.info("Transferring data with DISTINCT ON (sku) + DO UPDATE approach...");
            String intelligentUpsertSql =
                "INSERT INTO " + tableName + " " +
                "(ean, image_url, in_stock, isbn, language, brand, merchant_name, mpn, " +
                "product_name, domain, retail_price, sale_price, sku, thumbnail_url, upc, " +
                "affiliated_url, merchant_raw_category, retailer_key, creation_date) " +
                "SELECT DISTINCT ON (sku, retailer_key) " +  // Handle within-batch duplicates by SKU (matching PK)
                "ean, image_url, in_stock, isbn, language, brand, merchant_name, mpn, " +
                "product_name, domain, retail_price, sale_price, sku, thumbnail_url, upc, " +
                "affiliated_url, merchant_raw_category, retailer_key, creation_date " +
                "FROM " + tempTableName + " " +
                "ON CONFLICT (sku, retailer_key) DO UPDATE SET " +  // Update existing SKU with fresh data from CSV
                "ean = EXCLUDED.ean, " +
                "image_url = EXCLUDED.image_url, " +
                "in_stock = EXCLUDED.in_stock, " +
                "isbn = EXCLUDED.isbn, " +
                "language = EXCLUDED.language, " +
                "brand = EXCLUDED.brand, " +
                "merchant_name = EXCLUDED.merchant_name, " +
                "mpn = EXCLUDED.mpn, " +
                "product_name = EXCLUDED.product_name, " +
                "domain = EXCLUDED.domain, " +
                "retail_price = EXCLUDED.retail_price, " +
                "sale_price = EXCLUDED.sale_price, " +
                "thumbnail_url = EXCLUDED.thumbnail_url, " +
                "upc = EXCLUDED.upc, " +
                "affiliated_url = EXCLUDED.affiliated_url, " +
                "merchant_raw_category = EXCLUDED.merchant_raw_category, " +
                "creation_date = EXCLUDED.creation_date";

            try (Statement stmt = conn.createStatement()) {
                rowsProcessed = stmt.executeUpdate(intelligentUpsertSql);
                log.info("Successfully processed {} rows (inserted new + updated existing records)", rowsProcessed);
            }

            // Step 5: Clean up - Drop the temporary table
            log.info("Cleaning up temporary staging table...");
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("DROP TABLE IF EXISTS " + tempTableName);
                log.info("Temporary staging table dropped successfully");
            }

        } catch (Exception e) {
            log.error("Staging table processing failed: {}", e.getMessage(), e);

            // Cleanup on error
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("DROP TABLE IF EXISTS " + tempTableName);
                log.info("Cleaned up temporary table after error");
            } catch (Exception cleanupEx) {
                log.warn("Failed to cleanup temporary table: {}", cleanupEx.getMessage());
            }

            throw e; // Re-throw the original exception
        }

        long endTime = System.currentTimeMillis();
        long totalDurationMs = endTime - startTime;
        long recordsPerSecond = totalDurationMs > 0 ? (rowsLoaded * 1000 / totalDurationMs) : 0;

        log.info("Staging table processing completed: {} rows loaded into staging, {} rows processed in main table in {} ms ({} records/sec)",
                rowsLoaded, rowsProcessed, totalDurationMs, recordsPerSecond);

        return rowsProcessed;
    }

    /**
     * Validate that primary key exists on sku for ON CONFLICT to work
     * Just logs the constraint information for debugging
     */
    private void ensureUniqueConstraintExists(Connection conn, String tableName) {
        try (Statement stmt = conn.createStatement()) {
            // Check what constraints actually exist
            String checkConstraintSql =
                "SELECT constraint_name, constraint_type " +
                "FROM information_schema.table_constraints " +
                "WHERE table_name = ?";

            try (PreparedStatement checkStmt = conn.prepareStatement(checkConstraintSql)) {
                checkStmt.setString(1, tableName);
                try (ResultSet rs = checkStmt.executeQuery()) {
                    log.info("Existing constraints on table {}:", tableName);
                    while (rs.next()) {
                        log.info("  - {} ({})", rs.getString("constraint_name"), rs.getString("constraint_type"));
                    }
                }
            }

            log.info("Using existing primary key constraint for ON CONFLICT");

        } catch (Exception e) {
            log.warn("Failed to check table constraints: {}", e.getMessage());
        }
    }

    /**
     * UNUSED - Legacy row-by-row processing with field-level duplicate checking
     * Kept for potential future use but replaced by performStagingTableProcessing
     * Used only when COPY command fails - NO ON CONFLICT used
     * Note: Only skips records if ALL fields are identical, otherwise inserts as new record
     */
    @SuppressWarnings("unused")
    private long performRowByRowProcessing(Connection conn, String csvFilePath, String tableName) throws Exception {
        log.info("Starting row-by-row processing with field-level duplicate checking (only skip if ALL fields identical)");
        long startTime = System.currentTimeMillis();

        long successfulRows = 0;
        long skippedRows = 0;

        try (PreparedStatement insertStmt = conn.prepareStatement(
                "INSERT INTO " + tableName + " (ean, image_url, in_stock, isbn, language, brand, merchant_name, " +
                "mpn, product_name, domain, retail_price, sale_price, sku, thumbnail_url, " +
                "upc, affiliated_url, merchant_raw_category, retailer_key, creation_date) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
             PreparedStatement checkStmt = conn.prepareStatement(
                "SELECT ean, image_url, in_stock, isbn, language, brand, merchant_name, mpn, product_name, domain, " +
                "retail_price, sale_price, thumbnail_url, upc, affiliated_url, merchant_raw_category, creation_date " +
                "FROM " + tableName + " WHERE sku = ? AND retailer_key = ?");
             Reader reader = new FileReader(csvFilePath);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            for (CSVRecord record : csvParser) {
                try {
                    String sku = record.get("sku");
                    String retailerKey = record.get("retailer_key");

                    // Check if identical record already exists (field-level duplicate detection)
                    // Only skip if ALL fields match, otherwise insert as new record
                    checkStmt.setString(1, sku);
                    checkStmt.setString(2, retailerKey);

                    boolean isDuplicate = false;
                    try (ResultSet rs = checkStmt.executeQuery()) {
                        while (rs.next()) {
                            // Compare all fields to determine if this is truly a duplicate
                            if (areAllFieldsIdentical(record, rs)) {
                                isDuplicate = true;
                                skippedRows++;
                                log.info("SKIPPED COMPLETE DUPLICATE: SKU='{}', retailer_key='{}', product_name='{}', brand='{}' - ALL fields identical",
                                        sku, retailerKey, record.get("product_name"), record.get("brand"));
                                break;
                            }
                        }
                    }
                    if (isDuplicate) {
                        continue; // Skip this record
                    }

                    // Insert new record
                    setInsertParameters(insertStmt, record);
                    insertStmt.executeUpdate();
                    successfulRows++;

                    // Log progress every 1000 records with timing
                    if ((successfulRows + skippedRows) % 1000 == 0) {
                        long currentTime = System.currentTimeMillis();
                        long elapsedMs = currentTime - startTime;
                        long recordsProcessed = successfulRows + skippedRows;
                        long recordsPerSecond = elapsedMs > 0 ? (recordsProcessed * 1000 / elapsedMs) : 0;

                        log.info("Progress: {} records processed in {} ms ({} records/sec) - {} inserted, {} duplicates skipped",
                                recordsProcessed, elapsedMs, recordsPerSecond, successfulRows, skippedRows);
                    }

                } catch (Exception recordException) {
                    skippedRows++;
                    log.warn("SKIPPED INVALID RECORD: Line {}, SKU='{}', retailer_key='{}' - Error: {}",
                            record.getRecordNumber(),
                            record.get("sku"),
                            record.get("retailer_key"),
                            recordException.getMessage());
                }
            }
        }

        long endTime = System.currentTimeMillis();
        long totalDurationMs = endTime - startTime;
        long totalRecords = successfulRows + skippedRows;
        long recordsPerSecond = totalDurationMs > 0 ? (totalRecords * 1000 / totalDurationMs) : 0;

        log.info("Row-by-row processing completed: {} inserted, {} skipped, {} total processed in {} ms ({} records/sec)",
                successfulRows, skippedRows, totalRecords, totalDurationMs, recordsPerSecond);

        return successfulRows;
    }

    /**
     * Parse timestamp string with multiple format support
     * Handles both ISO format (with T) and PostgreSQL format (with space)
     */
    private Timestamp parseTimestamp(String timestampStr) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return Timestamp.valueOf(LocalDateTime.now());
        }

        // List of supported timestamp formats
        DateTimeFormatter[] formatters = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),           // PostgreSQL standard
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),       // With milliseconds
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS"),    // With microseconds
            DateTimeFormatter.ISO_LOCAL_DATE_TIME,                        // ISO format with T
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),         // ISO without milliseconds
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),     // ISO with milliseconds
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS")   // ISO with microseconds
        };

        String cleanTimestamp = timestampStr.trim();

        // Try each format
        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(cleanTimestamp, formatter);
                return Timestamp.valueOf(localDateTime);
            } catch (DateTimeParseException e) {
                // Continue to next format
            }
        }

        // If all formats fail, try direct Timestamp.valueOf() as last resort
        try {
            return Timestamp.valueOf(cleanTimestamp);
        } catch (IllegalArgumentException e) {
            log.warn("Failed to parse timestamp '{}', using current time instead. Error: {}",
                    timestampStr, e.getMessage());
            return Timestamp.valueOf(LocalDateTime.now());
        }
    }



    /**
     * Helper method to set INSERT statement parameters
     */
    private void setInsertParameters(PreparedStatement insertStmt, CSVRecord record) throws SQLException {
        insertStmt.setString(1, record.get("ean"));
        insertStmt.setString(2, record.get("image_url"));
        insertStmt.setString(3, record.get("in_stock"));
        insertStmt.setString(4, record.get("isbn"));
        insertStmt.setString(5, record.get("language"));
        insertStmt.setString(6, record.get("brand"));
        insertStmt.setString(7, record.get("merchant_name"));
        insertStmt.setString(8, record.get("mpn"));
        insertStmt.setString(9, record.get("product_name"));
        insertStmt.setString(10, record.get("domain"));

        // Handle numeric fields safely
        String retailPrice = record.get("retail_price");
        if (retailPrice != null && !retailPrice.trim().isEmpty()) {
            insertStmt.setBigDecimal(11, new BigDecimal(retailPrice));
        } else {
            insertStmt.setBigDecimal(11, null);
        }

        String salePrice = record.get("sale_price");
        if (salePrice != null && !salePrice.trim().isEmpty()) {
            insertStmt.setBigDecimal(12, new BigDecimal(salePrice));
        } else {
            insertStmt.setBigDecimal(12, null);
        }

        insertStmt.setString(13, record.get("sku"));
        insertStmt.setString(14, record.get("thumbnail_url"));
        insertStmt.setString(15, record.get("upc"));
        insertStmt.setString(16, record.get("affiliated_url"));
        insertStmt.setString(17, record.get("merchant_raw_category"));

        // The retailer_key is already transformed in PsqlCopyRunnerWithTransform
        insertStmt.setString(18, record.get("retailer_key"));

        // Handle timestamp parsing
        String creationDateStr = record.get("creation_date");
        Timestamp creationDate = parseTimestamp(creationDateStr);
        insertStmt.setTimestamp(19, creationDate);
    }

    /**
     * Compare all fields between CSV record and database record to determine if they are identical
     * Returns true only if ALL fields match exactly
     */
    private boolean areAllFieldsIdentical(CSVRecord csvRecord, ResultSet dbRecord) throws Exception {
        try {
            // Compare each field - return false if any field differs
            if (!isFieldEqual(csvRecord.get("ean"), dbRecord.getString("ean"))) return false;
            if (!isFieldEqual(csvRecord.get("image_url"), dbRecord.getString("image_url"))) return false;
            if (!isFieldEqual(csvRecord.get("in_stock"), dbRecord.getString("in_stock"))) return false;
            if (!isFieldEqual(csvRecord.get("isbn"), dbRecord.getString("isbn"))) return false;
            if (!isFieldEqual(csvRecord.get("language"), dbRecord.getString("language"))) return false;
            if (!isFieldEqual(csvRecord.get("brand"), dbRecord.getString("brand"))) return false;
            if (!isFieldEqual(csvRecord.get("merchant_name"), dbRecord.getString("merchant_name"))) return false;
            if (!isFieldEqual(csvRecord.get("mpn"), dbRecord.getString("mpn"))) return false;
            if (!isFieldEqual(csvRecord.get("product_name"), dbRecord.getString("product_name"))) return false;
            if (!isFieldEqual(csvRecord.get("domain"), dbRecord.getString("domain"))) return false;

            // Compare numeric fields (prices)
            if (!isPriceEqual(csvRecord.get("retail_price"), dbRecord.getBigDecimal("retail_price"))) return false;
            if (!isPriceEqual(csvRecord.get("sale_price"), dbRecord.getBigDecimal("sale_price"))) return false;

            // Compare remaining string fields
            if (!isFieldEqual(csvRecord.get("thumbnail_url"), dbRecord.getString("thumbnail_url"))) return false;
            if (!isFieldEqual(csvRecord.get("upc"), dbRecord.getString("upc"))) return false;
            if (!isFieldEqual(csvRecord.get("affiliated_url"), dbRecord.getString("affiliated_url"))) return false;
            if (!isFieldEqual(csvRecord.get("merchant_raw_category"), dbRecord.getString("merchant_raw_category"))) return false;

            // Note: We don't compare creation_date as it's auto-generated and will always differ

            return true; // All fields are identical

        } catch (Exception e) {
            log.warn("Error comparing fields for SKU '{}': {} - treating as different record",
                    csvRecord.get("sku"), e.getMessage());
            return false; // If comparison fails, treat as different record (safer to insert)
        }
    }

    /**
     * Compare string fields, treating null and empty as equivalent
     */
    private boolean isFieldEqual(String csvValue, String dbValue) {
        String normalizedCsv = normalizeStringValue(csvValue);
        String normalizedDb = normalizeStringValue(dbValue);
        return Objects.equals(normalizedCsv, normalizedDb);
    }

    /**
     * Compare price fields, handling null and string-to-BigDecimal conversion
     */
    private boolean isPriceEqual(String csvValue, BigDecimal dbValue) {
        try {
            String normalizedCsv = normalizeStringValue(csvValue);
            if (normalizedCsv == null && dbValue == null) return true;
            if (normalizedCsv == null || dbValue == null) return false;

            BigDecimal csvPrice = new BigDecimal(normalizedCsv);
            return csvPrice.compareTo(dbValue) == 0;

        } catch (NumberFormatException e) {
            // If CSV value is not a valid number, treat as different
            return false;
        }
    }

    /**
     * Normalize string values: trim whitespace and convert empty strings to null
     */
    private String normalizeStringValue(String value) {
        if (value == null) return null;
        String trimmed = value.trim();
        return trimmed.isEmpty() ? null : trimmed;
    }
}