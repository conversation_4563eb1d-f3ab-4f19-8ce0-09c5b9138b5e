package com.progleasing.marketplace.etl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "sovrn.etl")
@Setter
@Getter
@Configuration
public class SovrnRequestConfig {
    private int maxProductsToImport;
    private int gracePeriodDays;
    private boolean deleteProducts;
}
