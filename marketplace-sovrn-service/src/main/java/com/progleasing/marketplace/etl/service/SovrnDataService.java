package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.SovrnRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.config.RetailerKeyMappingProvider;
import com.progleasing.marketplace.etl.repositories.*;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class SovrnDataService {


    private SovrnRequestConfig sovrnRequestConfig;
    private ProductDataRepository productDataRepository;
    private PriceDataRepository priceDataRepository;
    private ImageDataRepository imageDataRepository;
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private ProductDataDisabledRepository productDataDisabledRepository;
    private final RetailerKeyMappingProvider retailerMappingProvider;

    @Transactional
    public void populateProductData(List<SovrnProducts> sovrnProductsList, ETLMessageDTO messageDTO, List<String> productIds) {
        try {
            List<ProductData> productDataList = new ArrayList<>();
            List<PriceData> priceDataList = new ArrayList<>();
            List<ImageData> imageDataList = new ArrayList<>();
            List<String> disabledProductIds = new ArrayList<>();
            List<ProductDataDisabled> productDataDisabledList = productDataDisabledRepository.findProductsByFeedSource
                    (messageDTO.getSearchTerm(),messageDTO.getL1CategoryName(), messageDTO.getL2CategoryName(),ProductData.FeedSource.sovrn);
            if(!productDataDisabledList.isEmpty()){
                disabledProductIds = productDataDisabledList.stream().map(ProductDataDisabled :: getId).collect(Collectors.toList());
            }
            for (SovrnProducts sovrnProduct : sovrnProductsList) {
                String productId = sovrnProduct.getSku();
                if (!disabledProductIds.contains(productId)) {
                    productDataList.add(
                            extractProductData(sovrnProduct,
                                    messageDTO)
                    );
                    Optional.of(extractPriceData(sovrnProduct)).ifPresent(priceDataList::add);
                    Optional.of(extractImageData(sovrnProduct)).ifPresent(imageDataList::addAll);
                }
            }
            if (!CollectionUtils.isEmpty(productDataList)) {
                productDataRepository.saveAll(productDataList);
            }
            if (!CollectionUtils.isEmpty(priceDataList)) {
                priceDataRepository.saveAll(priceDataList);
            }
            if (!CollectionUtils.isEmpty(imageDataList)) {
                imageDataRepository.saveAll(imageDataList);
            }
            productIds.removeAll(productDataList.stream().map(ProductData::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(productIds) && sovrnRequestConfig.isDeleteProducts()) {
                productDataRepository.markForDeletion(productIds, Timestamp.valueOf(LocalDateTime.now().plusDays(sovrnRequestConfig.getGracePeriodDays())));
                log.info("Product marked for deletion, productIds :{}", productIds);
            }else{
                log.info("No products there marked for deletion");
            }
            log.info("Adding products {} for category {} search term {}",productDataList.size(),messageDTO.getCategoryKey(),messageDTO.getSearchTerm());
        } catch (Exception e) {
            log.error("Error saving data to DB for category {} searchTerms {}",messageDTO.getCategoryKey(),messageDTO.getSearchTerm(),e);
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public void updateStatusQueue(ETLMessageDTO messageDTO, String messageId, EtlQueueStatusData.Status status) {
        try{
            String messageData = String.format(
                    "%s|%s|%s|%s|%s|%s|%s|%s|%s",
                    messageId,
                    messageDTO.getSearchTerm(),
                    messageDTO.getCategoryKey(),
                    messageDTO.getRetailerName(),
                    messageDTO.getRetailerKey(),
                    messageDTO.getMinPrice(),
                    messageDTO.getMaxPrice(),
                    messageDTO.getL1CategoryName(),
                    messageDTO.getL2CategoryName()
            );
            EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_SEARCH;
            Optional<EtlQueueStatusData> existing =
                    etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(
                            messageDTO.getReferenceId(),
                            ProductData.FeedSource.sovrn.name(),
                            etlStep,
                            messageId
                    );
            EtlQueueStatusData etlQueueStatusData;
            if (existing.isPresent()) {
                etlQueueStatusData = existing.get();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
                log.info("Sovrn Search Catalog sync {} for message ID {}",status.toString(), messageId);
            } else {
                etlQueueStatusData = new EtlQueueStatusData();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setEtlStep(etlStep);
                etlQueueStatusData.setEtlID(messageDTO.getReferenceId());
                etlQueueStatusData.setMessageId(messageId);
                etlQueueStatusData.setSqsMessageData(messageData);
                etlQueueStatusData.setRetailerSearch( ProductData.FeedSource.sovrn.name());
                if (status == EtlQueueStatusData.Status.STARTED) {
                    etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
                }
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
            }
            etlQueueStatusDataRepository.save(etlQueueStatusData);
            etlQueueStatusDataRepository.flush();
            log.info("Queue status saved successfully for message ID {} import id {} to {}", messageId,messageDTO.getReferenceId(),status);
        }catch (Exception e) {
            log.error("Error updating queue status for {} referenceId {}",messageId,messageDTO.getReferenceId());
            throw e;
        }
    }

    public boolean isMessageProcessed(String etlId, String messageId){
        Optional<EtlQueueStatusData> sovrnData  = etlQueueStatusDataRepository.findAllByEtlIDAndMessageId(etlId,messageId);
        return sovrnData.map(etlQueueStatusData -> etlQueueStatusData.getStatus().equals(EtlQueueStatusData.Status.COMPLETED)).orElse(false);
    }

    private ProductData extractProductData(SovrnProducts item , ETLMessageDTO messageDTO) {
        ProductData newProduct = buildProductDataFromItem(item, messageDTO);
        Optional<ProductData> existingOpt = productDataRepository.findById(newProduct.getId());
        if (existingOpt.isPresent()) {
            ProductData existing = existingOpt.get();
            if (hasProductChanged(existing, newProduct)) {
                updateProduct(existing, newProduct);
                return existing;
            }
        }
        return newProduct;
    }

    private ProductData buildProductDataFromItem(SovrnProducts item, ETLMessageDTO messageDTO) {
        ProductData productData = new ProductData();
        productData.setId(item.getSku());
        productData.setName(item.getProductName());
        productData.setDescription(item.getProductName());
        productData.setLongDescription(item.getProductName());
        productData.setRetailerKey(item.getRetailerKey());
        String retailerName = retailerMappingProvider
                .getRetailerKeyMapping()
                .getOrDefault(item.getRetailerKey(), item.getMerchantName());
        productData.setRetailerName(retailerName);
        productData.setBrand(item.getBrand());
        productData.setProductTrackingUrl(item.getAffiliatedUrl());
        productData.setAffiliateAddToCartUrl(null);
        productData.setLastModified(Timestamp.from(Instant.now()));
        productData.setCategoryKey(messageDTO.getCategoryKey());
        productData.setSearchTerm(messageDTO.getSearchTerm());
        productData.setRatingAverage(BigDecimal.ZERO);
        productData.setRatingCount(0);
        productData.setL1CategoryName(messageDTO.getL1CategoryName());
        productData.setL2CategoryName(messageDTO.getL2CategoryName());
        productData.setFeedSource(ProductData.FeedSource.sovrn);
        return productData;
    }

    private boolean hasProductChanged(ProductData existing, ProductData incoming) {
        return !Objects.equals(trimToNull(existing.getName()), trimToNull(incoming.getName())) ||
                !Objects.equals(trimToNull(existing.getDescription()), trimToNull(incoming.getDescription())) ||
                !Objects.equals(trimToNull(existing.getLongDescription()), trimToNull(incoming.getLongDescription())) ||
                !Objects.equals(trimToNull(existing.getRetailerName()), trimToNull(incoming.getRetailerName())) ||
                !Objects.equals(trimToNull(existing.getRetailerKey()), trimToNull(incoming.getRetailerKey())) ||
                !Objects.equals(trimToNull(existing.getBrand()), trimToNull(incoming.getBrand())) ||
                existing.getRatingAverage() == null ? incoming.getRatingAverage() != null : existing.getRatingAverage().compareTo(incoming.getRatingAverage()) != 0 ||
                existing.getRatingCount() != incoming.getRatingCount() ||
                !Objects.equals(trimToNull(existing.getProductTrackingUrl()), trimToNull(incoming.getProductTrackingUrl())) ||
                !Objects.equals(trimToNull(existing.getAffiliateAddToCartUrl()), trimToNull(incoming.getAffiliateAddToCartUrl()));
    }


    private String trimToNull(String s) {
        return (s == null || s.trim().isEmpty()) ? null : s.trim();
    }


    private void updateProduct(ProductData existing, ProductData updated) {
        existing.setName(updated.getName());
        existing.setDescription(updated.getDescription());
        existing.setLongDescription(updated.getLongDescription());
        existing.setRetailerName(updated.getRetailerName());
        existing.setRetailerKey(updated.getRetailerKey());
        existing.setBrand(updated.getBrand());
        existing.setRatingAverage(updated.getRatingAverage());
        existing.setRatingCount(updated.getRatingCount());
        existing.setProductTrackingUrl(updated.getProductTrackingUrl());
        existing.setAffiliateAddToCartUrl(updated.getAffiliateAddToCartUrl());
        existing.setCategoryKey(String.join(",", new HashSet<>(List.of(existing.getCategoryKey().split(",")))) + "," + updated.getCategoryKey());
        existing.setSearchTerm(String.join(",", new HashSet<>(List.of(existing.getSearchTerm().split(",")))) + "," + updated.getSearchTerm());
        existing.setLastModified(Timestamp.from(Instant.now()));
    }


    private PriceData extractPriceData(SovrnProducts item) {
        PriceData newPriceData = buildPriceDataFromItem(item);
        Optional<PriceData> existingOpt = priceDataRepository.findById(newPriceData.getId());
        if (existingOpt.isPresent()) {
            PriceData existing = existingOpt.get();
            if (hasPriceChanged(existing, newPriceData)) {
                existing.setPrice(newPriceData.getPrice());
                existing.setSalePrice(newPriceData.getSalePrice());
                existing.setLastModified(Timestamp.from(Instant.now()));
                return existing;
            }
        }
        return newPriceData;
    }

    private PriceData buildPriceDataFromItem(SovrnProducts item) {
        PriceData priceData = new PriceData();
        priceData.setId(item.getSku());
        BigDecimal currentPrice = item.getSalePrice() != null
                ? item.getSalePrice()
                : BigDecimal.ZERO;
        BigDecimal originalPrice = item.getRetailPrice() !=null
                ? item.getRetailPrice()
                : currentPrice;
        priceData.setPrice(originalPrice);
        if (originalPrice.compareTo(currentPrice) != 0 && currentPrice.compareTo(BigDecimal.ZERO) > 0) {
            priceData.setSalePrice(currentPrice);
        }
        priceData.setLastModified(Timestamp.from(Instant.now()));
        return priceData;
    }


    private boolean hasPriceChanged(PriceData existing, PriceData incoming) {
        return existing.getPrice() == null || incoming.getPrice() == null ||
                existing.getPrice().compareTo(incoming.getPrice()) != 0;
    }

    private List<ImageData> extractImageData(SovrnProducts item) {
        List<ImageData> imageDataList = new ArrayList<>();

        String imageUrl = item.getImageUrl();
        String thumbnailUrl = item.getThumbnailUrl();
        if (thumbnailUrl != null && !thumbnailUrl.isBlank()) {
            ImageData thumbnailData = new ImageData();
            thumbnailData.setId(item.getSku());
            thumbnailData.setImageUrl(thumbnailUrl);
            thumbnailData.setLastModified(Timestamp.from(Instant.now()));
            imageDataList.add(thumbnailData);
        }
        if (imageUrl != null && !imageUrl.isBlank() && !thumbnailUrl.equals(imageUrl)) {
            ImageData imageData = new ImageData();
            imageData.setId(item.getSku());
            imageData.setImageUrl(imageUrl);
            imageData.setLastModified(Timestamp.from(Instant.now()));
            imageDataList.add(imageData);
        }

        return imageDataList;
    }

}
