package com.progleasing.marketplace.etl.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class RetailerKeyMappingProvider {

    private final Map<String, String> retailerKeyMapping = new HashMap<>();

    public Map<String, String> getRetailerKeyMapping() {
        return retailerKeyMapping;
    }

    @PostConstruct
    public void loadRetailerKeyMapping() {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("retailer-key-mapping.csv");
             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {

            String line;
            boolean isHeader = true;

            while ((line = reader.readLine()) != null) {
                if (isHeader) {
                    isHeader = false;
                    continue;
                }
                String[] parts = line.split(",", -1);
                if (parts.length == 2) {
                    retailerKeyMapping.put(parts[0].trim(), parts[1].trim());
                }
            }

        } catch (Exception e) {
            log.error("[ERROR] Failed to load retailer-key-mapping.csv: " + e.getMessage());
        }
    }
}
