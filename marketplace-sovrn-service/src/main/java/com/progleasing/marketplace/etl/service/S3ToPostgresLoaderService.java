package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.PlS3ClientProvider;
import com.progleasing.marketplace.etl.repository.SovrnProductsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;

@Service
@Slf4j
public class S3ToPostgresLoaderService {

    private final DataSource dataSource;
    private final PlS3ClientProvider plS3ClientProvider;
    private final SovrnProductsRepository sovrnProductsRepository;
    private final PsqlCopyRunnerWithTransform psqlCopyRunnerWithTransform;
    private final String tempFolderName;
    private static final int RETENTION_DAYS = 5;

    @Autowired
    public S3ToPostgresLoaderService(DataSource dataSource, @Qualifier("pl-client-provider") PlS3ClientProvider plS3ClientProvider,
                                     SovrnProductsRepository sovrnProductsRepository,
                                     PsqlCopyRunnerWithTransform psqlCopyRunnerWithTransform,
                                     @Value("${product.pl.s3.temp-folder}") String tempFolderName) {
        this.dataSource = dataSource;
        this.plS3ClientProvider = plS3ClientProvider;
        this.sovrnProductsRepository = sovrnProductsRepository;
        this.psqlCopyRunnerWithTransform = psqlCopyRunnerWithTransform;
        this.tempFolderName = tempFolderName;
    }

    @Transactional
    public long loadSovrnProductsFromS3(String fileName, String plKey) throws IOException {
        log.info("Starting load of Sovrn products from S3 fileName {} ", fileName);

        String bucketName = plS3ClientProvider.getPlBucketName();
        S3Client s3Client = plS3ClientProvider.createPlS3Client();

        // Check if the file exists in S3
        if (!doesFileExistInS3(s3Client, bucketName, plKey)) {
            log.warn("No data/files found for retailer_key {}. File is missing. Will attempt deletion after {} days if no files are found.", fileName, RETENTION_DAYS);
            return 0;
        }

        GetObjectRequest s3Request = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(plKey)
                .build();

        try (
                InputStream s3Stream = s3Client.getObject(s3Request);
                Connection pgConn = dataSource.getConnection()
        ) {

            // Note: Table truncation is now handled at the beginning of the complete process
            log.info("Skipping individual retailer deletion - table truncation handled at process start");

            String tempS3Key = String.format("%s%s_%d.csv", tempFolderName, fileName, System.currentTimeMillis());
            log.info("Uploading file to S3 temp folder: {} (configured: {})", tempS3Key, tempFolderName);

            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(tempS3Key)
                    .build();

            s3Client.putObject(putRequest, RequestBody.fromInputStream(s3Stream, -1));
            log.info("Successfully uploaded to S3 temp folder: {}", tempS3Key);

            long rowsLoaded = psqlCopyRunnerWithTransform.initiateLoadingFromS3(tempS3Key, fileName, bucketName, s3Client);
            log.info("Successfully loaded {} rows from S3 temp file: {}", rowsLoaded, fileName);

            try {
                DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                        .bucket(bucketName)
                        .key(tempS3Key)
                        .build();
                s3Client.deleteObject(deleteRequest);
                log.info("Cleaned up S3 temp file: {}", tempS3Key);
            } catch (Exception e) {
                log.warn("Failed to delete S3 temp file: {}", tempS3Key, e);
            }

            return rowsLoaded;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    private boolean doesFileExistInS3(S3Client s3Client, String bucketName, String key) {
        try {
            s3Client.headObject(HeadObjectRequest.builder().bucket(bucketName).key(key).build());
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        }
    }

    @Scheduled(cron = "0 0 0 * * ?") // Runs daily at midnight
    @Transactional
    public void deleteOldRecords() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(RETENTION_DAYS);
        sovrnProductsRepository.deleteByCreationDateBefore(cutoffDate);
        log.info("Deleted records older than {} days", RETENTION_DAYS);
    }

    @Transactional
    public long loadSovrnProductsFromLocalFile(String localFilePath, String fileName) throws IOException {
        log.info("Starting load of Sovrn products from local file: {} (retailer_key: {})", localFilePath, fileName);
        long startTime = System.currentTimeMillis();

        try {
            // Note: Table truncation is now handled at the beginning of the complete process
            log.info("Skipping individual retailer deletion - table truncation handled at process start for file: {}", fileName);

            // Load data using COPY command with transformation
            log.info("Starting data load with bulk COPY operation...");
            long loadStartTime = System.currentTimeMillis();
            long recordsLoaded = psqlCopyRunnerWithTransform.initiateLoadingViaCommand(localFilePath, fileName);
            long loadEndTime = System.currentTimeMillis();

            if (recordsLoaded < 0) {
                throw new IOException("COPY command failed for file: " + localFilePath);
            }

            long totalTime = System.currentTimeMillis() - startTime;
            long loadTime = loadEndTime - loadStartTime;
            long recordsPerSecond = loadTime > 0 ? (recordsLoaded * 1000 / loadTime) : 0;

            log.info("Successfully loaded {} records from local file: {} in {} ms total ({} ms load time, {} records/sec)",
                    recordsLoaded, localFilePath, totalTime, loadTime, recordsPerSecond);
            return recordsLoaded;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("Error loading data from local file {} after {} ms: {}", localFilePath, totalTime, e.getMessage(), e);
            throw new IOException("Failed to load data from local file: " + localFilePath, e);
        }
    }
}
