package com.progleasing.marketplace.etl.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
@Slf4j
public class SovrnUnifiedFilterService {

    private final SovrnFolderLevelFilterService folderLevelFilterService;
    private final SovrnFileLevelFilterService fileLevelFilterService;

    @Autowired
    public SovrnUnifiedFilterService(SovrnFolderLevelFilterService folderLevelFilterService,
                                   SovrnFileLevelFilterService fileLevelFilterService) {
        this.folderLevelFilterService = folderLevelFilterService;
        this.fileLevelFilterService = fileLevelFilterService;
    }

    public boolean shouldProcessS3Key(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            log.warn("Empty S3 key provided for filtering");
            return false;
        }

        if (isWayfairS3Key(s3Key)) {
            String folderPath = extractFolderPathFromS3Key(s3Key);
            boolean shouldProcess = folderLevelFilterService.shouldProcessWayfairFolder(folderPath);
            log.debug("Wayfair folder-level filtering for {}: {}", s3Key, shouldProcess ? "PROCESS" : "SKIP");
            return shouldProcess;
        }

        if (fileLevelFilterService.isWalmartFile(s3Key)) {
            boolean shouldProcess = fileLevelFilterService.shouldProcessWalmartFile(s3Key);
            log.debug("Walmart file-level filtering for {}: {}", s3Key, shouldProcess ? "PROCESS" : "SKIP");
            return shouldProcess;
        }

        if (fileLevelFilterService.isTargetFile(s3Key)) {
            boolean shouldProcess = fileLevelFilterService.shouldProcessTargetFile(s3Key);
            log.debug("Target file-level filtering for {}: {}", s3Key, shouldProcess ? "PROCESS" : "SKIP");
            return shouldProcess;
        }

        log.debug("No filtering applied for S3 key {}: PROCESS", s3Key);
        return true;
    }

    private boolean isWayfairS3Key(String s3Key) {
        return s3Key != null && s3Key.toLowerCase().contains("wayfair");
    }

    /**
     * Extract folder path from S3 key
     * Example: "feed_exports/.../wayfair_north_america_11480/file.csv.gz" -> "wayfair_north_america_11480"
     */
    private String extractFolderPathFromS3Key(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            return null;
        }

        try {
            String[] pathParts = s3Key.split("/");
            
            // Look for the folder part (second to last part, before filename)
            if (pathParts.length >= 2) {
                return pathParts[pathParts.length - 2];
            }

            return null;
        } catch (Exception e) {
            log.error("Error extracting folder path from S3 key '{}': {}", s3Key, e.getMessage());
            return null;
        }
    }

    /**
     * Get filtering strategy for a given S3 key
     */
    public String getFilteringStrategy(String s3Key) {
        if (isWayfairS3Key(s3Key)) {
            return "FOLDER_LEVEL";
        } else if (fileLevelFilterService.isWalmartFile(s3Key) || fileLevelFilterService.isTargetFile(s3Key)) {
            return "FILE_LEVEL";
        } else {
            return "NO_FILTERING";
        }
    }

    /**
     * Get retailer type from S3 key
     */
    public String getRetailerType(String s3Key) {
        if (isWayfairS3Key(s3Key)) {
            return "wayfair";
        } else if (fileLevelFilterService.isWalmartFile(s3Key)) {
            return "walmart";
        } else if (fileLevelFilterService.isTargetFile(s3Key)) {
            return "target";
        } else {
            return "other";
        }
    }

    /**
     * Get detailed filtering information for debugging
     */
    public FilteringInfo getFilteringInfo(String s3Key) {
        String retailerType = getRetailerType(s3Key);
        String strategy = getFilteringStrategy(s3Key);
        boolean shouldProcess = shouldProcessS3Key(s3Key);
        
        String reason = "";
        if ("wayfair".equals(retailerType)) {
            String folderPath = extractFolderPathFromS3Key(s3Key);
            reason = shouldProcess ? "Folder ID is in allowed list" : "Folder ID not in allowed list";
        } else if ("walmart".equals(retailerType) || "target".equals(retailerType)) {
            reason = shouldProcess ? "File ID is in allowed list" : "File ID not in allowed list";
        } else {
            reason = "No filtering applied for this retailer";
        }

        return new FilteringInfo(s3Key, retailerType, strategy, shouldProcess, reason);
    }

    /**
     * Data class for filtering information
     */
    public static class FilteringInfo {
        public final String s3Key;
        public final String retailerType;
        public final String strategy;
        public final boolean shouldProcess;
        public final String reason;

        public FilteringInfo(String s3Key, String retailerType, String strategy, boolean shouldProcess, String reason) {
            this.s3Key = s3Key;
            this.retailerType = retailerType;
            this.strategy = strategy;
            this.shouldProcess = shouldProcess;
            this.reason = reason;
        }

        @Override
        public String toString() {
            return String.format("FilteringInfo{s3Key='%s', retailer='%s', strategy='%s', shouldProcess=%s, reason='%s'}", 
                s3Key, retailerType, strategy, shouldProcess, reason);
        }
    }
}
