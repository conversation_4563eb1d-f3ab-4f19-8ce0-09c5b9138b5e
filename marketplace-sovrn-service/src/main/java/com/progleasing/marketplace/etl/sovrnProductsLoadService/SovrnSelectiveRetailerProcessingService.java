package com.progleasing.marketplace.etl.sovrnProductsLoadService;

import com.progleasing.marketplace.etl.repository.SovrnProductsRepository;
import com.progleasing.marketplace.etl.config.PlS3ClientProvider;
import com.progleasing.marketplace.etl.config.SovrnS3ClientProvider;
import com.progleasing.marketplace.etl.service.S3ToPostgresLoaderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import jakarta.annotation.PreDestroy;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SovrnSelectiveRetailerProcessingService {

    private final PlS3ClientProvider plS3ClientProvider;
    private final SovrnS3ClientProvider sovrnS3ClientProvider;
    private final SovrnFileProcessorService fileProcessorService;
    private final SovrnProductsRepository sovrnProductsRepository;
    private final S3ToPostgresLoaderService s3ToPostgresLoaderService;

    @Value("${product.pl.s3.folder}")
    private String plS3Folder;

    // Parallel database loading configuration
    @Value("${product.pl.s3.parallel-loading.enabled:true}")
    private boolean parallelLoadingEnabled;

    @Value("${product.pl.s3.parallel-loading.thread-count:3}")
    private int parallelLoadingThreads;

    // Thread pool for parallel processing (initialized after @Value injection)
    private ExecutorService parallelProcessingExecutor;

    @Autowired
    public SovrnSelectiveRetailerProcessingService(
            @Qualifier("pl-client-provider") PlS3ClientProvider plS3ClientProvider,
            @Qualifier("sovrn-client-provider") SovrnS3ClientProvider sovrnS3ClientProvider,
            SovrnFileProcessorService fileProcessorService,
            SovrnProductsRepository sovrnProductsRepository,
            S3ToPostgresLoaderService s3ToPostgresLoaderService) {
        this.plS3ClientProvider = plS3ClientProvider;
        this.sovrnS3ClientProvider = sovrnS3ClientProvider;
        this.fileProcessorService = fileProcessorService;
        this.sovrnProductsRepository = sovrnProductsRepository;
        this.s3ToPostgresLoaderService = s3ToPostgresLoaderService;

        // Initialize parallel processing executor after @Value injection
        initializeParallelExecutor();

        log.info("Initialized selective retailer processing with parallel loading: {} (threads: {})",
                parallelLoadingEnabled, parallelLoadingThreads);
    }

    private void initializeParallelExecutor() {
        int threadCount = parallelLoadingEnabled ? parallelLoadingThreads : 2;
        this.parallelProcessingExecutor = Executors.newFixedThreadPool(threadCount);
        log.info("Created selective retailer processing executor with {} threads", threadCount);
    }

    public void processRetailersSelectively(String allowedRetailers) {
        long selectiveProcessingStartTime = System.currentTimeMillis();
        log.info("Starting selective retailer processing...");

        if (allowedRetailers != null && !allowedRetailers.trim().isEmpty()) {
            // Parse comma-separated retailer IDs
            String[] retailerIds = allowedRetailers.trim().split(",");
            log.info("Processing {} specific retailer ID(s): {}", retailerIds.length, allowedRetailers);

            int successCount = 0;
            int failureCount = 0;

            for (String retailerId : retailerIds) {
                String trimmedRetailerId = retailerId.trim();
                if (!trimmedRetailerId.isEmpty()) {
                    // Validate that retailer ID is numeric
                    if (!trimmedRetailerId.matches("\\d+")) {
                        log.warn("Skipping invalid retailer ID (must be numeric): {}", trimmedRetailerId);
                        failureCount++;
                        continue;
                    }

                    long singleRetailerStartTime = System.currentTimeMillis();
                    try {
                        log.info("Processing retailer ID: {}", trimmedRetailerId);

                        // Delete existing records for this retailer before processing new data
                        deleteExistingRetailerRecords(trimmedRetailerId);

                        processSpecificRetailer(trimmedRetailerId);

                        long singleRetailerEndTime = System.currentTimeMillis();
                        long singleRetailerDuration = singleRetailerEndTime - singleRetailerStartTime;
                        log.info("Successfully completed retailer ID: {} in {} ms ({} minutes)",
                                trimmedRetailerId, singleRetailerDuration, singleRetailerDuration / 60000.0);
                        successCount++;

                    } catch (Exception e) {
                        long singleRetailerEndTime = System.currentTimeMillis();
                        long singleRetailerDuration = singleRetailerEndTime - singleRetailerStartTime;
                        log.error("Failed to process retailer ID: {} after {} ms ({} minutes) - {}",
                                trimmedRetailerId, singleRetailerDuration, singleRetailerDuration / 60000.0, e.getMessage(), e);
                        failureCount++;
                    }
                } else {
                    log.warn("Skipping empty retailer ID in list: {}", allowedRetailers);
                    failureCount++;
                }
            }

            long selectiveProcessingEndTime = System.currentTimeMillis();
            long totalDuration = selectiveProcessingEndTime - selectiveProcessingStartTime;
            log.info("Selective retailer processing completed: {} successful, {} failed in {} ms ({} minutes)",
                    successCount, failureCount, totalDuration, totalDuration / 60000.0);

        } else {
            log.info("No specific retailer ID provided, processing all retailers");
            processAllRetailers();

            long selectiveProcessingEndTime = System.currentTimeMillis();
            long totalDuration = selectiveProcessingEndTime - selectiveProcessingStartTime;
            log.info("All retailers processing completed in {} ms ({} minutes)",
                    totalDuration, totalDuration / 60000.0);
        }
    }

    private void processSpecificRetailer(String retailerId) {
        try {
            S3Client plS3 = plS3ClientProvider.createPlS3Client();
            String plBucketName = plS3ClientProvider.getPlBucketName();
            
            log.info("Searching for retailer ID {} in PL S3 bucket: {}", retailerId, plBucketName);
            
            // Step 1: Search for retailer files in PL S3
            List<S3Object> retailerFilesInPL = findRetailerFilesInPLS3(plS3, plBucketName, retailerId);
            
            if (!retailerFilesInPL.isEmpty()) {
                log.info("Found {} files for retailer {} in PL S3, processing directly", 
                        retailerFilesInPL.size(), retailerId);
                processRetailerFiles(retailerFilesInPL, retailerId);
            } else {
                log.info("Retailer {} not found in PL S3, searching in Sovrn S3", retailerId);
                
                // Step 2: Search and copy from Sovrn S3 if not found in PL S3
                boolean copiedFromSovrn = copyRetailerFromSovrnToPL(retailerId);
                
                if (copiedFromSovrn) {
                    // Step 3: Search again in PL S3 after copying
                    retailerFilesInPL = findRetailerFilesInPLS3(plS3, plBucketName, retailerId);
                    if (!retailerFilesInPL.isEmpty()) {
                        log.info("Successfully copied and found {} files for retailer {} in PL S3", 
                                retailerFilesInPL.size(), retailerId);
                        processRetailerFiles(retailerFilesInPL, retailerId);
                    } else {
                        log.warn("Failed to find retailer {} files in PL S3 even after copying from Sovrn", retailerId);
                    }
                } else {
                    log.warn("Retailer {} not found in either PL S3 or Sovrn S3", retailerId);
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing specific retailer {}: {}", retailerId, e.getMessage(), e);
            throw new RuntimeException("Failed to process retailer " + retailerId, e);
        }
    }

    private void processAllRetailers() {
        try {
            log.info("Processing all retailers using existing file processor");
            fileProcessorService.processFilesFromPLS3();
        } catch (Exception e) {
            log.error("Error processing all retailers: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process all retailers", e);
        }
    }

    /**
     * Delete existing records for a specific retailer before processing new data
     * This ensures clean data for the retailer without affecting other retailers
     */
    private void deleteExistingRetailerRecords(String retailerId) {
        try {
            // Find retailer files to extract the actual folder name and transform it
            String transformedRetailerKey = extractRetailerKeyFromFiles(retailerId);

            log.info("Deleting existing records for retailer ID {} (transformed key: '{}')", retailerId, transformedRetailerKey);
            long deleteStartTime = System.currentTimeMillis();

            int deletedCount = sovrnProductsRepository.deleteByRetailerKey(transformedRetailerKey);

            long deleteEndTime = System.currentTimeMillis();
            long deleteDuration = deleteEndTime - deleteStartTime;

            log.info("Deleted {} existing records for retailer key '{}' in {} ms ({} seconds)",
                    deletedCount, transformedRetailerKey, deleteDuration, deleteDuration / 1000.0);

        } catch (Exception e) {
            log.error("Failed to delete existing records for retailer ID {}: {}", retailerId, e.getMessage(), e);
            throw new RuntimeException("Failed to delete existing records for retailer " + retailerId, e);
        }
    }

    /**
     * Extract retailer key from actual folder name found in S3 for the given retailer ID
     * This dynamically finds the folder and transforms it using the same logic as file processing
     */
    private String extractRetailerKeyFromFiles(String retailerId) {
        try {
            // First, try to find files in PL S3
            S3Client plS3 = plS3ClientProvider.createPlS3Client();
            String plBucketName = plS3ClientProvider.getPlBucketName();
            List<S3Object> retailerFiles = findRetailerFilesInPLS3(plS3, plBucketName, retailerId);

            if (!retailerFiles.isEmpty()) {
                // Extract retailer key from the first file's S3 path
                String s3Key = retailerFiles.get(0).key();
                String transformedKey = extractAndTransformRetailerKey(s3Key);
                log.info("Extracted retailer key '{}' from PL S3 file: {}", transformedKey, s3Key);
                return transformedKey;
            }

            // If not found in PL S3, try Sovrn S3
            S3Client sovrnS3 = sovrnS3ClientProvider.createSovrnS3Client();
            String sovrnBucketName = sovrnS3ClientProvider.getSovrnBucketName();
            String sovrnPrefix = sovrnS3ClientProvider.getSovrnPrefix();
            List<S3Object> sovrnRetailerFiles = findRetailerFilesInSovrnS3(sovrnS3, sovrnBucketName, sovrnPrefix, retailerId);

            if (!sovrnRetailerFiles.isEmpty()) {
                // Extract retailer key from the first file's S3 path
                String s3Key = sovrnRetailerFiles.get(0).key();
                String transformedKey = extractAndTransformRetailerKey(s3Key);
                log.info("Extracted retailer key '{}' from Sovrn S3 file: {}", transformedKey, s3Key);
                return transformedKey;
            }

            // Fallback: if no files found, use retailer ID as lowercase key
            log.warn("No files found for retailer ID {} in either PL S3 or Sovrn S3, using lowercase ID as key", retailerId);
            return retailerId.toLowerCase();

        } catch (Exception e) {
            log.error("Error extracting retailer key from files for retailer ID {}: {}", retailerId, e.getMessage(), e);
            // Fallback to lowercase retailer ID
            return retailerId.toLowerCase();
        }
    }



    private List<S3Object> findRetailerFilesInPLS3(S3Client plS3, String bucketName, String retailerId) {
        List<S3Object> retailerFiles = new ArrayList<>();
        String continuationToken = null;

        try {
            do {
                ListObjectsV2Request.Builder requestBuilder = ListObjectsV2Request.builder()
                        .bucket(bucketName)
                        .prefix(plS3Folder);

                if (continuationToken != null) {
                    requestBuilder.continuationToken(continuationToken);
                }

                ListObjectsV2Response response = plS3.listObjectsV2(requestBuilder.build());

                // Filter files that contain the retailer ID (now includes folder structure)
                for (S3Object obj : response.contents()) {
                    if (obj.key().endsWith(".gz") && obj.size() > 0 && containsRetailerId(obj.key(), retailerId)) {
                        retailerFiles.add(obj);
                        log.debug("Found retailer file in PL S3: {} (Size: {} KB)",
                                obj.key(), obj.size() / 1024);
                    }
                }

                continuationToken = response.nextContinuationToken();

            } while (continuationToken != null);

        } catch (Exception e) {
            log.error("Error searching for retailer {} files in PL S3: {}", retailerId, e.getMessage(), e);
        }

        log.info("Found {} files for retailer {} in PL S3", retailerFiles.size(), retailerId);
        return retailerFiles;
    }

    private boolean copyRetailerFromSovrnToPL(String retailerId) {
        try {
            S3Client sovrnS3 = sovrnS3ClientProvider.createSovrnS3Client();
            S3Client plS3 = plS3ClientProvider.createPlS3Client();
            
            String sovrnBucketName = sovrnS3ClientProvider.getSovrnBucketName();
            String sovrnPrefix = sovrnS3ClientProvider.getSovrnPrefix();
            String plBucketName = plS3ClientProvider.getPlBucketName();
            
            log.info("Searching for retailer {} in Sovrn S3 bucket: {}", retailerId, sovrnBucketName);
            
            // Find retailer files in Sovrn S3
            List<S3Object> sovrnRetailerFiles = findRetailerFilesInSovrnS3(sovrnS3, sovrnBucketName, sovrnPrefix, retailerId);
            
            if (sovrnRetailerFiles.isEmpty()) {
                log.warn("No files found for retailer {} in Sovrn S3", retailerId);
                return false;
            }
            
            log.info("Found {} files for retailer {} in Sovrn S3, copying to PL S3", 
                    sovrnRetailerFiles.size(), retailerId);
            
            // Copy each file from Sovrn S3 to PL S3
            int successCount = 0;
            for (S3Object sovrnFile : sovrnRetailerFiles) {
                try {
                    copyFileFromSovrnToPL(sovrnS3, plS3, sovrnBucketName, plBucketName, sovrnFile);
                    successCount++;
                } catch (Exception e) {
                    log.error("Failed to copy file {} for retailer {}: {}", 
                            sovrnFile.key(), retailerId, e.getMessage(), e);
                }
            }
            
            log.info("Successfully copied {}/{} files for retailer {} from Sovrn S3 to PL S3", 
                    successCount, sovrnRetailerFiles.size(), retailerId);
            
            return successCount > 0;
            
        } catch (Exception e) {
            log.error("Error copying retailer {} from Sovrn S3 to PL S3: {}", retailerId, e.getMessage(), e);
            return false;
        }
    }

    private List<S3Object> findRetailerFilesInSovrnS3(S3Client sovrnS3, String bucketName, String prefix, String retailerId) {
        List<S3Object> retailerFiles = new ArrayList<>();
        String continuationToken = null;

        try {
            do {
                ListObjectsV2Request.Builder requestBuilder = ListObjectsV2Request.builder()
                        .bucket(bucketName)
                        .prefix(prefix);

                if (continuationToken != null) {
                    requestBuilder.continuationToken(continuationToken);
                }

                ListObjectsV2Response response = sovrnS3.listObjectsV2(requestBuilder.build());

                // Filter files that contain the retailer ID
                for (S3Object obj : response.contents()) {
                    if (obj.key().endsWith(".gz") && obj.size() > 0 && containsRetailerId(obj.key(), retailerId)) {
                        retailerFiles.add(obj);
                        log.debug("Found retailer file in Sovrn S3: {} (Size: {} KB)",
                                obj.key(), obj.size() / 1024);
                    }
                }

                continuationToken = response.nextContinuationToken();

            } while (continuationToken != null);

        } catch (Exception e) {
            log.error("Error searching for retailer {} files in Sovrn S3: {}", retailerId, e.getMessage(), e);
        }

        return retailerFiles;
    }

    private void copyFileFromSovrnToPL(S3Client sovrnS3, S3Client plS3, String sovrnBucket, String plBucket, S3Object sovrnFile) throws IOException {
        try {
            // Extract folder name and file name to preserve complete folder structure
            String[] pathParts = sovrnFile.key().split("/");
            if (pathParts.length < 2) {
                throw new IllegalArgumentException("Invalid S3 key format: " + sovrnFile.key());
            }

            String folderName = pathParts[pathParts.length - 2]; // e.g., "adorama_11359"
            String fileName = pathParts[pathParts.length - 1];   // e.g., "products.csv.gz"

            // Create destination key with complete folder structure
            String plKey = plS3Folder + folderName + "/" + fileName;

            log.info("Copying file with folder structure: {} -> {} (Size: {} KB)",
                    sovrnFile.key(), plKey, sovrnFile.size() / 1024);

            // Use the same approach as SovrnS3CopyService that was working
            streamFileFromSovrnToPL(sovrnS3, plS3, sovrnBucket, sovrnFile.key(), plBucket, plKey);

            log.info("Successfully copied file to folder structure: {}/{}", folderName, fileName);

        } catch (Exception e) {
            log.error("Error copying file {} from Sovrn to PL with folder structure: {}", sovrnFile.key(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Stream file from Sovrn S3 to PL S3 using chunked approach without content-length
     * This avoids the content-length mismatch issues entirely
     */
    private void streamFileFromSovrnToPL(S3Client sovrnS3, S3Client plS3, String sovrnBucket,
                                       String sovrnKey, String plBucket, String plKey) throws IOException {
        try {
            log.info("Starting chunked transfer without content-length specification");

            // Get the object with streaming
            GetObjectRequest getRequest = GetObjectRequest.builder()
                    .bucket(sovrnBucket)
                    .key(sovrnKey)
                    .build();

            // Use a buffered approach to avoid content-length issues
            try (InputStream inputStream = sovrnS3.getObject(getRequest)) {

                // Read the entire stream into a byte array to avoid streaming issues
                log.info("Reading file into memory buffer...");
                byte[] fileData = inputStream.readAllBytes();
                log.info("File read into buffer: {} bytes", fileData.length);

                PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(plBucket)
                        .key(plKey)
                        .contentType("application/gzip")
                        .contentEncoding("gzip")
                        .contentLength((long) fileData.length)
                        .build();

                // Upload using byte array - this is reliable and avoids streaming issues
                plS3.putObject(putRequest, RequestBody.fromBytes(fileData));

                log.info("Successfully uploaded file using byte buffer ({} bytes)", fileData.length);

            } catch (OutOfMemoryError memError) {
                log.error("Out of memory error - file too large for memory buffer: {}", memError.getMessage());
                // Fall back to temporary file approach for very large files
                streamLargeFileWithTempFile(sovrnS3, plS3, sovrnBucket, sovrnKey, plBucket, plKey);
            } catch (Exception streamException) {
                log.error("Error during buffered transfer: {}", streamException.getMessage(), streamException);
                throw streamException;
            }

        } catch (Exception e) {
            log.error("Error streaming file from {} to {}: {}", sovrnKey, plKey, e.getMessage(), e);
            throw new IOException("Failed to stream file from Sovrn S3 to PL S3", e);
        }
    }

    /**
     * Fallback method for very large files that can't fit in memory
     */
    private void streamLargeFileWithTempFile(S3Client sovrnS3, S3Client plS3, String sovrnBucket,
                                           String sovrnKey, String plBucket, String plKey) throws IOException {
        java.io.File tempFile = null;
        try {
            log.info("Using temporary file approach for large file");

            // Create temporary file
            tempFile = java.io.File.createTempFile("sovrn_large_", ".tmp");

            GetObjectRequest getRequest = GetObjectRequest.builder()
                    .bucket(sovrnBucket)
                    .key(sovrnKey)
                    .build();

            // Download to temporary file
            try (InputStream inputStream = sovrnS3.getObject(getRequest);
                 FileOutputStream fos = new FileOutputStream(tempFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;

                    if (totalBytes % (100 * 1024 * 1024) == 0) {
                        log.info("Downloaded {} MB to temp file", totalBytes / (1024 * 1024));
                    }
                }

                log.info("Downloaded {} bytes to temporary file", totalBytes);
            }

            // Upload from temporary file
            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(plBucket)
                    .key(plKey)
                    .contentType("application/gzip")
                    .contentEncoding("gzip")
                    .contentLength(tempFile.length())
                    .build();

            plS3.putObject(putRequest, RequestBody.fromFile(tempFile));
            log.info("Successfully uploaded large file using temporary file ({} bytes)", tempFile.length());

        } finally {
            // Clean up temporary file
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                log.info("Temporary file cleanup: {}", deleted ? "success" : "failed");
            }
        }
    }

    private void processRetailerFiles(List<S3Object> retailerFiles, String retailerId) {
        try {
            int threadCount = parallelLoadingEnabled ? parallelLoadingThreads : 2;
            log.info("Processing {} files for retailer {} using parallel processing ({} threads)",
                    retailerFiles.size(), retailerId, threadCount);

            // Note: No need to delete existing records since table is truncated at process start
            log.info("Processing {} files for retailer {} (table already truncated at process start)",
                    retailerFiles.size(), retailerId);

            if (retailerFiles.size() <= 1) {
                // Single file - process directly
                processFileSequentially(retailerFiles, retailerId);
            } else {
                // Multiple files - process in parallel with configurable threads
                processFilesInParallel(retailerFiles, retailerId);
            }

        } catch (Exception e) {
            log.error("Error processing retailer {} files: {}", retailerId, e.getMessage(), e);
            throw new RuntimeException("Failed to process retailer " + retailerId + " files", e);
        }
    }

    private void processFilesInParallel(List<S3Object> retailerFiles, String retailerId) {
        int threadCount = parallelLoadingEnabled ? parallelLoadingThreads : 2;
        log.info("Starting parallel processing with {} threads for {} files", threadCount, retailerFiles.size());

        // Split files into batches for parallel processing based on thread count
        int effectiveThreadCount = parallelLoadingEnabled ? parallelLoadingThreads : 2;
        int batchSize = (int) Math.ceil((double) retailerFiles.size() / effectiveThreadCount);
        List<List<S3Object>> batches = new ArrayList<>();

        for (int i = 0; i < retailerFiles.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, retailerFiles.size());
            batches.add(retailerFiles.subList(i, endIndex));
        }

        log.info("Split {} files into {} batches for parallel processing (using {} threads)",
                retailerFiles.size(), batches.size(), effectiveThreadCount);

        // Process each batch in parallel
        List<CompletableFuture<Void>> futures = batches.stream()
                .map(batch -> CompletableFuture.runAsync(() -> {
                    String threadName = Thread.currentThread().getName();
                    log.info("Thread {} starting to process {} files", threadName, batch.size());

                    for (S3Object file : batch) {
                        processCompleteEndToEndPipeline(file, retailerId, threadName);
                    }

                    log.info("Thread {} completed processing {} files", threadName, batch.size());
                }, parallelProcessingExecutor))
                .collect(Collectors.toList());

        // Wait for all threads to complete
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        try {
            allFutures.join();
            log.info("All parallel processing threads completed successfully for retailer {}", retailerId);
        } catch (Exception e) {
            log.error("Error in parallel processing for retailer {}: {}", retailerId, e.getMessage(), e);
            throw new RuntimeException("Parallel processing failed for retailer " + retailerId, e);
        }
    }

    private void processFileSequentially(List<S3Object> retailerFiles, String retailerId) {
        log.info("Processing {} files sequentially", retailerFiles.size());

        for (S3Object file : retailerFiles) {
            processCompleteEndToEndPipeline(file, retailerId, "main-thread");
        }
    }

    /**
     * Complete end-to-end pipeline for a single file:
     * 1. Copy from Sovrn S3 (if needed)
     * 2. Paste to PL S3
     * 3. Connect to PL S3 and fetch data
     * 4. Stream and load to PostgreSQL
     */
    private void processCompleteEndToEndPipeline(S3Object file, String retailerId, String threadName) {
        try {
            String fileName = file.key().substring(file.key().lastIndexOf('/') + 1);
            log.info("[{}] Starting end-to-end processing for retailer {} file: {} (Size: {} KB)",
                    threadName, retailerId, fileName, file.size() / 1024);

            long startTime = System.currentTimeMillis();

            processFileDirectly(file, retailerId, threadName);

            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;

            log.info("[{}] Successfully completed end-to-end processing for retailer {} file: {} in {} ms",
                    threadName, retailerId, fileName, processingTime);

        } catch (Exception e) {
            log.error("[{}] Failed to process retailer {} file {}: {}",
                    threadName, retailerId, file.key(), e.getMessage(), e);
            // Don't throw exception to allow other files to continue processing
        }
    }

    private void processFileDirectly(S3Object file, String retailerId, String threadName) {
        try {
            String fileName = file.key().substring(file.key().lastIndexOf('/') + 1);
            log.info("[{}] Processing file directly: {} for retailer {}", threadName, fileName, retailerId);

            // Extract retailer key from S3 path (same logic as SovrnFileProcessorService)
            String retailerKey = extractAndTransformRetailerKey(file.key());

            // Download file from S3 to local temp directory
            String tempFilePath = downloadFileFromS3(file);

            try {
                // Use S3ToPostgresLoaderService to load the file
                // This replicates the logic from SovrnFileProcessorService.importFileToDatabase()
                long recordsLoaded = s3ToPostgresLoaderService.loadSovrnProductsFromLocalFile(tempFilePath, retailerKey);

                if (recordsLoaded >= 0) {
                    log.info("[{}] Successfully imported {} records from file: {} with retailerKey: '{}'",
                            threadName, recordsLoaded, fileName, retailerKey);
                } else {
                    log.warn("[{}] Database import returned negative count for file: {} with retailerKey: '{}'",
                            threadName, fileName, retailerKey);
                }

            } finally {
                // Clean up temporary file
                java.io.File tempFile = new java.io.File(tempFilePath);
                if (tempFile.exists()) {
                    boolean deleted = tempFile.delete();
                    log.debug("[{}] Temporary file cleanup: {} ({})", threadName, tempFilePath, deleted ? "success" : "failed");
                }
            }

        } catch (Exception e) {
            log.error("[{}] Error processing file directly: {} - {}", threadName, file.key(), e.getMessage(), e);
            throw new RuntimeException("Failed to process file directly: " + file.key(), e);
        }
    }

    private String downloadFileFromS3(S3Object file) throws IOException {
        S3Client plS3 = plS3ClientProvider.createPlS3Client();
        String bucketName = plS3ClientProvider.getPlBucketName();

        String fileName = file.key().substring(file.key().lastIndexOf('/') + 1);
        String tempDir = "src/main/resources/temp";

        // Create temp directory if it doesn't exist
        java.io.File tempDirFile = new java.io.File(tempDir);
        if (!tempDirFile.exists()) {
            tempDirFile.mkdirs();
        }

        String tempFilePath = tempDir + "/" + fileName.replace(".gz", "");

        GetObjectRequest request = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(file.key())
                .build();

        // Download and decompress if it's a .gz file
        try (InputStream inputStream = plS3.getObject(request);
             InputStream decompressedStream = fileName.endsWith(".gz") ?
                     new java.util.zip.GZIPInputStream(inputStream) : inputStream;
             FileOutputStream outputStream = new FileOutputStream(tempFilePath)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = decompressedStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        return tempFilePath;
    }

    private boolean containsRetailerId(String s3Key, String retailerId) {
        if (s3Key == null || retailerId == null) {
            return false;
        }

        // Extract folder name from S3 key
        String[] pathParts = s3Key.split("/");
        if (pathParts.length >= 2) {
            String folderName = pathParts[pathParts.length - 2]; // Second to last part (folder name)

            // Check if folder name ends with the retailer ID
            return folderName.endsWith("_" + retailerId) || folderName.endsWith(retailerId);
        }

        return false;
    }

    private String extractAndTransformRetailerKey(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            log.warn("S3 key is null or empty, using 'unknown' as retailer key");
            return "unknown";
        }

        try {
            // Extract retailer folder from S3 path (same logic as SovrnFileProcessorService)
            String[] pathParts = s3Key.split("/");
            String retailerFolder = "unknown";

            if (pathParts.length >= 2) {
                retailerFolder = pathParts[pathParts.length - 2];
                log.info("Extracted retailer folder '{}' from S3 key: {}", retailerFolder, s3Key);
            } else {
                log.warn("Unable to extract retailer folder from S3 key: {} - using 'unknown'", s3Key);
            }

            // Transform retailer key (same logic as PsqlCopyRunnerWithTransform.transformRetailerKey)
            String cleanFolderName = retailerFolder.endsWith("/") ?
                    retailerFolder.substring(0, retailerFolder.length() - 1) : retailerFolder;
            String cleanForComparison = cleanFolderName.toLowerCase().replaceAll("[^a-z]", "");

            // Handle special cases
            switch (cleanForComparison) {
                case "bestbuyus": return "bestbuy";
                case "hpus": return "hp";
                case "westelmus": return "westelm";
                case "walmartaffiliateprogram": return "walmart";
                case "thetirerack": return "tirerack";
                case "wayfairnorthamerica": return "wayfair";
                case "thehomedepot": return "homedepot";
                default: return cleanForComparison;
            }
        } catch (Exception e) {
            log.warn("Error extracting/transforming retailer key from S3 key '{}': {} - using 'unknown'", s3Key, e.getMessage());
            return "unknown";
        }
    }

    @PreDestroy
    public void cleanup() {
        if (parallelProcessingExecutor != null && !parallelProcessingExecutor.isShutdown()) {
            log.info("Shutting down parallel processing executor...");
            parallelProcessingExecutor.shutdown();
            try {
                if (!parallelProcessingExecutor.awaitTermination(60, java.util.concurrent.TimeUnit.SECONDS)) {
                    parallelProcessingExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                parallelProcessingExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("Parallel processing executor shutdown completed");
        }
    }
}
