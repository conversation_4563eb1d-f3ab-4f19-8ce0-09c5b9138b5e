package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.SovrnRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.repositories.*;
import com.progleasing.marketplace.etl.repository.SovrnProductsRepository;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class SovrnCatalogSyncService {

    private static int MAX_BATCH_SIZE = 100;
    private SovrnRequestConfig sovrnRequestConfig;
    private ObjectMapper objectMapper;
    private SovrnProductsRepository sovrnProductsRepository;
    private ProductDataRepository productDataRepository;
    private SovrnDataService sovrnDataService;

    public void syncCatalogItems(String messagePayload, String messageId) throws Exception {
        ETLMessageDTO messageDTO = objectMapper.readValue(messagePayload, ETLMessageDTO.class);
        log.info("Processing Sovrn Search for category {} searchTerm {} retailerName {}", messageDTO.getCategoryKey(),
                messageDTO.getSearchTerm(), messageDTO.getRetailerName());
        if(!sovrnDataService.isMessageProcessed(messageDTO.getReferenceId(),messageId)) {
            sovrnDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.STARTED);
            List<String> retailers = Arrays.stream(messageDTO.getRetailerKey().split(","))
                    .map(String::trim)
                    .map(String::toLowerCase)
                    .collect(Collectors.toList());
            List<ProductData> searchTermProductDataList = productDataRepository.findProductsBySearchTerm(
                    messageDTO.getSearchTerm(), ProductData.FeedSource.sovrn);
            List<String> productIds = searchTermProductDataList.stream()
                    .map(ProductData::getId)
                    .collect(Collectors.toList());

            int maxProductsToImport = sovrnRequestConfig.getMaxProductsToImport();
            int totalFetched = 0;
            int page = 0;
            List<SovrnProducts> productsToExport = new ArrayList<>();
            try {
                while (totalFetched < maxProductsToImport) {
                    int currentBatchSize = Math.min(MAX_BATCH_SIZE, maxProductsToImport - totalFetched);
                    Pageable pageable = PageRequest.of(page, currentBatchSize);
                    Specification<SovrnProducts> spec = buildProductSearchSpec(messageDTO.getSearchTerm(), retailers, messageDTO.getMinPrice(), messageDTO.getMaxPrice());
                    Page<SovrnProducts> sovrnProducts = sovrnProductsRepository.findAll(spec, pageable);
                    List<SovrnProducts> content = sovrnProducts.getContent();
                    if (content.isEmpty()) break;
                    productsToExport.addAll(content);
                    totalFetched += content.size();
                    log.info("Fetched {} products (Total so far: {}) for retailer {}", content.size(), totalFetched, messageDTO.getRetailerName());
                    if (!sovrnProducts.hasNext()) break;
                    page++;
                }
                if (!CollectionUtils.isEmpty(productsToExport)) {
                    sovrnDataService.populateProductData(productsToExport, messageDTO, productIds);
                } else {
                    log.warn(" No product found for message ID {} message {}", messageId, objectMapper.writeValueAsString(messageDTO));
                }
                sovrnDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.COMPLETED);
            } catch (Exception e) {
                log.error("Catalog item sync failed for category ID {}: {}", messageDTO.getCategoryKey(), e.getMessage(), e);
                sovrnDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING);
                throw e;
            }
        } else{
            log.warn("Messge with id {} Etl id {} already processed",messageId,messageDTO.getReferenceId());
        }
    }

    public static Specification<SovrnProducts> buildProductSearchSpec(
            String text,
            List<String> retailers,
            BigDecimal minPrice,
            BigDecimal maxPrice
    ) {
        return (Root<SovrnProducts> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (text != null && !text.trim().isEmpty()) {
                List<Predicate> keywordPredicates = new ArrayList<>();
                for (String word : text.toLowerCase().split("\\s+")) {
                    Predicate productNameLike = cb.like(cb.lower(root.get("productName")), "%" + word + "%");
                    Predicate categoryLike = cb.like(cb.lower(root.get("merchantRawCategory")), "%" + word + "%");
                    keywordPredicates.add(cb.or(productNameLike, categoryLike));
                }
                predicates.add(cb.or(keywordPredicates.toArray(new Predicate[0])));
            }
            if (retailers != null && !retailers.isEmpty()) {
                predicates.add(root.get("retailerKey").in(retailers));
            }
            if (minPrice != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("salePrice"), minPrice));
            }
            if (maxPrice != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("salePrice"), maxPrice));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

}