package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.repository.CustomSovrnProductsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.postgresql.copy.CopyManager;
import org.postgresql.core.BaseConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
public class PsqlCopyRunnerWithTransform {

    private final CustomSovrnProductsRepository customSovrnProductsRepository;
    private static final String STANDARD_IMAGE_CDN_SNIPPET = "https://img-cdn.viglink.com/cdn-cgi/image/w=400/";
    private static final String THUMBNAIL_CDN_SNIPPET = "https://img-cdn.viglink.com/cdn-cgi/image/w=200/";


    @Autowired
    public PsqlCopyRunnerWithTransform(CustomSovrnProductsRepository customSovrnProductsRepository) {
        this.customSovrnProductsRepository = customSovrnProductsRepository;
    }

    long initiateLoadingViaCommand(String csvFilePath, String retailerKey) {

        log.info("*** initiateLoadingViaCommand --> Processing file: {}", csvFilePath);
        try {
            String transformedFilePath;
            if (csvFilePath.endsWith(".csv")) {
                transformedFilePath = csvFilePath.substring(0, csvFilePath.length() - 4) + "_transformed.csv";
            } else {
                transformedFilePath = csvFilePath + "_transformed.csv";
            }

            // Check if input file exists
            if (!Files.exists(Paths.get(csvFilePath))) {
                log.error("Input file does not exist: {}", csvFilePath);
                return -1;
            }

            transformCsvFile(csvFilePath, transformedFilePath, retailerKey);

            long rowsCopied = customSovrnProductsRepository.copyFromCsv(transformedFilePath);

            if (rowsCopied >= 0) {
                log.info(" Successfully copied {} rows to database from file: {}", rowsCopied, csvFilePath);
            } else {
                log.error("COPY command failed for file: {}", csvFilePath);
            }

            // Clean up transformed file
            try {
                Files.deleteIfExists(Paths.get(transformedFilePath));
                log.info("Cleaned up transformed file: {}", transformedFilePath);
            } catch (Exception e) {
                log.warn("Failed to delete transformed file: {}", transformedFilePath, e);
            }

            return rowsCopied;

        } catch (Exception e) {
            log.error("FAILED DUE TO : {}", e.getMessage(), e);
            return -1;
        }
    }

    private void transformCsvFile(String inputFilePath, String outputFilePath, String retailerKey) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String now = LocalDateTime.now().format(dtf);

        String transformedRetailerKey = transformRetailerKey(retailerKey);
        log.info("Final transformed retailer key to be used in CSV: '{}'", transformedRetailerKey);
        log.info("Starting CSV transformation from {} to {}", inputFilePath, outputFilePath);

        try {
            Path inputPath = Paths.get(inputFilePath);
            if (!Files.exists(inputPath)) {
                throw new RuntimeException("Input file does not exist: " + inputFilePath);
            }
            if (!Files.isReadable(inputPath)) {
                throw new RuntimeException("Input file is not readable: " + inputFilePath);
            }

            Path outputPath = Paths.get(outputFilePath);
            Path outputDir = outputPath.getParent();
            if (outputDir != null && !Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            try (
                    Reader reader = Files.newBufferedReader(inputPath);
                    CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader());
                    Writer writer = Files.newBufferedWriter(outputPath);
                    CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                            "ean", "image_url", "in_stock", "isbn", "language", "brand",
                            "merchant_name", "mpn", "product_name", "domain", "retail_price", "sale_price", "sku",
                            "thumbnail_url", "upc", "affiliated_url", "merchant_raw_category", "retailer_key", "creation_date"
                    ))
            ) {
                int recordCount = 0;
                for (CSVRecord record : csvParser) {
                    String transformedImageUrl = STANDARD_IMAGE_CDN_SNIPPET + record.get("image_url");
                    String transformedThumbnailUrl = THUMBNAIL_CDN_SNIPPET + record.get("thumbnail_url");
                    csvPrinter.printRecord(
                            record.get("ean"),
                            transformedImageUrl,
                            record.get("in_stock"),
                            record.get("isbn"),
                            record.get("language"),
                            record.get("brand"),
                            record.get("merchant_name"),
                            record.get("mpn"),
                            record.get("name"), // renamed to product_name
                            record.get("domain"),
                            record.get("retail_price"),
                            record.get("sale_price"),
                            record.get("sku"),
                            transformedThumbnailUrl,
                            record.get("upc"),
                            record.get("affiliated_url"),
                            record.get("merchant_raw_category"),
                            transformedRetailerKey,
                            now
                    );
                    recordCount++;
                }
                csvPrinter.flush();
                log.info("Successfully transformed {} records from {} to {} with retailerKey: '{}'", recordCount, inputFilePath, outputFilePath, transformedRetailerKey);
                log.info("*** CSV TRANSFORMATION END ***");
            }
        } catch (Exception e) {
            log.error("Error transforming CSV from {} to {}: {}", inputFilePath, outputFilePath, e.getMessage(), e);
            throw new RuntimeException("Error transforming CSV from " + inputFilePath + " to " + outputFilePath + ": " + e.getMessage(), e);
        }
    }

    private void transformCsv(InputStream inputStream, Writer writer, String retailerKey) {
        DateTimeFormatter dtf = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        String now = LocalDateTime.now().format(dtf);

        // Transform retailer key from numeric ID to retailer name format
        String transformedRetailerKey = transformRetailerKey(retailerKey);

        try (
                Reader reader = new InputStreamReader(inputStream);
                CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader());
                CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                        "ean", "image_url", "in_stock", "isbn", "language", "brand",
                        "merchant_name", "mpn", "product_name", "domain", "retail_price", "sale_price", "sku",
                        "thumbnail_url", "upc", "affiliated_url", "merchant_raw_category", "retailer_key", "creation_date"
                ))
        ) {
            for (CSVRecord record : csvParser) {
                csvPrinter.printRecord(
                        record.get("ean"),
                        record.get("image_url"),
                        record.get("in_stock"),
                        record.get("isbn"),
                        record.get("language"),
                        record.get("brand"),
                        record.get("merchant_name"),
                        record.get("mpn"),
                        record.get("name"), // renamed to product_name
                        record.get("domain"),
                        record.get("retail_price"),
                        record.get("sale_price"),
                        record.get("sku"),
                        record.get("thumbnail_url"),
                        record.get("upc"),
                        record.get("affiliated_url"),
                        record.get("merchant_raw_category"),
                        transformedRetailerKey,
                        now
                );
            }
            csvPrinter.flush();
        } catch (Exception e) {
            log.info("  Error transforming CSV due to {}", e.getMessage());
        }
    }


    public static long runJdbcCopy(String host, String port, String db, String user,
                                   String password, String csvFilePath, String tableName) {
        String url = String.format("jdbc:postgresql://%s:%s/%s", host, port, db);
        log.info("Starting runJdbcCopy for file: {}", csvFilePath);
        long startTime = System.currentTimeMillis();

        try (Connection conn = DriverManager.getConnection(url, user, password);
             Reader reader = new FileReader(csvFilePath)) {

            CopyManager copyManager = new CopyManager(conn.unwrap(BaseConnection.class));
            long rows = copyManager.copyIn(
                    "COPY " + tableName + " (ean, image_url, in_stock, isbn, language, brand, merchant_name, " +
                            "mpn, product_name, domain, retail_price, sale_price, sku, thumbnail_url, " +
                            "upc, affiliated_url, merchant_raw_category, retailer_key, creation_date) " +
                            "FROM STDIN WITH (FORMAT csv, HEADER true, DELIMITER ',', QUOTE '\"', ENCODING 'UTF8')",
                    reader
            );

            long endTime = System.currentTimeMillis();
            long durationMs = endTime - startTime;
            log.info("runJdbcCopy completed: {} rows in {} ms ({} rows/sec)",
                    rows, durationMs, durationMs > 0 ? (rows * 1000 / durationMs) : "N/A");
            return rows; // Return actual row count instead of -1

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long durationMs = endTime - startTime;
            log.error("runJdbcCopy failed after {} ms: {}", durationMs, e.getMessage(), e);
            return -1; // Return -1 only on failure
        }
    }

    public long initiateLoadingFromS3(String s3TempKey, String retailerKey, String bucketName, S3Client s3Client) {
        log.info("*** initiateLoadingFromS3 --> Processing S3 file: {}", s3TempKey);

        try {
            GetObjectRequest getRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(s3TempKey)
                    .build();

            Path tempInput = Files.createTempFile("s3_temp_input", ".csv");
            Path tempOutput = Files.createTempFile("s3_temp_output", ".csv");

            try {
                try (InputStream s3Stream = s3Client.getObject(getRequest);
                     FileOutputStream fos = new FileOutputStream(tempInput.toFile())) {
                    s3Stream.transferTo(fos);
                }

                log.info("Downloaded S3 temp file to local: {}", tempInput.toString());

                transformCsvFile(tempInput.toString(), tempOutput.toString(), retailerKey);
                log.info("Transformed CSV saved to: {}", tempOutput.toString());

                long rowsCopied = customSovrnProductsRepository.copyFromCsv(tempOutput.toString());

                if (rowsCopied >= 0) {
                    log.info(" Successfully copied {} rows to database from S3 temp file: {}", rowsCopied, s3TempKey);
                } else {
                    log.error("COPY command failed for S3 temp file: {}", s3TempKey);
                }

                return rowsCopied;

            } finally {
                try {
                    Files.deleteIfExists(tempInput);
                    Files.deleteIfExists(tempOutput);
                    log.info("Cleaned up local temp files");
                } catch (Exception e) {
                    log.warn("Failed to delete local temp files", e);
                }
            }

        } catch (Exception e) {
            log.error("FAILED processing S3 temp file {}: {}", s3TempKey, e.getMessage(), e);
            return -1;
        }
    }
    String transformRetailerKey(String folderName) {
        if (folderName == null || folderName.trim().isEmpty()) {
            return "unknown";
        }

        try {
            String cleanFolderName = folderName.endsWith("/") ? folderName.substring(0, folderName.length() - 1) : folderName;
            String cleanForComparison = cleanFolderName.toLowerCase().replaceAll("[^a-z]", "");

            // Handle special cases
            switch (cleanForComparison) {
                case "bestbuyus": return "bestbuy";
                case "hpus": return "hp";
                case "westelmus": return "westelm";
                case "walmartaffiliateprogram": return "walmart";
                case "thetirerack": return "tirerack";
                case "wayfairnorthamerica": return "wayfair";
                case "thehomedepot": return "homedepot";
                case "gamestopinc": return "gamestop";
                case "dellhomehomeoffice": return "dell";
                default: return cleanForComparison;
            }
        } catch (Exception e) {
            return "unknown";
        }
    }
}

