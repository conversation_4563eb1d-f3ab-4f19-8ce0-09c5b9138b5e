package com.progleasing.marketplace.etl.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "sovrn_products")
@Data
public class SovrnProducts {

    @Id
    @Column(name = "sku", length = 100, nullable = false)
    private String sku;

    @Column(name="retailer_key")
    private String retailerKey;

    @Column(name = "ean", length = 50)
    private String ean;

    @Column(name = "isbn", length = 50)
    private String isbn;

    @Column(name = "mpn", length = 100)
    private String mpn;

    @Column(name = "upc", length = 50)
    private String upc;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "brand", length = 100)
    private String brand;

    @Column(name = "language", length = 20)
    private String language;

    @Column(name = "in_stock", length = 10)
    private String inStock;

    @Column(name = "merchant_name", length = 100)
    private String merchantName;

    @Column(name = "merchant_raw_category", columnDefinition = "TEXT")
    private String merchantRawCategory;

    @Column(name = "retail_price", precision = 10, scale = 2)
    private BigDecimal retailPrice;

    @Column(name = "sale_price", precision = 10, scale = 2)
    private BigDecimal salePrice;

    @Column(name = "image_url", columnDefinition = "TEXT")
    private String imageUrl;

    @Column(name = "thumbnail_url", columnDefinition = "TEXT")
    private String thumbnailUrl;

    @Column(name = "affiliated_url", columnDefinition = "TEXT")
    private String affiliatedUrl;

    @Column(name = "domain", length = 255)
    private String domain;

    @Column(name = "creation_date")
    private LocalDateTime creationDate;
}
