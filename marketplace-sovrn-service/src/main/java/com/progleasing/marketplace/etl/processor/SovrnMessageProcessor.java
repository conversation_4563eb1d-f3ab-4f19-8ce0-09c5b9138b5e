package com.progleasing.marketplace.etl.processor;

import com.progleasing.marketplace.etl.service.SovrnCatalogSyncService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Component
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.sovrn.listener.enabled", havingValue = "true", matchIfMissing = true)
public class SovrnMessageProcessor {

    private final SovrnCatalogSyncService sovrnCatalogSyncService;
    private final Executor executor;

    public SovrnMessageProcessor(
            SovrnCatalogSyncService syncService,
            @Qualifier("sovrnTaskExecutor") Executor executor) {
        this.sovrnCatalogSyncService = syncService;
        this.executor = executor;
    }

    @SqsListener(value = "${aws.sqs.sovrn.queue-name}",
            maxConcurrentMessages = "${aws.sovrn.sqs.concurrent-message}",
            acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL,
            maxMessagesPerPoll = "${aws.sovrn.sqs.concurrent-message}"
    )
    public void handle(List<Message<String>> messages) {
        log.info("Messages received: {}", messages.size());

        List<CompletableFuture<Void>> futures = messages.stream().map(message ->
                CompletableFuture.runAsync(() -> {
                    try {
                        UUID messageId = (UUID) message.getHeaders().get("id");
                        String payload = message.getPayload();
                        log.info("Received message with payload: {} and id {}", payload, messageId.toString());
                        sovrnCatalogSyncService.syncCatalogItems(payload, messageId.toString());
                        Acknowledgement.acknowledge(message);
                    } catch (Exception e) {
                        log.error("Error processing message", e);
                    }
                }, executor)
        ).toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

}