package com.progleasing.marketplace.etl.config;

import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Configuration class for Sovrn folder filtering.
 * Reads allowed folder IDs from application.yml and provides methods to check if a folder should be processed.
 * Only folders listed in the configuration will be processed.
 */
@Configuration
@ConfigurationProperties(prefix = "product.sovrn-products-load.s3.allowed-folder-ids")
@Getter
@Setter
@Slf4j
public class SovrnFolderFilterConfig {

    // With @ConfigurationProperties, Spring Boot will automatically populate these fields
    // from the YAML configuration under allowed-folder-ids
    private String wayfair;
    private String homedepot;
    private String lowes;
    private String walmart;
    private String target;
    private String dickssportinggoods;
    private String westelm;
    private String tractorsupply;
    private String adorama;
    private String bestbuy;
    private String williamssonoma;
    private String golfgalaxy;
    private String tirerack;
    private String ashley;
    private String gamestop;
    private String costplusworldmarket;
    private String surlatable;
    private String dellhomehomeoffice;
    private String dysoninc;
    private String hp;
    private String mattressfirm;

    private Set<String> walmartFolderIds;
    private Set<String> wayfairFolderIds;
    private Set<String> targetFolderIds;
    private Set<String> allAllowedFolderIds;

    @PostConstruct
    public void init() {
        log.info("=== INITIALIZING SOVRN FOLDER FILTER CONFIGURATION ===");

        // Parse ALL retailer fields dynamically
        allAllowedFolderIds = new HashSet<>();

        // Process all 21 retailers
        processRetailer("wayfair", wayfair);
        processRetailer("homedepot", homedepot);
        processRetailer("lowes", lowes);
        processRetailer("walmart", walmart);
        processRetailer("target", target);
        processRetailer("dickssportinggoods", dickssportinggoods);
        processRetailer("westelm", westelm);
        processRetailer("tractorsupply", tractorsupply);
        processRetailer("adorama", adorama);
        processRetailer("bestbuy", bestbuy);
        processRetailer("williamssonoma", williamssonoma);
        processRetailer("golfgalaxy", golfgalaxy);
        processRetailer("tirerack", tirerack);
        processRetailer("ashley", ashley);
        processRetailer("gamestop", gamestop);
        processRetailer("costplusworldmarket", costplusworldmarket);
        processRetailer("surlatable", surlatable);
        processRetailer("dellhomehomeoffice", dellhomehomeoffice);
        processRetailer("dysoninc", dysoninc);
        processRetailer("hp", hp);
        processRetailer("mattressfirm", mattressfirm);

        // Set legacy fields for backward compatibility
        walmartFolderIds = parseCommaSeparatedIds(walmart, "Walmart");
        wayfairFolderIds = parseCommaSeparatedIds(wayfair, "Wayfair");
        targetFolderIds = parseCommaSeparatedIds(target, "Target");

        log.info("=== CONFIGURATION SUMMARY ===");
        log.info("Total ALLOWED folder IDs: {} -> {}", allAllowedFolderIds.size(), allAllowedFolderIds);
        log.info("=== INITIALIZATION COMPLETE ===");
    }

    /**
     * Process a single retailer's configuration
     */
    private void processRetailer(String retailerName, String folderIdsString) {
        if (folderIdsString != null && !folderIdsString.trim().isEmpty()) {
            Set<String> retailerFolderIds = parseCommaSeparatedIds(folderIdsString, retailerName);
            allAllowedFolderIds.addAll(retailerFolderIds);
            log.info("  {} ALLOWED folder IDs: {}", retailerName.toUpperCase(), retailerFolderIds);
        } else {
            log.debug("  {} has no folder IDs configured (empty/null)", retailerName.toUpperCase());
        }
    }

    /**
     * Parse comma-separated string into a Set of folder IDs
     */
    private Set<String> parseCommaSeparatedIds(String commaSeparatedIds, String retailerName) {
        if (commaSeparatedIds == null || commaSeparatedIds.trim().isEmpty()) {
            log.warn("No folder IDs configured for retailer: {}", retailerName);
            return Collections.emptySet();
        }

        Set<String> folderIds = Arrays.stream(commaSeparatedIds.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toSet());

        log.debug("Parsed {} folder IDs for {}: {}", folderIds.size(), retailerName, folderIds);
        return folderIds;
    }

    /**
     * Check if a folder ID should be processed based on configuration
     * Returns true if the folder should be processed (i.e., IS in the allowed list)
     */
    public boolean shouldProcessFolder(String folderId) {
        if (folderId == null || folderId.trim().isEmpty()) {
            log.warn("Empty or null folder ID provided for processing check - will skip");
            return false; // Skip unknown folders
        }

        String trimmedFolderId = folderId.trim();
        boolean isAllowed = allAllowedFolderIds.contains(trimmedFolderId);

        if (isAllowed) {
            log.debug("Folder ID {} is ALLOWED for processing (in allowed list)", trimmedFolderId);
        } else {
            log.debug("Folder ID {} will be SKIPPED (not in allowed list)", trimmedFolderId);
        }

        return isAllowed;
    }

    /**
     * Extract folder ID from S3 key path
     * Updated to handle actual S3 format: feed_exports/hash/retailer_name_ID/file.csv.gz
     * Examples:
     * - feed_exports/ea3e5537553c6cf739af818331fa97f2/adorama_11359/35134.csv.gz -> 11359
     * - feed_exports/ea3e5537553c6cf739af818331fa97f2/cost_plus_world_market_10203/12455.csv.gz -> 10203
     */
    public String extractFolderIdFromS3Key(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            log.warn("Empty or null S3 key provided for folder ID extraction");
            return null;
        }

        try {
            log.debug("Extracting folder ID from S3 key: {}", s3Key);

            // Split by '/' and look for the retailer folder (3rd part)
            String[] pathParts = s3Key.split("/");
            log.debug("Path parts: {}", String.join(", ", pathParts));

            // Expected format: feed_exports/hash/retailer_name_ID/file.csv.gz
            // So retailer folder is at index 2
            if (pathParts.length >= 3) {
                String retailerFolder = pathParts[2]; // e.g., "adorama_11359", "cost_plus_world_market_10203"
                log.debug("Retailer folder: '{}'", retailerFolder);

                // Extract ID from the end of the folder name
                // Pattern: retailer_name_ID where ID is numeric
                String folderId = extractIdFromFolderName(retailerFolder);
                if (folderId != null) {
                    log.info("Extracted folder ID '{}' from retailer folder '{}' in S3 key: {}", folderId, retailerFolder, s3Key);
                    return folderId;
                }
            }

            log.warn("No valid folder ID found in S3 key: {}", s3Key);
            return null;

        } catch (Exception e) {
            log.error("Error extracting folder ID from S3 key '{}': {}", s3Key, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extract numeric ID from retailer folder name
     * Examples:
     * - "adorama_11359" -> "11359"
     * - "cost_plus_world_market_10203" -> "10203"
     * - "walmart_affiliate_program_11946" -> "11946"
     * - "wayfair_north_america_48541" -> "48541"
     */
    private String extractIdFromFolderName(String folderName) {
        if (folderName == null || folderName.trim().isEmpty()) {
            return null;
        }

        try {
            // Look for numeric ID at the end of the folder name
            // Pattern: retailer_name_ID where ID is numeric
            String[] parts = folderName.split("_");

            // Check from the end backwards for the first numeric part
            for (int i = parts.length - 1; i >= 0; i--) {
                String part = parts[i];
                if (part.matches("\\d+")) { // Check if part is all digits
                    log.debug("Found numeric ID '{}' in folder name '{}'", part, folderName);
                    return part;
                }
            }

            log.debug("No numeric ID found in folder name: {}", folderName);
            return null;

        } catch (Exception e) {
            log.error("Error extracting ID from folder name '{}': {}", folderName, e.getMessage(), e);
            return null;
        }
    }



    /**
     * Check if an S3 key should be processed based on its folder ID and retailer type
     * Returns true if the file should be processed based on the new logic:
     * - For Walmart/Wayfair/Target: only process files from allowed folder IDs
     * - For other retailers: process all files (no filtering)
     */
    public boolean shouldProcessS3Key(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            log.warn("Empty or null S3 key provided - skipping");
            return false;
        }

        String retailerType = getRetailerTypeFromS3Key(s3Key);

        // For non-Walmart/Wayfair/Target retailers, process all files
        if (!isFilteredRetailer(retailerType)) {
            log.debug("S3 key {} belongs to non-filtered retailer '{}' - processing without restrictions", s3Key, retailerType);
            return true;
        }

        // For Walmart/Wayfair/Target, apply folder ID filtering
        String folderId = extractFolderIdFromS3Key(s3Key);
        if (folderId == null) {
            log.warn("Could not extract folder ID from S3 key: {} - skipping filtered retailer file", s3Key);
            return false;
        }

        boolean shouldProcess = shouldProcessFolder(folderId);
        log.debug("S3 key {} belongs to filtered retailer '{}' with folder ID '{}' - shouldProcess: {}",
                s3Key, retailerType, folderId, shouldProcess);
        return shouldProcess;
    }

    /**
     * Get retailer name based on folder ID
     */
    public String getRetailerNameByFolderId(String folderId) {
        if (folderId == null) return "Unknown";
        
        if (walmartFolderIds.contains(folderId)) return "Walmart";
        if (wayfairFolderIds.contains(folderId)) return "Wayfair";
        if (targetFolderIds.contains(folderId)) return "Target";
        
        return "Unknown";
    }

    /**
     * Extract retailer type from S3 key
     */
    public String getRetailerTypeFromS3Key(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            return "unknown";
        }

        try {
            String[] pathParts = s3Key.split("/");

            // Look for retailer folder pattern
            for (String part : pathParts) {
                if (part.contains("walmart_affiliate_program")) {
                    return "walmart";
                } else if (part.contains("wayfair")) {
                    return "wayfair";
                } else if (part.contains("target")) {
                    return "target";
                }
            }

            // If no retailer pattern found in folder names, return "other"
            // Note: We don't identify retailers by folder ID anymore since
            // folder ID extraction now works consistently for all retailers

            // If no specific retailer pattern found, return "other"
            return "other";

        } catch (Exception e) {
            log.error("Error extracting retailer type from S3 key '{}': {}", s3Key, e.getMessage(), e);
            return "unknown";
        }
    }

    /**
     * Check if a retailer type requires folder ID filtering
     */
    public boolean isFilteredRetailer(String retailerType) {
        return "walmart".equals(retailerType) || "wayfair".equals(retailerType) || "target".equals(retailerType);
    }

    /**
     * Get all allowed folder IDs
     */
    public Set<String> getAllAllowedFolderIds() {
        return Collections.unmodifiableSet(allAllowedFolderIds);
    }

    /**
     * Get folder IDs for a specific retailer
     */
    public Set<String> getFolderIdsByRetailer(String retailer) {
        if (retailer == null) return Collections.emptySet();
        
        switch (retailer.toLowerCase()) {
            case "walmart":
                return Collections.unmodifiableSet(walmartFolderIds);
            case "wayfair":
                return Collections.unmodifiableSet(wayfairFolderIds);
            case "target":
                return Collections.unmodifiableSet(targetFolderIds);
            default:
                return Collections.emptySet();
        }
    }
}
