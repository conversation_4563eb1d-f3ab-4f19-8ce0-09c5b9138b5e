package com.progleasing.marketplace.etl.sovrnProductsLoadService;

import com.progleasing.marketplace.etl.config.PlS3ClientProvider;
import com.progleasing.marketplace.etl.config.SovrnS3ClientProvider;
import com.progleasing.marketplace.etl.service.SovrnUnifiedFilterService;
import com.progleasing.marketplace.etl.config.SovrnFolderFilterConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service to copy files from Sovrn S3 to PL S3 in compressed (.gz) format
 * Uses existing S3 clients and folder filtering logic
 */
@Service
@Slf4j
public class SovrnS3CopyService {

    private final SovrnS3ClientProvider sovrnS3ClientProvider;
    private final PlS3ClientProvider plS3ClientProvider;
    private final SovrnUnifiedFilterService unifiedFilterService;
    private final SovrnFolderFilterConfig folderFilterConfig;
    private final String plS3Folder;

    @Autowired
    public SovrnS3CopyService(@Qualifier("sovrn-client-provider") SovrnS3ClientProvider sovrnS3ClientProvider,
                              @Qualifier("pl-client-provider") PlS3ClientProvider plS3ClientProvider,
                              SovrnUnifiedFilterService unifiedFilterService,
                              SovrnFolderFilterConfig folderFilterConfig,
                              @Value("${product.pl.s3.folder}") String plS3Folder) {
        this.sovrnS3ClientProvider = sovrnS3ClientProvider;
        this.plS3ClientProvider = plS3ClientProvider;
        this.unifiedFilterService = unifiedFilterService;
        this.folderFilterConfig = folderFilterConfig;
        this.plS3Folder = plS3Folder;
    }

    /**
     * Main method to copy files from Sovrn S3 to PL S3
     * Applies folder filtering logic and keeps files in compressed format
     * Properly manages resources and memory cleanup
     */
    public void copyFilesFromSovrnToPL() {
        log.info("Starting S3 to S3 copy process from Sovrn to PL...");

        S3Client sovrnS3 = null;
        S3Client plS3 = null;

        try {
            // Create S3 clients
            sovrnS3 = sovrnS3ClientProvider.createSovrnS3Client();
            plS3 = plS3ClientProvider.createPlS3Client();

            String sovrnBucketName = sovrnS3ClientProvider.getSovrnBucketName();
            String sovrnPrefix = sovrnS3ClientProvider.getSovrnPrefix();
            String plBucketName = plS3ClientProvider.getPlBucketName();

            log.info("Connected to Sovrn S3 bucket: {}", sovrnBucketName);
            log.info("Connected to PL S3 bucket: {}", plBucketName);
            log.info("Sovrn prefix: {}", sovrnPrefix);
            log.info("PL folder: {}", plS3Folder);

            // List all objects in Sovrn S3
            List<S3Object> allObjects = listAllObjects(sovrnS3, sovrnBucketName, sovrnPrefix);
            log.info("Found {} total objects in Sovrn S3", allObjects.size());

            // Debug: Show all unique folder paths to help identify Wayfair folder
            Set<String> uniqueFolders = allObjects.stream()
                    .map(obj -> {
                        String[] parts = obj.key().split("/");
                        return parts.length > 2 ? parts[2] : "unknown"; // Get retailer folder part
                    })
                    .collect(Collectors.toSet());

            log.info("=== ALL RETAILER FOLDERS FOUND IN SOVRN S3 ===");
            uniqueFolders.forEach(folder -> log.info("  Folder: {}", folder));
            log.info("=== END FOLDER LIST ===");

            // Filter objects based on configuration - ONLY copy configured retailers
            List<S3Object> objectsToProcess = filterObjectsForConfiguredRetailersOnly(allObjects);
            log.info("After filtering: {} objects to process, {} objects to skip", 
                    objectsToProcess.size(), allObjects.size() - objectsToProcess.size());

            // Copy each filtered object to PL S3
            int successCount = 0;
            int failureCount = 0;

            for (int i = 0; i < objectsToProcess.size(); i++) {
                S3Object obj = objectsToProcess.get(i);
                try {
                    // Log memory usage before processing large files
                    logMemoryUsage("Before processing file " + (i + 1));

                    copyObjectToPLS3(sovrnS3, plS3, sovrnBucketName, plBucketName, obj, i + 1, objectsToProcess.size());
                    successCount++;

                    // Force garbage collection after each file to free memory
                    if (obj.size() > 100 * 1024 * 1024) { // For files > 100MB
                        System.gc();
                        logMemoryUsage("After processing large file " + (i + 1));
                    }

                } catch (Exception e) {
                    log.error("Failed to copy object {}: {}", obj.key(), e.getMessage(), e);
                    failureCount++;
                }
            }

            log.info("S3 copy process completed. Success: {}, Failures: {}", successCount, failureCount);

        } catch (Exception e) {
            log.error("Error during S3 copy process: {}", e.getMessage(), e);
            throw new RuntimeException("S3 copy process failed", e);
        } finally {
            // Force garbage collection to free memory (S3 connections managed by connection pool)
            System.gc();
            log.info("Memory cleanup completed");
        }
    }



    /**
     * Log current memory usage for monitoring
     */
    private void logMemoryUsage(String context) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / (1024 * 1024); // MB
        long freeMemory = runtime.freeMemory() / (1024 * 1024); // MB
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory() / (1024 * 1024); // MB

        log.info("Memory Usage [{}]: Used={}MB, Free={}MB, Total={}MB, Max={}MB",
                context, usedMemory, freeMemory, totalMemory, maxMemory);
    }

    /**
     * List all objects from Sovrn S3 with pagination support
     */
    private List<S3Object> listAllObjects(S3Client sovrnS3, String bucketName, String prefix) {
        List<S3Object> allObjects = new ArrayList<>();
        String continuationToken = null;

        do {
            ListObjectsV2Request.Builder requestBuilder = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix);

            if (continuationToken != null) {
                requestBuilder.continuationToken(continuationToken);
            }

            ListObjectsV2Response response = sovrnS3.listObjectsV2(requestBuilder.build());
            allObjects.addAll(response.contents());
            continuationToken = response.nextContinuationToken();

        } while (continuationToken != null);

        return allObjects;
    }

    /**
     * Filter objects to ONLY include configured retailers
     * This ensures only retailers configured in application.yml are copied to PL S3
     */
    private List<S3Object> filterObjectsForConfiguredRetailersOnly(List<S3Object> allObjects) {
        List<S3Object> filteredObjects = new ArrayList<>();
        List<String> skippedFiles = new ArrayList<>();

        log.info("=== APPLYING CONFIGURATION-BASED FILTERING ===");
        log.info("Only copying retailers configured in allowed-folder-ids and allowed-file-ids");
        log.info("All other retailers (out of 318+ in Sovrn S3) will be skipped");

        // Debug: Show all configured retailers from SovrnFolderFilterConfig
        Set<String> allConfiguredFolderIds = folderFilterConfig.getAllAllowedFolderIds();
        log.info("=== CONFIGURED RETAILERS FROM APPLICATION.YML ===");
        log.info("Total configured folder IDs: {} -> {}", allConfiguredFolderIds.size(), allConfiguredFolderIds);
        log.info("Using SovrnFolderFilterConfig to support ALL 21 configured retailers");

        for (S3Object obj : allObjects) {
            String fileName = obj.key().substring(obj.key().lastIndexOf('/') + 1);
            long fileSizeKB = obj.size() / 1024;

            // Use SovrnFolderFilterConfig to check if file should be processed
            // This supports ALL 21 configured retailers, not just 3
            String folderId = folderFilterConfig.extractFolderIdFromS3Key(obj.key());
            String retailerType = folderFilterConfig.getRetailerTypeFromS3Key(obj.key());
            boolean shouldProcess = folderFilterConfig.shouldProcessFolder(folderId);

            // Debug: Show detailed filtering info for each file
            log.debug("FILTERING DEBUG: File: {} | S3 Path: {} | FolderID: {} | Retailer: {} | ShouldProcess: {}",
                    fileName, obj.key(), folderId, retailerType, shouldProcess);

            // ONLY process files that are configured in application.yml
            if (shouldProcess) {
                filteredObjects.add(obj);
                log.info("WILL COPY: {} (Size: {} KB) - Retailer: {}, FolderID: {}, Reason: Folder ID is configured in application.yml",
                        fileName, fileSizeKB, retailerType.toUpperCase(), folderId);
            } else {
                skippedFiles.add(fileName);
                log.info("WILL SKIP: {} (Size: {} KB) - Retailer: {}, FolderID: {}, Reason: Folder ID not configured in application.yml (S3 Path: {})",
                        fileName, fileSizeKB, retailerType.toUpperCase(), folderId, obj.key());
            }
        }

        log.info("=== FILTERING SUMMARY ===");
        log.info("Total files found in Sovrn S3: {}", allObjects.size());
        log.info("Files to copy (configured retailers): {}", filteredObjects.size());
        log.info("Files to skip (non-configured retailers): {}", skippedFiles.size());

        if (!skippedFiles.isEmpty()) {
            log.info("Skipped files: {}", String.join(", ", skippedFiles));
        }

        return filteredObjects;
    }

    /**
     * OLD Filter objects based on folder filtering logic
     * @deprecated Replaced by filterObjectsForConfiguredRetailersOnly
     */
    @Deprecated
    private List<S3Object> filterObjects(List<S3Object> allObjects) {
        List<S3Object> filteredObjects = new ArrayList<>();
        List<String> skippedFiles = new ArrayList<>();

        for (S3Object obj : allObjects) {
            String fileName = obj.key().substring(obj.key().lastIndexOf('/') + 1);
            long fileSizeKB = obj.size() / 1024;

            // Use the new unified filtering service
            SovrnUnifiedFilterService.FilteringInfo filteringInfo = unifiedFilterService.getFilteringInfo(obj.key());

            if (filteringInfo.shouldProcess) {
                filteredObjects.add(obj);

                log.info("WILL PROCESS: {} (Size: {} KB) - Retailer: {}, Strategy: {}, Reason: {}",
                        fileName, fileSizeKB, filteringInfo.retailerType.toUpperCase(),
                        filteringInfo.strategy, filteringInfo.reason);
            } else {
                skippedFiles.add(fileName);

                log.info("WILL SKIP: {} (Size: {} KB) - Retailer: {}, Strategy: {}, Reason: {}",
                        fileName, fileSizeKB, filteringInfo.retailerType.toUpperCase(),
                        filteringInfo.strategy, filteringInfo.reason);
            }
        }

        log.info("Filtering summary: {} files to process, {} files to skip", 
                filteredObjects.size(), skippedFiles.size());
        
        if (!skippedFiles.isEmpty()) {
            log.info("Skipped files: {}", String.join(", ", skippedFiles));
        }

        return filteredObjects;
    }

    /**
     * Copy a single object from Sovrn S3 to PL S3 in compressed format
     */
    private void copyObjectToPLS3(S3Client sovrnS3, S3Client plS3, String sovrnBucketName,
                                  String plBucketName, S3Object obj, int currentIndex, int totalCount) throws IOException {

        String fileName = obj.key().substring(obj.key().lastIndexOf('/') + 1);
        String retailerFolder = extractRetailerFolder(obj.key());

        // Remove trailing slash from plS3Folder if present to avoid double slashes
        String basePath = plS3Folder.endsWith("/") ? plS3Folder.substring(0, plS3Folder.length() - 1) : plS3Folder;
        String plS3Key = basePath + "/" + retailerFolder + "/" + fileName;

        long fileSizeGB = obj.size() / (1024 * 1024 * 1024);
        long fileSizeMB = obj.size() / (1024 * 1024);

        log.info("Copying file {}/{}: {} -> {} (Size: {} MB)", currentIndex, totalCount, obj.key(), plS3Key, fileSizeMB);

        if (fileSizeGB >= 1) {
            log.warn("Large file detected: {} GB - Using streaming copy", fileSizeGB);
        }

        // Stream file directly from Sovrn S3 to PL S3 (handles large files)
        streamFileFromSovrnToPLS3(sovrnS3, plS3, sovrnBucketName, plBucketName, obj.key(), plS3Key);
        log.info("Successfully streamed to PL S3: {} (Size: {} MB)", plS3Key, fileSizeMB);
    }

    /**
     * Extract retailer folder name from S3 key
     * The S3 key structure is: feed_exports/hash/retailer_folder/file.csv.gz
     * We want to extract the retailer_folder part
     */
    private String extractRetailerFolder(String s3Key) {
        String[] pathParts = s3Key.split("/");

        // The retailer folder is typically the second-to-last part before the filename
        // Structure: feed_exports/hash/retailer_folder/filename.csv.gz
        if (pathParts.length >= 3) {
            // Return the retailer folder (second-to-last part)
            return pathParts[pathParts.length - 2];
        }

        // Fallback: if path structure is different, look for the last folder before filename
        if (pathParts.length >= 2) {
            return pathParts[pathParts.length - 2];
        }

        // Last resort: extract from filename if no folder structure
        String fileName = pathParts[pathParts.length - 1];
        return fileName.substring(0, fileName.lastIndexOf('.'));
    }



    /**
     * Stream file directly from Sovrn S3 to PL S3 (handles large files efficiently)
     * This method uses streaming to avoid loading large files into memory
     */
    private void streamFileFromSovrnToPLS3(S3Client sovrnS3, S3Client plS3, String sovrnBucketName,
                                          String plBucketName, String sovrnKey, String plKey) throws IOException {

        try {
            // First get the object metadata to get the content length
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(sovrnBucketName)
                    .key(sovrnKey)
                    .build();

            HeadObjectResponse headResponse = sovrnS3.headObject(headRequest);
            long contentLength = headResponse.contentLength();

            log.debug("File {} has content length: {} bytes", sovrnKey, contentLength);

            // Now get the object with streaming
            GetObjectRequest getRequest = GetObjectRequest.builder()
                    .bucket(sovrnBucketName)
                    .key(sovrnKey)
                    .build();

            try (InputStream inputStream = sovrnS3.getObject(getRequest)) {

                PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(plBucketName)
                        .key(plKey)
                        .contentType("application/gzip")
                        .contentEncoding("gzip")
                        .contentLength(contentLength)
                        .build();

                // Stream directly from Sovrn S3 to PL S3 with proper content length
                plS3.putObject(putRequest, RequestBody.fromInputStream(inputStream, contentLength));

                // Explicitly close the input stream to free resources immediately
                inputStream.close();

            } catch (Exception streamException) {
                log.error("Error during streaming operation: {}", streamException.getMessage(), streamException);
                throw streamException;
            }

        } catch (Exception e) {
            log.error("Error streaming file from {} to {}: {}", sovrnKey, plKey, e.getMessage(), e);
            throw new IOException("Failed to stream file from Sovrn S3 to PL S3", e);
        }
    }
}
