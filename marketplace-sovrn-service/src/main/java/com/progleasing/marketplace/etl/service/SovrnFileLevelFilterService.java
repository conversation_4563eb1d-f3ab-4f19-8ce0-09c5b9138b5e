package com.progleasing.marketplace.etl.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service for file-level filtering
 * Used for retailers where we process folders but filter individual files (like Walmart and Target)
 * 
 * Example:
 * - walmart_affiliate_program_11946/36616.csv.gz -> PROCESS (file ID 36616 is allowed)
 * - walmart_affiliate_program_11946/99999.csv.gz -> SKIP (file ID 99999 is not allowed)
 * - target_10238/11551.csv.gz -> PROCESS (file ID 11551 is allowed)
 * - target_10238/12345.csv.gz -> SKIP (file ID 12345 is not allowed)
 */
@Service
@Slf4j
public class SovrnFileLevelFilterService {

    @Value("${product.sovrn-products-load.s3.allowed-file-ids.walmart:}")
    private String walmartAllowedFileIds;

    @Value("${product.sovrn-products-load.s3.allowed-file-ids.target:}")
    private String targetAllowedFileIds;

    private Set<String> allowedWalmartFileIds;
    private Set<String> allowedTargetFileIds;

    @PostConstruct
    public void init() {
        allowedWalmartFileIds = parseCommaSeparatedIds(walmartAllowedFileIds, "Walmart");
        allowedTargetFileIds = parseCommaSeparatedIds(targetAllowedFileIds, "Target");
        
        log.info("Initialized File-Level Filter Service:");
        log.info("  Walmart allowed file IDs: {}", allowedWalmartFileIds);
        log.info("  Target allowed file IDs: {}", allowedTargetFileIds);
    }

    /**
     * Check if a Walmart file should be processed based on file ID
     * 
     * @param s3Key Example: "feed_exports/.../walmart_affiliate_program_11946/36616.csv.gz"
     * @return true if file should be processed, false if should be skipped
     */
    public boolean shouldProcessWalmartFile(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            log.warn("Empty S3 key provided for Walmart file filtering");
            return false;
        }

        String fileId = extractFileIdFromS3Key(s3Key);
        if (fileId == null) {
            log.warn("Could not extract file ID from Walmart S3 key: {}", s3Key);
            return false;
        }

        boolean shouldProcess = allowedWalmartFileIds.contains(fileId);
        
        if (shouldProcess) {
            log.debug("Walmart file {} with ID {} is ALLOWED for processing", s3Key, fileId);
        } else {
            log.debug("Walmart file {} with ID {} will be SKIPPED (not in allowed list)", s3Key, fileId);
        }

        return shouldProcess;
    }

    /**
     * Check if a Target file should be processed based on file ID
     * 
     * @param s3Key Example: "feed_exports/.../target_10238/11551.csv.gz"
     * @return true if file should be processed, false if should be skipped
     */
    public boolean shouldProcessTargetFile(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            log.warn("Empty S3 key provided for Target file filtering");
            return false;
        }

        String fileId = extractFileIdFromS3Key(s3Key);
        if (fileId == null) {
            log.warn("Could not extract file ID from Target S3 key: {}", s3Key);
            return false;
        }

        boolean shouldProcess = allowedTargetFileIds.contains(fileId);
        
        if (shouldProcess) {
            log.debug("Target file {} with ID {} is ALLOWED for processing", s3Key, fileId);
        } else {
            log.debug("Target file {} with ID {} will be SKIPPED (not in allowed list)", s3Key, fileId);
        }

        return shouldProcess;
    }

    /**
     * Check if an S3 key is a Walmart file
     */
    public boolean isWalmartFile(String s3Key) {
        if (s3Key == null) return false;
        return s3Key.toLowerCase().contains("walmart_affiliate_program");
    }

    /**
     * Check if an S3 key is a Target file
     */
    public boolean isTargetFile(String s3Key) {
        if (s3Key == null) return false;
        return s3Key.toLowerCase().contains("target");
    }

    /**
     * Extract file ID from S3 key
     * Examples:
     * - "feed_exports/.../walmart_affiliate_program_11946/36616.csv.gz" -> "36616"
     * - "feed_exports/.../target_10238/11551.csv.gz" -> "11551"
     */
    private String extractFileIdFromS3Key(String s3Key) {
        if (s3Key == null || s3Key.trim().isEmpty()) {
            return null;
        }

        try {
            String[] pathParts = s3Key.split("/");

            // Look for the filename (last part)
            if (pathParts.length > 0) {
                String filename = pathParts[pathParts.length - 1];
                
                // Extract ID from filename (remove .csv.gz extension)
                if (filename.contains(".csv")) {
                    String fileId = filename.substring(0, filename.indexOf(".csv"));
                    
                    // Validate it's a number
                    if (fileId.matches("\\d+")) {
                        return fileId;
                    }
                }
            }

            log.warn("Could not extract valid file ID from S3 key: {}", s3Key);
            return null;

        } catch (Exception e) {
            log.error("Error extracting file ID from S3 key '{}': {}", s3Key, e.getMessage());
            return null;
        }
    }

    /**
     * Parse comma-separated string into a Set of file IDs
     */
    private Set<String> parseCommaSeparatedIds(String commaSeparatedIds, String retailerName) {
        if (commaSeparatedIds == null || commaSeparatedIds.trim().isEmpty()) {
            log.warn("No file IDs configured for retailer: {}", retailerName);
            return Collections.emptySet();
        }

        Set<String> fileIds = Arrays.stream(commaSeparatedIds.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toSet());

        log.debug("Parsed {} file IDs for {}: {}", fileIds.size(), retailerName, fileIds);
        return fileIds;
    }

    /**
     * Get all allowed Walmart file IDs
     */
    public Set<String> getAllowedWalmartFileIds() {
        return Collections.unmodifiableSet(allowedWalmartFileIds);
    }

    /**
     * Get all allowed Target file IDs
     */
    public Set<String> getAllowedTargetFileIds() {
        return Collections.unmodifiableSet(allowedTargetFileIds);
    }

    /**
     * Check if a file ID is allowed for Walmart
     */
    public boolean isWalmartFileIdAllowed(String fileId) {
        return fileId != null && allowedWalmartFileIds.contains(fileId.trim());
    }

    /**
     * Check if a file ID is allowed for Target
     */
    public boolean isTargetFileIdAllowed(String fileId) {
        return fileId != null && allowedTargetFileIds.contains(fileId.trim());
    }

    /**
     * Get retailer type from S3 key
     */
    public String getRetailerType(String s3Key) {
        if (isWalmartFile(s3Key)) return "walmart";
        if (isTargetFile(s3Key)) return "target";
        return "other";
    }
}
