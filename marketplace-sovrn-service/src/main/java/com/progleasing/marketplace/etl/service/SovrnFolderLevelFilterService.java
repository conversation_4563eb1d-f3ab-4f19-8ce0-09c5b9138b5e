package com.progleasing.marketplace.etl.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service for folder-level filtering
 * Used for retailers where we filter entire folders (like Wayfair)
 * 
 * Example: 
 * - wayfair_north_america_48541/ -> SKIP (folder not allowed)
 * - wayfair_north_america_11480/ -> PROCESS (folder allowed)
 */
@Service
@Slf4j
public class SovrnFolderLevelFilterService {

    @Value("${product.sovrn-products-load.s3.allowed-folder-ids.wayfair:}")
    private String wayfairAllowedFolderIds;

    private Set<String> allowedWayfairFolderIds;

    @PostConstruct
    public void init() {
        allowedWayfairFolderIds = parseCommaSeparatedIds(wayfairAllowedFolderIds, "Wayfair");
        
        log.info("Initialized Folder-Level Filter Service:");
        log.info("  Wayfair allowed folder IDs: {}", allowedWayfairFolderIds);
    }

    /**
     * Check if a Wayfair folder should be processed based on folder ID
     * 
     * @param folderPath Example: "wayfair_north_america_11480/"
     * @return true if folder should be processed, false if should be skipped
     */
    public boolean shouldProcessWayfairFolder(String folderPath) {
        if (folderPath == null || folderPath.trim().isEmpty()) {
            log.warn("Empty folder path provided for Wayfair folder filtering");
            return false;
        }

        String folderId = extractFolderIdFromPath(folderPath);
        if (folderId == null) {
            log.warn("Could not extract folder ID from Wayfair folder path: {}", folderPath);
            return false;
        }

        boolean shouldProcess = allowedWayfairFolderIds.contains(folderId);
        
        if (shouldProcess) {
            log.debug("Wayfair folder {} with ID {} is ALLOWED for processing", folderPath, folderId);
        } else {
            log.debug("Wayfair folder {} with ID {} will be SKIPPED (not in allowed list)", folderPath, folderId);
        }

        return shouldProcess;
    }

    /**
     * Check if a folder path is a Wayfair folder
     */
    public boolean isWayfairFolder(String folderPath) {
        if (folderPath == null) return false;
        return folderPath.toLowerCase().contains("wayfair");
    }

    /**
     * Extract folder ID from folder path
     * Example: "wayfair_north_america_11480/" -> "11480"
     */
    private String extractFolderIdFromPath(String folderPath) {
        if (folderPath == null || folderPath.trim().isEmpty()) {
            return null;
        }

        try {
            // Remove trailing slash if present
            String cleanPath = folderPath.endsWith("/") ? 
                folderPath.substring(0, folderPath.length() - 1) : folderPath;

            // Extract the number at the end of the folder name
            String[] parts = cleanPath.split("_");
            String lastPart = parts[parts.length - 1];

            // Validate it's a number
            if (lastPart.matches("\\d+")) {
                return lastPart;
            }

            log.warn("Last part '{}' is not a valid folder ID for path: {}", lastPart, folderPath);
            return null;

        } catch (Exception e) {
            log.error("Error extracting folder ID from path '{}': {}", folderPath, e.getMessage());
            return null;
        }
    }

    /**
     * Parse comma-separated string into a Set of folder IDs
     */
    private Set<String> parseCommaSeparatedIds(String commaSeparatedIds, String retailerName) {
        if (commaSeparatedIds == null || commaSeparatedIds.trim().isEmpty()) {
            log.warn("No folder IDs configured for retailer: {}", retailerName);
            return Collections.emptySet();
        }

        Set<String> folderIds = Arrays.stream(commaSeparatedIds.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toSet());

        log.debug("Parsed {} folder IDs for {}: {}", folderIds.size(), retailerName, folderIds);
        return folderIds;
    }

    /**
     * Get all allowed Wayfair folder IDs
     */
    public Set<String> getAllowedWayfairFolderIds() {
        return Collections.unmodifiableSet(allowedWayfairFolderIds);
    }

    /**
     * Check if a folder ID is allowed for Wayfair
     */
    public boolean isWayfairFolderIdAllowed(String folderId) {
        return folderId != null && allowedWayfairFolderIds.contains(folderId.trim());
    }
}
