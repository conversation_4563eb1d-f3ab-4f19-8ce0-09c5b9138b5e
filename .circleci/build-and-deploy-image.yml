version: 2.1

orbs:
  k8s: progfin-ondemand/k8s@2
  octopus: progfin-ondemand/octopus@2


parameters:
  project-name:
    type: string
    default: "marketplace-feeds-processor"
  deployment-platform:
    type: string
    default: "shared-eks"
  octopus-release-version-file:
    type: string
    default: ./octopus_version
  octopus-release-version-jq-key:
    type: string
    default: .version

workflows:
  java-aws-k8s-build-and-deploy:
    jobs:
      - octopus/bump-version:
          name: bump-version
          context:
            - aws-sharedservices-limited
          version-file: << pipeline.parameters.octopus-release-version-file >>
          octopus-project: "marketplace-feeds-processor"
          filters:
            branches:
              only:
                - main
      - k8s/build-push-image:
          name: k8s-build-ecr-image
          context:
            - aws-sharedservices-limited
          build-version-json-file: << pipeline.parameters.octopus-release-version-file >>
          build-version-jq-key: ".version"
          tag: "${CIRCLE_SHA1}"
          build-number: << pipeline.number >>
          repo: "marketplace-feeds-processor"
          pre-check-policies: false
          useArtifactory: false
          caching: true
          useECR: true
          dockerfile: "./Dockerfile"
          use-bump-version: true
          requires:
            - bump-version
          filters:
            branches:
              only:
                - main
      - k8s/prepare-octopus-deploy:
          name: prepare-shared-eks-deployment
          context:
            - aws-sharedservices-limited
          build-version-json-file: << pipeline.parameters.octopus-release-version-file >>
          helm-use-s3: true
          helm-s3-repo: "prog-leasing-helm-charts"
          helm-s3-chart: "platform-helm"
          helm-s3-chart-version: "2.2.5"
          helm-directory: "helm"
          deployment-platform: <<pipeline.parameters.deployment-platform>>
          repo: << pipeline.parameters.project-name >>
          deploy-to-dev: true
          use-bump-version: true
          add-bump-version-suffix: true
          multi-cluster-values-files: true
          requires:
            - k8s-build-ecr-image
          filters:
            branches:
              only:
                - main

