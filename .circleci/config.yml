##Zii CI Initiate ##

version: 2.1

setup: true

orbs:
  gradle: circleci/gradle@3.0.0
  mend: progfin-ondemand/whitesource@1.15
  fastpass: progfin-ondemand/unified-pipeline-policy@1
  continuation: circleci/continuation@1


parameters:
  project-name:
    type: string
    default: "marketplace-feeds-processor"
  sonar-scan-image:
    type: string
    default: ""
  sonar-scan-exclusions:
    type: string
    default: "tests/**"
  mend-scan-exclusions:
    type: string
    default: "**/tests/**,**/*.Tests/**,**/*.UnitTests/**"
  fail-on-security-vulnerabilities:
    type: boolean
    default: true
  deployment-platform:
    type: string
    default: "shared-eks"

jobs:
  continue-to-build-and-deploy-image:
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - continuation/continue:
          configuration_path: ./.circleci/build-and-deploy-image.yml

  gradle-build:
    docker:
      - image: eclipse-temurin:21-jdk
    steps:
      - checkout
      - run:
          name: Gradle Build
          command: chmod +x ./gradlew && ./gradlew  clean bootJar -x test

workflows:
  java-k8s-scans-and-tests:
    jobs:
      - gradle-build

      - fastpass/populate-standard-fields:
          name: fastpass-populate-standard-fields
          build-number: << pipeline.number >>
          prj-name: << pipeline.parameters.project-name >>
          context: [ aws-sharedservices ]
          requires:
            - gradle-build
          filters:
            branches:
              only:
                - main
                - revert-cyberArk
      - mend/sast-scan:
          name: mend-sast-scan
          context: [aws-sharedservices-limited, security-scans]
          branch: << pipeline.git.branch >>
          fail_build: << pipeline.parameters.fail-on-security-vulnerabilities >>
          requires: [fastpass-populate-standard-fields]


      - continue-to-build-and-deploy-image:
          name: "Continue to Image Build and Deploy"
          context: [aws-sharedservices-limited]
          requires:
            - mend-sast-scan
          filters:
            branches:
              only:
                - main
                - revert-cyberArk