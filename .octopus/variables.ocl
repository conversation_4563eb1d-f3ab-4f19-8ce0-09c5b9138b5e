
variable "ServiceName" {
    value "marketplace-feeds-processor" {
        channel = ["shared-eks-feature-branch", "shared-eks-main-branch"]
    }
}


variable "FailureAuditControls" {
    value "jira_issue_released,language_and_framework_versions" {}
}

variable "UriPrefix" {
    value "/" {}
}

variable "SystemName" {
    value "mrktplace" {}
}

variable "Namespace" {
    value "#{K8s.Shared.EnvironmentMap | toLower}-#{SystemName | toLower}" {
        channel = ["shared-eks-feature-branch", "shared-eks-main-branch"]
    }
}

variable "DATASOURCE_URL" {
    value "***************************************************************************************************************************************************************" {
        environment = ["tst"]
    }
    value "***************************************************************************************************************************************************************" {
        environment = ["stg"]
    }
    value "***************************************************************************************************************************************************************" {
            environment = ["prd"]
        }
}

variable "SERVICEACCOUNT_NAMESPACE" {
    value "test-mrktplace" {
        environment = ["tst"]
    }
    value "stage-mrktplace" {
        environment = ["stg"]
    }
    value "prod-mrktplace" {
            environment = ["prd"]
    }
}

variable "PL_SOVRN_S3_BUCKET_NAME" {
    value "pl-2t-mrktplace-sss-marketplace-data-01" {
        environment = ["tst"]
    }
    value "pl-2s-mrktplace-sss-marketplace-data-01" {
        environment = ["stg"]
    }
    value "pl-2p-mrktplace-sss-marketplace-data-01" {
            environment = ["prd"]
        }
}

variable "AWS_ARN" {
    value "arn:aws:iam::************:role/pl-2t-marketplace-sqs-sqsaccessrole-10" {
        environment = ["tst"]
    }
    value "arn:aws:iam::************:role/pl-2s-marketplace-sqs-sqsaccessrole-01" {
        environment = ["stg"]
    }
    value "arn:aws:iam::************:role/pl-2p-marketplace-sqs-sqsaccessrole-01" {
            environment = ["prd"]
        }
}

variable "DATASOURCE_USER_NAME" {
    value "svc-marketplacedb-tst" {
        environment = ["tst"]
    }
    value "svc-mrktplace-stg" {
        environment = ["stg"]
    }
    value "svc-mrktplace-prd" {
        environment = ["prd"]
    }
}


variable "PROJECT_KEY" {
    value "pl-mp-test-01" {
        environment = ["tst"]
    }
    value "pl-mp-stage-01" {
        environment = ["stg"]
    }
    value "pl-mp-prod-01" {
        environment = ["prd"]
    }
}

variable "API_URL" {
    value "https://api.us-central1.gcp.commercetools.com" {
        environment = ["tst"]
    }
    value "https://api.us-central1.gcp.commercetools.com" {
        environment = ["stg"]
    }
    value "https://api.us-central1.gcp.commercetools.com" {
        environment = ["prd"]
    }
}

variable "AUTH_URL" {
    value "https://auth.us-central1.gcp.commercetools.com" {
        environment = ["tst"]
    }
    value "https://auth.us-central1.gcp.commercetools.com" {
        environment = ["stg"]
    }
    value "https://auth.us-central1.gcp.commercetools.com" {
        environment = ["prd"]
    }
}

variable "IMPORT_API_URL" {
    value "https://import.us-central1.gcp.commercetools.com" {
        environment = ["tst"]
    }
    value "https://import.us-central1.gcp.commercetools.com" {
        environment = ["stg"]
    }
    value "https://import.us-central1.gcp.commercetools.com" {
        environment = ["prd"]
    }
}

variable "AWS_REGION" {
    value "us-west-2" {
        environment = ["tst"]
    }
    value "us-west-2" {
        environment = ["stg"]
    }
    value "us-west-2" {
        environment = ["prd"]
    }
}

variable "CJ_ETL_HOST" {
    value "https://ads.api.cj.com/query" {
        environment = ["tst"]
    }
    value "https://ads.api.cj.com/query" {
        environment = ["stg"]
    }
    value "https://ads.api.cj.com/query" {
        environment = ["prd"]
    }
}

variable "CJ_PROMOTIONAL_ID" {
    value "*********" {
        environment = ["tst"]
    }
    value "*********" {
        environment = ["stg"]
    }
    value "*********" {
        environment = ["prd"]
    }
}

variable "CJ_COMPANY_ID" {
    value "6475808" {
        environment = ["tst"]
    }
    value "6475808" {
        environment = ["stg"]
    }
    value "6475808" {
        environment = ["prd"]
    }
}

variable "LEASIBILITY_S3_BUCKET_NAME" {
    value "pl-2t-lsablty-sss-leasability-01" {
        environment = ["tst"]
    }
    value "pl-2s-lsablty-sss-leasability-01" {
        environment = ["stg"]
    }
    value "pl-2p-lsablty-sss-leasability-01" {
        environment = ["prd"]
    }
}

variable "cyberark:hostId" {
    value "host/Dev/M1_MRKTPLACEFEEDPROCESSOR_A" {
        environment = ["tst"]
    }
    value "host/QA/M2_MRKTPLACEFEEDPROCESSOR_A" {
        environment = ["stg"]
    }
    value "host/Prod/M4_MRKTPLACEFEEDPROCESSOR_A" {
        environment = ["prd"]
    }
}

variable "cyberark:safe"{
    value "M1_MRKTPLACEFEEDPROCESSOR"{
        environment = ["tst"]
    }
    value "M2_MRKTPLACEFEEDPROCESSOR"{
        environment = ["stg"]
    }
    value "M4_MRKTPLACEFEEDPROCESSOR"{
        environment = ["prd"]
    }
}

variable "cyberark:secrets:0:accountName" {
    value "PostgreSQL-svc-marketplacedb-tst" {
        environment = ["tst"]
    }
    value "PostgreSQL-svc-marketplacedb-stg" {
        environment = ["stg"]
    }
    value "PostgreSQL-svc-marketplacedb-prd" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:1:accountName" {
    value "S_GenericWebApp-CT-CTClientId" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-CT-CTClientId" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-CT-CTClientId" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:2:accountName" {
    value "S_GenericWebApp-CT-CTClientSecret" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-CT-CTClientSecret" {
        environment = ["stg"]
    }
    value "S_GenericWebApp-CT-CTClientSecret" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:3:accountName" {
    value "S_GenericWebApp-ETL-AWSETLAccessKey" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-AWSETLAccessKey" {
        environment = ["stg"]
    }
    value "S_GenericWebApp-ETL-AWSETLAccessKey" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:4:accountName" {
    value "S_GenericWebApp-ETL-AWSETLSecretKey" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-AWSETLSecretKey" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-AWSETLSecretKey" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:5:accountName" {
    value "S_GenericWebApp-ETL-AWSETLPartnerTag" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-AWSETLPartnerTag" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-AWSETLPartnerTag" {
        environment = ["prd"]
    }
}


variable "cyberark:secrets:6:accountName" {
    value "S_GenericWebApp-ETL-ImpactUserName" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-ImpactUserName" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-ImpactUserName" {
        environment = ["prd"]
    }
}


variable "cyberark:secrets:7:accountName" {
    value "S_GenericWebApp-ETL-ImpactPassword" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-ImpactPassword" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-ImpactPassword" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:8:accountName" {
    value "S_GenericWebApp-ETL-ImpactAccountSID" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-ImpactAccountSID" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-ImpactAccountSID" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:9:accountName" {
    value "S_GenericWebApp-ETL-CJETLToken" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-CJETLToken" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-CJETLToken" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:10:accountName" {
    value "S_GenericWebApp-ETL-SerpAPIKey" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-SerpAPIKey" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-SerpAPIKey" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:11:accountName" {
    value "S_GenericWebApp-ETL-SovrnAccessKey" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-SovrnAccessKey" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-SovrnAccessKey" {
        environment = ["prd"]
    }
}

variable "cyberark:secrets:12:accountName" {
    value "S_GenericWebApp-ETL-SovrnSecretKey" {
        environment = ["tst"]
    }
    value "S_GenericWebApp-ETL-SovrnSecretKey" {
        environment = ["stg"]
    }

    value "S_GenericWebApp-ETL-SovrnSecretKey" {
        environment = ["prd"]
    }
}


