
step "commit-to-shared-gitops-repo" {
    name = "Commit to shared gitops repo"
    action {
        channels = ["shared-eks-feature-branch", "shared-eks-main-branch"]
        properties = {
            Gitops.PackageName = "#{Octopus.Project.Name}"
            Octopus.Action.Template.Id = "ActionTemplates-1483"
            Octopus.Action.Template.Version = "5"
        }
        worker_pool = "k8s-nonprod-workers"
    }
}


step "validate-shared-deployment" {
    name = "Validate shared deployment"
    action {
        channels = ["shared-eks-feature-branch", "shared-eks-main-branch"]
        properties = {
            Gitops.PackageName = "#{Octopus.Project.Name}"
            Octopus.Action.Template.Id = "ActionTemplates-1523"
            Octopus.Action.Template.Version = "2"
        }
        worker_pool_variable = "K8s.Hub.WorkerPool"
    }
}