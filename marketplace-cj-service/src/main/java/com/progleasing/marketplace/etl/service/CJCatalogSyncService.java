package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.entity.RetailerPartnerData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import com.progleasing.marketplace.etl.repositories.RetailerPartnerRepository;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class CJCatalogSyncService {


    private ObjectMapper objectMapper;
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private RetailerPartnerRepository retailerPartnerRepository;
    private CjProductService cjProductService;

    public void syncCatalogItems(String messagePayload, String messageId) throws Exception {
        ETLMessageDTO messageDTO = objectMapper.readValue(messagePayload, ETLMessageDTO.class);
        log.info("Processing CJ Query for l1 category {} l2 category {} searchTerm {} retailerName {}", messageDTO.getL1CategoryName(), messageDTO.getL2CategoryName(), messageDTO.getSearchTerm(), messageDTO.getRetailerName());
        EtlQueueStatusData queueStatusData = updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.STARTED);
        try{
            List<String> retailers = Arrays.asList(messageDTO.getRetailerName().split("\\s*,\\s*", -1));
            Map<String, String> partnerIdToRetailerName = mapRetailerNamesToPartnerIdMap(retailers);
            List<String> partnerIds = new ArrayList<>(partnerIdToRetailerName.keySet());
            Float[] priceRange = validateAndExtractPriceRange(messageDTO.getMinPrice(), messageDTO.getMaxPrice());
            Float minPrice = priceRange[0];
            Float maxPrice = priceRange[1];
            cjProductService.fetchProductsAndPersistData(messageDTO.getSearchTerm(),minPrice,maxPrice,partnerIds, messageDTO.getCategoryKey(), messageDTO.getL1CategoryName(), messageDTO.getL2CategoryName(),queueStatusData, partnerIdToRetailerName);
            updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.COMPLETED);
        }catch (Exception e) {
            log.error("Catalog item sync failed for category ID {}: {}", "messageDTO.getCategory()", e.getMessage(), e);
            updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING);
            throw e;
        }
    }

    private Float[] validateAndExtractPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        if (minPrice != null && maxPrice != null && minPrice.compareTo(maxPrice) >= 0) {
            log.warn("Invalid price range ignored: minPrice {} >= maxPrice {}", minPrice, maxPrice);
            return new Float[]{null, null};
        }

        Float min = minPrice != null ? minPrice.floatValue() : null;
        Float max = maxPrice != null ? maxPrice.floatValue() : null;

        return new Float[]{min, max};
    }

    private Map<String, String> mapRetailerNamesToPartnerIdMap(List<String> retailerNames) {
        List<RetailerPartnerData> retailerDataList = retailerPartnerRepository.findByRetailerNameIn(retailerNames);

        Map<String, String> partnerIdToRetailerName = retailerDataList.stream()
                .collect(Collectors.toMap(RetailerPartnerData::getPartnerId, RetailerPartnerData::getRetailerName));

        for (String retailerName : retailerNames) {
            boolean found = retailerDataList.stream().anyMatch(r -> r.getRetailerName().equals(retailerName));
            if (!found) {
                log.warn("No partner ID found for retailer: {}", retailerName);
            }
        }

        return partnerIdToRetailerName;
    }

    @Transactional
    private EtlQueueStatusData updateStatusQueue(ETLMessageDTO messageDTO, String messageId, EtlQueueStatusData.Status status) {
        try {
            String messageData = String.format(
                    "%s|%s|%s|%s|%s|%s|%s|%s|%s",
                    messageId,
                    messageDTO.getSearchTerm(),
                    messageDTO.getCategoryKey(),
                    messageDTO.getRetailerName(),
                    messageDTO.getRetailerKey(),
                    messageDTO.getMinPrice(),
                    messageDTO.getMaxPrice(),
                    messageDTO.getL1CategoryName(),
                    messageDTO.getL2CategoryName()
            );
            EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_SEARCH;
            Optional<EtlQueueStatusData> existing =
                    etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(
                            messageDTO.getReferenceId(),
                            ProductData.FeedSource.cj.name(),
                            etlStep,
                            messageId
                    );
            EtlQueueStatusData etlQueueStatusData;
            if (existing.isPresent()) {
                etlQueueStatusData = existing.get();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
            } else {
                etlQueueStatusData = new EtlQueueStatusData();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setEtlStep(etlStep);
                etlQueueStatusData.setEtlID(messageDTO.getReferenceId());
                etlQueueStatusData.setMessageId(messageId);
                etlQueueStatusData.setSqsMessageData(messageData);
                etlQueueStatusData.setRetailerSearch(ProductData.FeedSource.cj.name());
                if (status == EtlQueueStatusData.Status.STARTED) {
                    etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
                }
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
            }
            etlQueueStatusDataRepository.save(etlQueueStatusData);
            etlQueueStatusDataRepository.flush();
            log.info("Queue status saved successfully for message ID {} import id {} to {}", messageId,messageDTO.getReferenceId(),status);
            return etlQueueStatusData;
        }catch (Exception e) {
            log.error("Error updating queue status for {} referenceId {}",messageId,messageDTO.getReferenceId());
            throw e;
        }
    }

}
