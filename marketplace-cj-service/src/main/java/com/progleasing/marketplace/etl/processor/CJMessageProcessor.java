package com.progleasing.marketplace.etl.processor;


import com.progleasing.marketplace.etl.service.CJCatalogSyncService;
import io.awspring.cloud.sqs.annotation.SqsListener;
import io.awspring.cloud.sqs.annotation.SqsListenerAcknowledgementMode;
import io.awspring.cloud.sqs.listener.acknowledgement.Acknowledgement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

@Service
@Slf4j
@ConditionalOnProperty(value = "aws.sqs.cj.listener.enabled", havingValue = "true", matchIfMissing = true)
public class CJMessageProcessor {

    private final CJCatalogSyncService cjCatalogSyncService;
    private final Executor executor;

    @Value("${aws.sqs.concurrent-message:10}")
    private Integer maxConcurrentMessages;

    public CJMessageProcessor(CJCatalogSyncService CJCatalogSyncService ,
                              @Qualifier("cjTaskExecutor") Executor executor) {
        this.executor = executor;
        this.cjCatalogSyncService = CJCatalogSyncService;
    }

    @SqsListener(value = "${aws.sqs.cj.queue-name}",
            maxConcurrentMessages = "${aws.sqs.concurrent-message}", acknowledgementMode = SqsListenerAcknowledgementMode.MANUAL
    )
    public void handle(List<Message<String>> messages) {
        log.info("Messages received: {}", messages.size());

        List<CompletableFuture<Void>> futures = messages.stream().map(message ->
                CompletableFuture.runAsync(() -> {
                    try {
                        UUID messageId = (UUID) message.getHeaders().get("id");
                        String payload = message.getPayload();
                        log.info("Received message with payload: {} and id {}", payload, messageId.toString());
                        cjCatalogSyncService.syncCatalogItems(payload, messageId.toString());
                        Acknowledgement.acknowledge(message);
                    } catch (Exception e) {
                        log.error("Error processing message", e);
                    }
                }, executor)
        ).toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

}
