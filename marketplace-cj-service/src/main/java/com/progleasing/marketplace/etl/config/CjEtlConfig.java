package com.progleasing.marketplace.etl.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "cj.etl")
public class CjEtlConfig {

    private String host;
    private String authToken;
    private String companyId;
    private String promotionalId;
    private Search search;
    private int maxProductsToImport;
    private boolean deleteProducts;
    @Data
    public static class Search {
        private List<String> advertiserCountries;
        private Integer limit;
        private Boolean includeDeletedProducts;
        private String currency;
    }
    private int gracePeriodDays;
}
