package com.progleasing.marketplace.etl.utils;

import com.progleasing.marketplace.etl.config.CjEtlConfig;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CjSearchParamsBuilder {

    private final CjEtlConfig config;

    public CjSearchParamsBuilder(CjEtlConfig config) {
        this.config = config;
    }

    public Map<String, Object> build(
            List<String> partnerIds,
            List<String> keywords,
            Float lowPrice,
            Float highPrice
    ) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("companyId", config.getCompanyId());
        variables.put("partnerIds", partnerIds);
        variables.put("promotionalId", config.getPromotionalId());
        variables.put("advertiserCountries", config.getSearch().getAdvertiserCountries());
        variables.put("limit", config.getSearch().getLimit());
        variables.put("includeDeletedProducts", config.getSearch().getIncludeDeletedProducts());
        variables.put("currency", config.getSearch().getCurrency());
        variables.put("lowPrice", lowPrice);
        variables.put("highPrice", highPrice);
        variables.put("keywords", keywords);

        return variables;
    }
}
