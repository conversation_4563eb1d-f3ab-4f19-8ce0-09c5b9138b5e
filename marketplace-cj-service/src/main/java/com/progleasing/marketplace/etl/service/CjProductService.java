package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.CjEtlConfig;
import com.progleasing.marketplace.etl.utils.CjSearchParamsBuilder;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.response.CjProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import com.progleasing.marketplace.etl.response.CjProductResponse;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CjProductService {

    @Autowired
    private CjSearchParamsBuilder cjSearchParamsBuilder;

    @Autowired
    private CjEtlConfig cjEtlConfig;

    private static final String GRAPHQL_QUERY = """
query (
    $companyId: ID!,
    $partnerIds: [ID!],
    $promotionalId: ID!,
    $advertiserCountries: [String!],
    $limit: Int,
    $includeDeletedProducts: Boolean!,
    $currency: String,
    $lowPrice: Float,
    $highPrice: Float,
    $keywords: [String!]
) {
    products(
        companyId: $companyId,
        partnerIds: $partnerIds,
        advertiserCountries: $advertiserCountries,
        limit: $limit,
        includeDeletedProducts: $includeDeletedProducts,
        currency: $currency,
        lowPrice: $lowPrice,
        highPrice: $highPrice,
        keywords: $keywords
    ) {
        count
        limit
        totalCount
        resultList {
            additionalImageLink
            advertiserName
            advertiserId
            id
            title
            brand
            description
            price {
                amount
            }
            salePrice {
                amount
            }
            mobileLink
            link
            linkCode(pid: $promotionalId) {
                clickUrl
            }
            imageLink
        }
    }
}
""";

    private final RestTemplate cjRestTemplate;
    public CjProductService(ObjectMapper objectMapper) {
        this.cjRestTemplate = createCustomRestTemplate(objectMapper);
    }

    @Autowired
    private ProductPersistenceService productPersistenceService;

    private RestTemplate createCustomRestTemplate(ObjectMapper objectMapper) {
        RestTemplate restTemplate = new RestTemplate();

        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        List<MediaType> mediaTypes = new ArrayList<>(jsonConverter.getSupportedMediaTypes());
        mediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        jsonConverter.setSupportedMediaTypes(mediaTypes);

        restTemplate.getMessageConverters().addFirst(jsonConverter);
        return restTemplate;
    }

    public void fetchProductsAndPersistData(String queryTerm, Float minPrice, Float maxPrice, List<String> partnerIds,
                                            String categoryKey, String l1Category, String l2Category,
                                            EtlQueueStatusData queueStatusData,Map<String, String> partnerIdToRetailerName) {
        CjProductResponse response = fetchProducts(queryTerm, minPrice, maxPrice, partnerIds);
        if (response != null &&
                response.getData() != null &&
                response.getData().getProducts() != null &&
                response.getData().getProducts().getResultList() != null) {
            log.info("Fetched {} products from api.", response.getData().getProducts().getResultList().size());
            Map<String, List<Map<String, Object>>> groupedProducts = response.getData().getProducts().getResultList().stream()
                    .filter(product -> product.getAdvertiserId() != null && partnerIds.contains(product.getAdvertiserId()))
                    .collect(Collectors.groupingBy(
                            CjProduct::getAdvertiserId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> list.stream()
                                            .limit(cjEtlConfig.getMaxProductsToImport())
                                            .map(product -> {
                                                Map<String, Object> map = new HashMap<>();
                                                String productTrackingUrl = product.getLinkCode() != null && product.getLinkCode().getClickUrl() != null
                                                        ? product.getLinkCode().getClickUrl()
                                                        : "https://www.overstock.com";

                                                String advertiserId = product.getAdvertiserId();
                                                String retailerName = partnerIdToRetailerName.getOrDefault(advertiserId, advertiserId);
                                                List<String> externalImages = new ArrayList<>();
                                                if (product.getImageLink() != null) {
                                                    externalImages.add(product.getImageLink());
                                                }
                                                if (product.getAdditionalImageLink() != null && product.getAdditionalImageLink().length > 0) {
                                                    externalImages.addAll(List.of(product.getAdditionalImageLink()));
                                                }

                                                map.put("product_id", product.getId());
                                                map.put("title", product.getTitle());
                                                map.put("description", product.getTitle());
                                                map.put("long_description", product.getDescription());
                                                map.put("brand", product.getBrand());
                                                map.put("price", product.getPrice() != null ? product.getPrice().getAmount() : null);
                                                map.put("sale_price", product.getSalePrice() != null ? product.getSalePrice().getAmount() : null);
                                                map.put("mobile_link", product.getMobileLink());
                                                map.put("product_link", productTrackingUrl);
                                                map.put("link", product.getLink());
                                                map.put("source", retailerName);
                                                map.put("thumbnails", externalImages);
                                                map.put("rating", null);
                                                map.put("reviews", null);
                                                return map;
                                            })
                                            .toList()
                            )
                    ));

            List<Map<String, Object>> finalProductList = groupedProducts.values().stream()
                    .flatMap(List::stream)
                    .toList();
            productPersistenceService.saveProducts(finalProductList, queryTerm, categoryKey, l1Category, l2Category, queueStatusData);
        }

    }

    public CjProductResponse fetchProducts(String queryTerm, Float minPrice, Float maxPrice, List<String> partnerIds) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(cjEtlConfig.getAuthToken());

        Map<String, Object> variables = cjSearchParamsBuilder.build(
                partnerIds,
                List.of(queryTerm),
                minPrice,
                maxPrice
        );

        Map<String, Object> payload = new HashMap<>();
        payload.put("query", GRAPHQL_QUERY);
        payload.put("variables", variables);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(payload, headers);

        try {
            ResponseEntity<CjProductResponse> response = cjRestTemplate.exchange(
                    cjEtlConfig.getHost(),
                    HttpMethod.POST,
                    request,
                    CjProductResponse.class
            );
            return response.getBody();
        } catch (HttpStatusCodeException ex) {
            log.error("CJ API request failed: {} - {}", ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new RuntimeException(String.format("CJ API request failed: %s - %s", ex.getStatusCode(), ex.getResponseBodyAsString()));
        } catch (Exception e) {
            throw new RuntimeException("Unexpected error during CJ API request", e);
        }
    }
}
