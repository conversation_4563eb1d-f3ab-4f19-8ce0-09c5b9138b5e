package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.entity.RetailerPartnerData;
import com.progleasing.marketplace.etl.repositories.EtlQueueStatusDataRepository;
import com.progleasing.marketplace.etl.repositories.RetailerPartnerRepository;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CJCatalogSyncServiceTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;

    @Mock
    private RetailerPartnerRepository retailerPartnerRepository;

    @Mock
    private CjProductService cjProductService;

    @InjectMocks
    private CJCatalogSyncService cjCatalogSyncService;

    private final String messageId = "test-message-id";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void syncCatalogItems_shouldCompleteSuccessfully() throws Exception {
        String payload = "{\"category\":\"electronics\",\"searchTerm\":\"laptop\",\"retailerName\":\"Target\",\"retailerKey\":\"target\",\"minPrice\":100,\"maxPrice\":1000,\"referenceId\":\"ref-123\",\"l1CategoryName\":\"Computers\",\"l2CategoryName\":\"Laptops\"}";

        ETLMessageDTO messageDTO = new ETLMessageDTO();
        messageDTO.setCategoryKey("electronics");
        messageDTO.setSearchTerm("laptop");
        messageDTO.setRetailerName("Target");
        messageDTO.setRetailerKey("target");
        messageDTO.setMinPrice(new BigDecimal(100));
        messageDTO.setMaxPrice(new BigDecimal(1000));
        messageDTO.setReferenceId("ref-123");
        messageDTO.setL1CategoryName("Computers");
        messageDTO.setL2CategoryName("Laptops");

        RetailerPartnerData retailer = new RetailerPartnerData();
        retailer.setRetailerName("Target");
        retailer.setPartnerId("1234");

        when(objectMapper.readValue(payload, ETLMessageDTO.class)).thenReturn(messageDTO);
        when(retailerPartnerRepository.findByRetailerNameIn(List.of("Target"))).thenReturn(List.of(retailer));
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(any(), any(), any(),any()))
                .thenReturn(Optional.empty());

        cjCatalogSyncService.syncCatalogItems(payload, messageId);

        verify(cjProductService, times(1)).fetchProductsAndPersistData(
                eq("laptop"),
                eq(100f),
                eq(1000f),
                eq(List.of("1234")),
                eq("electronics"),
                eq("Computers"),
                eq("Laptops"),
                any(EtlQueueStatusData.class),
                anyMap()
        );
        verify(etlQueueStatusDataRepository, times(2)).save(any());
    }

    @Test
    void syncCatalogItems_shouldHandleException() throws Exception {
        String payload = "{\"category\":\"electronics\",\"searchTerm\":\"laptop\",\"retailerName\":\"Target\",\"retailerKey\":\"target\",\"minPrice\":100,\"maxPrice\":1000,\"referenceId\":\"ref-123\",\"l1CategoryName\":\"Computers\",\"l2CategoryName\":\"Laptops\"}";

        ETLMessageDTO messageDTO = new ETLMessageDTO();
        messageDTO.setCategoryKey("electronics");
        messageDTO.setSearchTerm("laptop");
        messageDTO.setRetailerName("Target");
        messageDTO.setRetailerKey("target");
        messageDTO.setMinPrice(new BigDecimal(100));
        messageDTO.setMaxPrice(new BigDecimal(1000));
        messageDTO.setReferenceId("ref-123");
        messageDTO.setL1CategoryName("Computers");
        messageDTO.setL2CategoryName("Laptops");

        RetailerPartnerData retailer = new RetailerPartnerData();
        retailer.setRetailerName("Target");
        retailer.setPartnerId("1234");

        when(objectMapper.readValue(payload, ETLMessageDTO.class)).thenReturn(messageDTO);
        when(retailerPartnerRepository.findByRetailerNameIn(List.of("Target"))).thenReturn(List.of(retailer));
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(any(), any(), any(),any()))
                .thenReturn(Optional.empty());

        doThrow(new RuntimeException("Simulated error")).when(cjProductService)
                .fetchProductsAndPersistData(any(), any(), any(), any(), any(), any(), any(), any(), any());

        assertThrows(RuntimeException.class, () -> cjCatalogSyncService.syncCatalogItems(payload, messageId));

        verify(etlQueueStatusDataRepository, times(2)).save(any());
    }

    @Test
    void syncCatalogItems_invalidPriceRange_shouldIgnoreAndPassNullsToApiService() throws Exception {
        String messagePayload = "{\"searchTerm\":\"laptop\",\"category\":\"electronics\",\"retailerName\":\"Target\",\"referenceId\":\"ref001\",\"retailerKey\":\"target\",\"minPrice\":500,\"maxPrice\":400,\"l1CategoryName\":\"Computers\",\"l2CategoryName\":\"Laptops\"}";
        String messageId = "msg-003";

        ETLMessageDTO dto = new ETLMessageDTO();
        dto.setSearchTerm("laptop");
        dto.setCategoryKey("electronics");
        dto.setRetailerName("Target");
        dto.setReferenceId("ref001");
        dto.setRetailerKey("target");
        dto.setMinPrice(new BigDecimal(500));
        dto.setMaxPrice(new BigDecimal(400));
        dto.setL1CategoryName("Computers");
        dto.setL2CategoryName("Laptops");

        RetailerPartnerData retailer = new RetailerPartnerData();
        retailer.setRetailerName("Target");
        retailer.setPartnerId("1234");

        when(objectMapper.readValue(eq(messagePayload), eq(ETLMessageDTO.class))).thenReturn(dto);
        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(anyString(), anyString(), any(),any()))
                .thenReturn(Optional.empty());
        when(etlQueueStatusDataRepository.save(any(EtlQueueStatusData.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(retailerPartnerRepository.findByRetailerNameIn(List.of("Target"))).thenReturn(List.of(retailer));

        cjCatalogSyncService.syncCatalogItems(messagePayload, messageId);

        verify(cjProductService).fetchProductsAndPersistData(
                eq(dto.getSearchTerm()),
                isNull(),
                isNull(),
                eq(List.of("1234")),
                eq(dto.getCategoryKey()),
                eq(dto.getL1CategoryName()),
                eq(dto.getL2CategoryName()),
                any(EtlQueueStatusData.class),
                anyMap()
        );
    }

}
