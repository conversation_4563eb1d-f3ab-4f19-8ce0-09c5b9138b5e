package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.entity.AdditionalProductData;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.entity.ImageData;
import com.progleasing.marketplace.etl.entity.ProductData;
import com.progleasing.marketplace.etl.repositories.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.StreamSupport;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static java.util.stream.StreamSupport.stream;

@ExtendWith(MockitoExtension.class)
class ProductPersistenceServiceTest {

    @Mock private ProductDataRepository productRepo;
    @Mock private PriceDataRepository priceRepo;
    @Mock private ImageDataRepository imageRepo;
    @Mock private EtlQueueStatusDataRepository statusRepo;

    @Mock
    private AdditionalProductDataRepository additionalProductDataRepo;

    @InjectMocks
    private ProductPersistenceService service;

    private EtlQueueStatusData status;

    @Mock
    private ProductDataDisabledRepository productDataDisabledRepository;

    @BeforeEach
    void setup() {
        status = new EtlQueueStatusData();
        status.setEtlID("etl_001");
        status.setStatus(EtlQueueStatusData.Status.STARTED);
    }

    @Test
    void testSaveProducts_savesNewData() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Test Product");
        product.put("source", "RetailerX");
        product.put("extracted_price", "99.99");
        product.put("thumbnail", "http://image.com/img.jpg");

        List<Map<String, Object>> products = List.of(product);

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(), any(), any())).thenReturn(List.of());

        service.saveProducts(products, "laptop", "electronics", "l1Cat", "l2Cat", status);

        verify(productRepo).saveAll(argThat(it ->
                stream(it.spliterator(), false).count() == 1));
        verify(priceRepo).saveAll(argThat(it ->
                stream(it.spliterator(), false).count() == 1));
        verify(imageRepo).saveAll(argThat(it ->
                stream(it.spliterator(), false).count() == 1));
        verify(statusRepo).save(status);
        assertEquals(EtlQueueStatusData.Status.COMPLETED, status.getStatus());
    }

    @Test
    void testSaveProducts_skipsUnchangedProduct() {
        ProductData existing = createExistingProduct();

        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Same Product");
        product.put("source", "RetailerX");

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of(existing));
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(),any(),any())).thenReturn(List.of());

        service.saveProducts(List.of(product), "search", "category", "l1Cat", "l2Cat", status);

        verify(productRepo, never()).saveAll(any());
    }

    @Test
    void testSaveProducts_handlesInvalidPriceGracefully() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Invalid Price Product");
        product.put("source", "RetailerX");
        product.put("extracted_price", "not_a_price");

        when(productRepo.findAllById(any())).thenReturn(List.of());
        when(priceRepo.findAllById(any())).thenReturn(List.of());
        when(imageRepo.findAllById(any())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(),any(),any(),any())).thenReturn(List.of());

        assertDoesNotThrow(() ->
                service.saveProducts(List.of(product), "search", "category", "l1Cat", "l2Cat", status)
        );
    }

    @Test
    void testSaveProducts_throwsExceptionOnFailure() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Test Product");
        product.put("source", "RetailerX");

        when(productRepo.findAllById(any())).thenThrow(new RuntimeException("DB error"));

        RuntimeException ex = assertThrows(RuntimeException.class, () ->
                service.saveProducts(List.of(product), "search", "cat", "l1Cat", "l2Cat", status)
        );

        assertTrue(ex.getMessage().contains("Failed to save products"));
    }

    @Test
    void testSaveProducts_savesAdditionalInfo() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Product with additional info");
        product.put("source", "RetailerX");
        product.put("mobile_link", "some mobile link");
        product.put("link", "some link");

        List<Map<String, Object>> products = List.of(product);

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(additionalProductDataRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(),any(),any())).thenReturn(List.of());

        service.saveProducts(products, "search", "category", "l1Cat", "l2Cat", status);

        verify(additionalProductDataRepo).saveAll(argThat(iterable -> {
            List<AdditionalProductData> list = StreamSupport.stream(iterable.spliterator(), false)
                    .toList();
            return list.size() == 1 &&
                    list.getFirst().getId().equals("p1") &&
                    list.getFirst().getOtherInfo() != null && !list.getFirst().getOtherInfo().isBlank();
        }));
        verify(statusRepo).save(status);
        assertEquals(EtlQueueStatusData.Status.COMPLETED, status.getStatus());
    }

    @Test
    void testSaveProducts_handlesMultipleImages() {
        Map<String, Object> product = new HashMap<>();
        product.put("product_id", "p1");
        product.put("title", "Product with Multiple Images");
        product.put("source", "RetailerX");
        product.put("extracted_price", "79.99");
        product.put("thumbnails", List.of("http://image.com/img1.jpg", "http://image.com/img2.jpg", "http://image.com/img3.jpg"));

        List<Map<String, Object>> products = List.of(product);

        when(productRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of("p1"))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(),any(),any(),any())).thenReturn(List.of());

        service.saveProducts(products, "laptop", "electronics", "l1Cat", "l2Cat", status);

        verify(imageRepo).saveAll(argThat(images ->
                stream(images.spliterator(), false)
                        .filter(img -> "p1".equals(img.getId()))
                        .count() == 3
        ));

        assertEquals(EtlQueueStatusData.Status.COMPLETED, status.getStatus());
    }

    @Test
    void testSaveProducts_skipsExistingImage() {
        String productId = "p1";
        String imageUrl = "http://image.com/img1.jpg";

        Map<String, Object> product = new HashMap<>();
        product.put("product_id", productId);
        product.put("title", "Test Product");
        product.put("source", "RetailerX");
        product.put("thumbnails", List.of(imageUrl));

        List<Map<String, Object>> products = List.of(product);

        ImageData existingImage = new ImageData();
        existingImage.setId(productId);
        existingImage.setImageUrl(imageUrl);
        existingImage.setLastModified(new java.sql.Timestamp(System.currentTimeMillis()));

        when(productRepo.findAllById(List.of(productId))).thenReturn(List.of());
        when(priceRepo.findAllById(List.of(productId))).thenReturn(List.of());
        when(imageRepo.findAllById(anyIterable())).thenReturn(List.of(existingImage));
        when(additionalProductDataRepo.findAllById(List.of(productId))).thenReturn(List.of());
        when(productDataDisabledRepository.findProductsByFeedSource(any(), any(),any(),any())).thenReturn(List.of());

        service.saveProducts(products, "search", "category", "l1Cat", "l2Cat", status);
        
        verify(imageRepo, never()).saveAll(any());
    }


    private ProductData createExistingProduct() {
        ProductData product = new ProductData();
        product.setId("p1");
        product.setName("Same Product");
        product.setDescription("Same Product");
        product.setLongDescription("");
        product.setRetailerName("RetailerX");
        product.setRetailerKey("retailerx");
        product.setBrand("");
        product.setRatingAverage(BigDecimal.ZERO);
        product.setRatingCount(0);
        product.setAffiliateAddToCartUrl("");
        product.setProductTrackingUrl("");
        product.setSearchTerm("search");
        product.setCategoryKey("category");
        product.setL1CategoryName("l1Cat");
        product.setL2CategoryName("l2Cat");
        product.setFeedSource(ProductData.FeedSource.cj);
        return product;
    }
}
