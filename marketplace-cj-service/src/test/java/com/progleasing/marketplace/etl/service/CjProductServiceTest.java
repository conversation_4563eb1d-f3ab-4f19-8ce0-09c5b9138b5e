package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.CjEtlConfig;
import com.progleasing.marketplace.etl.response.*;
import com.progleasing.marketplace.etl.utils.CjSearchParamsBuilder;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CjProductServiceTest {

    @Mock private CjSearchParamsBuilder cjSearchParamsBuilder;
    @Mock private CjEtlConfig cjEtlConfig;
    @Mock private ProductPersistenceService productPersistenceService;
    @Mock private ObjectMapper objectMapper;
    @Mock private RestTemplate cjRestTemplate;

    private CjProductService service;

    @BeforeEach
    void setUp() {
        service = new CjProductService(objectMapper);
        ReflectionTestUtils.setField(service, "cjSearchParamsBuilder", cjSearchParamsBuilder);
        ReflectionTestUtils.setField(service, "cjEtlConfig", cjEtlConfig);
        ReflectionTestUtils.setField(service, "productPersistenceService", productPersistenceService);
        ReflectionTestUtils.setField(service, "cjRestTemplate", cjRestTemplate);
    }

    @Test
    void fetchProductsAndPersistData_shouldSaveProducts() {
        String queryTerm = "test";
        Float minPrice = 10f;
        Float maxPrice = 100f;
        List<String> partnerIds = List.of("partner1");
        String categoryKey = "category";
        String l1Category = "Electronics";
        String l2Category = "Laptops";
        EtlQueueStatusData status = new EtlQueueStatusData();
        Map<String, String> partnerIdToRetailerName = Map.of("partner1", "RetailerX");

        when(cjSearchParamsBuilder.build(anyList(), anyList(), any(), any())).thenReturn(Map.of());
        when(cjEtlConfig.getMaxProductsToImport()).thenReturn(10);
        when(cjEtlConfig.getHost()).thenReturn("https://dummy-host.com");
        when(cjEtlConfig.getAuthToken()).thenReturn("dummy-token");

        CjProductResponse mockResponse = createMockProductResponse();
        ResponseEntity<CjProductResponse> responseEntity = new ResponseEntity<>(mockResponse, HttpStatus.OK);

        when(cjRestTemplate.exchange(
                eq("https://dummy-host.com"), eq(HttpMethod.POST), any(HttpEntity.class), eq(CjProductResponse.class)))
                .thenReturn(responseEntity);

        service.fetchProductsAndPersistData(
                queryTerm, minPrice, maxPrice, partnerIds, categoryKey,
                l1Category, l2Category, status, partnerIdToRetailerName
        );

        verify(productPersistenceService, atLeastOnce()).saveProducts(
                anyList(),
                eq(queryTerm),
                eq(categoryKey),
                eq(l1Category),
                eq(l2Category),
                eq(status)
        );
    }

    @Test
    void fetchProducts_shouldReturnResponse() {
        when(cjSearchParamsBuilder.build(anyList(), anyList(), any(), any())).thenReturn(Map.of());
        when(cjEtlConfig.getAuthToken()).thenReturn("fake-token");
        when(cjEtlConfig.getHost()).thenReturn("http://fakehost/graphql");

        CjProductResponse expectedResponse = new CjProductResponse();
        ResponseEntity<CjProductResponse> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);

        when(cjRestTemplate.exchange(
                anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(CjProductResponse.class)))
                .thenReturn(responseEntity);

        CjProductResponse actualResponse = service.fetchProducts("query", 1f, 2f, List.of("partner"));

        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    void fetchProducts_shouldThrowRuntimeExceptionOnHttpError() {
        when(cjSearchParamsBuilder.build(anyList(), anyList(), any(), any())).thenReturn(Map.of());
        when(cjEtlConfig.getAuthToken()).thenReturn("fake-token");
        when(cjEtlConfig.getHost()).thenReturn("http://fakehost/graphql");

        doThrow(new HttpStatusCodeException(HttpStatus.BAD_REQUEST) {
            @Override
            public String getResponseBodyAsString() {
                return "Bad Request";
            }
        }).when(cjRestTemplate).exchange(
                anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(CjProductResponse.class));

        RuntimeException ex = assertThrows(RuntimeException.class, () ->
                service.fetchProducts("query", null, null, List.of("partner"))
        );

        assertTrue(ex.getMessage().contains("CJ API request failed"));
    }

    private CjProductResponse createMockProductResponse() {
        CjProductResponse response = new CjProductResponse();
        CjData data = new CjData();
        CjProducts products = new CjProducts();

        CjProduct product = new CjProduct();
        product.setId("product123");
        product.setTitle("Test Product");
        product.setDescription("Description here");
        product.setBrand("BrandX");

        CjPrice price = new CjPrice();
        price.setAmount(50.0);
        product.setPrice(price);

        CjLinkCode linkCode = new CjLinkCode();
        linkCode.setClickUrl("http://tracking-link.com");
        product.setLinkCode(linkCode);

        product.setAdvertiserId("partner1");
        product.setImageLink("http://image.com/image.jpg");
        product.setAdditionalImageLink(new String[]{"http://image.com/additional1.jpg"});

        products.setResultList(List.of(product));
        data.setProducts(products);
        response.setData(data);

        return response;
    }
}
