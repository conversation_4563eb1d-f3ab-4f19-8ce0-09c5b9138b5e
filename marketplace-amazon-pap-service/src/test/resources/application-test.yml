spring:
  application:
    name: progressive.leasing
  datasource:
    url: ****************************************
    username: admin
    password: admin123
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.PostgresSQLDialect
  cloud:
    aws:
      region:
        static: us-east-1
      credentials:
        access-key: awsAccessKey
        secret-key: awsSecretKey

amazon:
  etl:
    host: example.com
    region: us-east-1
    accessKey: accessKey
    secretKey: accessSecret
    partnerTag: tag
    maxProductsToImport: 10

aws:
  sqs:
    listener:
      amazon:
        enabled: true
    queue-name: amazon-retailer-test-queue
    concurrent-message: 2


