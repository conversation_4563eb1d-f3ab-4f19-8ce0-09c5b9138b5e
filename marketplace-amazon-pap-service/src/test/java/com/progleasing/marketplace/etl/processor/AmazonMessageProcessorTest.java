package com.progleasing.marketplace.etl.processor;


import com.progleasing.marketplace.etl.service.AmazonCatalogSyncService;
import io.awspring.cloud.sqs.listener.acknowledgement.AcknowledgementCallback;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static org.mockito.Mockito.*;


@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
public class AmazonMessageProcessorTest {

    @Mock
    private AmazonCatalogSyncService amazonCatalogSyncService;

    @InjectMocks
    private AmazonMessageProcessor amazonMessageProcessor;

    @Mock
    private AcknowledgementCallback acknowledgement;


    @BeforeEach
    void setUp() {
        Executor directExecutor = Runnable::run;
        amazonMessageProcessor = new AmazonMessageProcessor(amazonCatalogSyncService, directExecutor);
    }

    @Test
    void testHandleMessagesSuccessfully() throws Exception {
        Message<String> message = mock(Message.class);
        UUID messageId = UUID.randomUUID();
        String payload = "Sample Payload";
        Map<String, Object> headersMap = new HashMap<>();
        headersMap.put("id", messageId);
        headersMap.put("AcknowledgementCallback", acknowledgement);
        MessageHeaders messageHeaders = new MessageHeaders(headersMap);

        when(message.getPayload()).thenReturn(payload);
        when(message.getHeaders()).thenReturn(messageHeaders);
        when(acknowledgement.onAcknowledge(message))
                .thenReturn(CompletableFuture.completedFuture(null));

        amazonMessageProcessor.handle(Collections.singletonList(message));

        ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> idCaptor = ArgumentCaptor.forClass(String.class);
        verify(amazonCatalogSyncService, times(1)).syncCatalogItems(payloadCaptor.capture(), idCaptor.capture());
        Assertions.assertEquals(payload, payloadCaptor.getValue());
    }

    @Test
    void testHandleMessageWithException() throws Exception {
        Message<String> message = mock(Message.class);
        UUID messageId = UUID.randomUUID();
        String payload = "Sample Payload";
        Map<String, Object> headersMap = new HashMap<>();
        headersMap.put("id", messageId);
        headersMap.put("AcknowledgementCallback", acknowledgement);
        MessageHeaders messageHeaders = new MessageHeaders(headersMap);
        when(message.getPayload()).thenReturn(payload);
        when(message.getHeaders()).thenReturn(messageHeaders);
        doThrow(new RuntimeException("Test Exception")).when(amazonCatalogSyncService).syncCatalogItems(any(), any());
        amazonMessageProcessor.handle(Collections.singletonList(message));
        ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> idCaptor = ArgumentCaptor.forClass(String.class);
        verify(amazonCatalogSyncService, times(1)).syncCatalogItems(payloadCaptor.capture(), idCaptor.capture());
        verify(acknowledgement, never()).onAcknowledge(message);
    }

}
