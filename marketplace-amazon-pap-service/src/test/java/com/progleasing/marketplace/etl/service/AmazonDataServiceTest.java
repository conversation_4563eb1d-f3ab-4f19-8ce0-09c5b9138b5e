package com.progleasing.marketplace.etl.service;


import com.progleasing.marketplace.etl.config.AWSRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.EtlQueueStatusData;
import com.progleasing.marketplace.etl.repositories.*;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AmazonDataServiceTest {

    @InjectMocks
    private AmazonDataService amazonDataService;

    @Mock
    private ProductDataRepository productDataRepository;
    @Mock private PriceDataRepository priceDataRepository;
    @Mock private ImageDataRepository imageDataRepository;
    @Mock private AdditionalProductDataRepository additionalProductDataRepository;
    @Mock private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    @Mock private ProductDataDisabledRepository productDataDisabledRepository;
    @Mock private AWSRequestConfig awsRequestConfig;

    @Test
    void testUpdateStatusQueue_insertNewStatus() {
        String messageId = "1234";
        ETLMessageDTO dto = new ETLMessageDTO();
        dto.setReferenceId("ref-1");
        dto.setSearchTerm("books");
        dto.setCategoryKey("cat-1");
        dto.setRetailerKey("amazon");
        dto.setRetailerName("Amazon");

        when(etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(
                any(), any(), any(), any())).thenReturn(Optional.empty());

        amazonDataService.updateStatusQueue(dto, messageId, EtlQueueStatusData.Status.STARTED);

        verify(etlQueueStatusDataRepository).save(any(EtlQueueStatusData.class));
        verify(etlQueueStatusDataRepository).flush();
    }

    @Test
    void testPopulateProductData_savesEntitiesCorrectly() {
        JSONObject productJson = new JSONObject("""
        {
            "ASIN": "B001234",
            "DetailPageURL": "http://example.com",
            "ItemInfo": {
                "Title": { "DisplayValue": "Test Product" },
                "Features": { "DisplayValues": ["Feature A", "Feature B"] },
                "ByLineInfo": { "Brand": { "DisplayValue": "TestBrand" } },
                "CustomerReviews": { "Rating": 4.5, "TotalReviewCount": 10 },
                "ProductInfo": {
                    "Color": { "DisplayValue": "Red" },
                    "Size": { "DisplayValue": "L" }
                },
                "ManufactureInfo": {
                    "ItemPartNumber": { "DisplayValue": "123ABC" },
                    "Model": { "DisplayValue": "ModelX" }
                }
            },
            "Offers": {
                "Listings": [{
                    "Price": { "Amount": "29.99" }
                }]
            },
            "Images": {
                "Primary": {
                    "Large": { "URL": "http://img.large" },
                    "Medium": { "URL": "http://img.medium" }
                }
            }
        }
    """);

        List<JSONObject> responseData = List.of(productJson);
        ETLMessageDTO messageDTO = new ETLMessageDTO();
        messageDTO.setSearchTerm("electronics");
        messageDTO.setL1CategoryName("Gadgets");
        messageDTO.setL2CategoryName("Smartphones");
        messageDTO.setCategoryKey("cat123");
        messageDTO.setRetailerKey("amazon");
        messageDTO.setRetailerName("Amazon");

        when(productDataDisabledRepository.findProductsByFeedSource(
                any(), any(), any(), any())).thenReturn(List.of());
        when(productDataRepository.findById("B001234")).thenReturn(Optional.empty());
        when(additionalProductDataRepository.findById("B001234")).thenReturn(Optional.empty());
        when(priceDataRepository.findById("B001234")).thenReturn(Optional.empty());
        when(awsRequestConfig.isDeleteProducts()).thenReturn(true);
        when(awsRequestConfig.getGracePeriodDays()).thenReturn(5);

        List<String> productIds = new ArrayList<>(List.of("B001234", "B009999"));
        amazonDataService.populateProductData(responseData, messageDTO, productIds);

        verify(productDataRepository).saveAll(anyList());
        verify(priceDataRepository).saveAll(anyList());
        verify(imageDataRepository).saveAll(anyList());
        verify(additionalProductDataRepository).saveAll(anyList());
        verify(productDataRepository).markForDeletion(eq(List.of("B009999")), any());
    }


}
