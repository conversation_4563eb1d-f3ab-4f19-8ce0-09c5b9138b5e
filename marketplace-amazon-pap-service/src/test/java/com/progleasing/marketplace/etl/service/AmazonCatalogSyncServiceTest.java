package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.progleasing.marketplace.etl.config.AWSRequestConfig;
import com.progleasing.marketplace.etl.repositories.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.client.RestTemplate;

import java.util.function.Consumer;

import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class AmazonCatalogSyncServiceTest {
    @Mock
    private AWSRequestConfig awsRequestConfig;

    @Spy
    private ObjectMapper objectMapper;

    @InjectMocks
    AmazonCatalogSyncService amazonCatalogSyncService;

    @Mock
    private ProductDataRepository productDataRepository;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private AmazonDataService amazonDataService;


    @BeforeEach
    void setUp() {
        Mockito.reset(restTemplate);
        MockitoAnnotations.openMocks(this);
        this.amazonCatalogSyncService = new AmazonCatalogSyncService(this.awsRequestConfig,this.objectMapper,this.productDataRepository,
                this.amazonDataService,this.restTemplate);
        when(awsRequestConfig.getAccessKey()).thenReturn("access");
        when(awsRequestConfig.getSecretKey()).thenReturn("secret");
        when(awsRequestConfig.getRegion()).thenReturn("us-east-1");
        when(awsRequestConfig.getHost()).thenReturn("example.com");
        when(awsRequestConfig.getPartnerTag()).thenReturn("partner-tag");
    }

    @Test
    void testSyncCatalogItems_noItemsInSearchResult() throws Exception {
        String messageBody = "{" +
                "\"categoryKey\":\"electronics\"," +
                "\"searchTerm\":\"smartphone\"," +
                "\"retailerName\":\"Amazon\"," +
                "\"retailerKey\":\"amazon\"," +
                "\"minPrice\":100," +
                "\"maxPrice\":1000," +
                "\"referenceId\":\"ref-123456789\"" +
                "}";
        String messageId = "msg123";
        String responseJson = "{" +
                "\"SearchResult\":{}" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);

        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(), eq(String.class)))
                .thenReturn(responseEntity);
        amazonCatalogSyncService.syncCatalogItems(messageBody, messageId);
        verify(amazonDataService, never()).populateProductData(any(),any(),any());
    }

    @Test
    void testSyncCatalogItems_emptySearchResult() throws Exception {
        String messageBody = "{" +
                "\"categoryKey\":\"electronics\"," +
                "\"searchTerm\":\"laptop\"," +
                "\"retailerName\":\"Amazon\"," +
                "\"retailerKey\":\"amazon\"," +
                "\"minPrice\":500," +
                "\"maxPrice\":2000," +
                "\"referenceId\":\"ref-987654321\"" +
                "}";
        String messageId = "msg456";
        String responseJson = "{" +
                "\"SearchResult\":{\"Items\":[]}" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);

        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(), eq(String.class)))
                .thenReturn(responseEntity);
        amazonCatalogSyncService.syncCatalogItems(messageBody, messageId);
        verify(amazonDataService, never()).populateProductData(any(),any(),any());
    }

    @Test
    void testSyncCatalogItems_itemsPresentInSearchResult() throws Exception {
        String messageBody = "{" +
                "\"categoryKey\":\"electronics\"," +
                "\"searchTerm\":\"camera\"," +
                "\"retailerName\":\"Amazon\"," +
                "\"retailerKey\":\"amazon\"," +
                "\"minPrice\":200," +
                "\"maxPrice\":1500," +
                "\"referenceId\":\"ref-456789123\"" +
                "}";
        String messageId = "msg789";
        String responseJson = "{" +
                "\"SearchResult\":{\"Items\":[{" +
                "\"ASIN\":\"B000123456\"," +
                "\"ItemInfo\":{\"Title\":{\"DisplayValue\":\"Sample Camera\"}," +
                "\"ByLineInfo\":{\"Brand\":{\"DisplayValue\":\"Canon\"}}," +
                "\"CustomerReviews\":{\"Rating\":4.5,\"TotalReviewCount\":120}}," +
                "\"Offers\":{\"Listings\":[{\"Price\":{\"Amount\":\"499.99\"}}]}," +
                "\"Images\":{\"Primary\":{\"Large\":{\"URL\":\"https://example.com/image_large.jpg\"}}}" +
                "}]}" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);

        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(), eq(String.class)))
                .thenReturn(responseEntity);
        amazonCatalogSyncService.syncCatalogItems(messageBody, messageId);
        verify(amazonDataService, times(1)).populateProductData(any(),any(),any());
    }
}
