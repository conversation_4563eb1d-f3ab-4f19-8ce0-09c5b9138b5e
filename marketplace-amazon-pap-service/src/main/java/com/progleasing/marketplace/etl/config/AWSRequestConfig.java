package com.progleasing.marketplace.etl.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "amazon.etl")
@Setter
@Getter
@Configuration
public class AWSRequestConfig {
    private String host;
    private String region;
    private String accessKey;
    private String secretKey;
    private String partnerTag;
    private int maxProductsToImport;
    private int gracePeriodDays;
    private boolean deleteProducts;
}
