package com.progleasing.marketplace.etl.service;

import com.progleasing.marketplace.etl.config.AWSRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.repositories.*;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class AmazonDataService {


    private ProductDataRepository productDataRepository;
    private PriceDataRepository priceDataRepository;
    private ImageDataRepository imageDataRepository;
    private AdditionalProductDataRepository additionalProductDataRepository;
    private EtlQueueStatusDataRepository etlQueueStatusDataRepository;
    private ProductDataDisabledRepository productDataDisabledRepository;
    private AWSRequestConfig awsRequestConfig;

    @Transactional
    public void updateStatusQueue(ETLMessageDTO messageDTO, String messageId, EtlQueueStatusData.Status status) {
        try {
            String messageData = String.format(
                    "%s|%s|%s|%s|%s|%s|%s|%s|%s",
                    messageId,
                    messageDTO.getSearchTerm(),
                    messageDTO.getCategoryKey(),
                    messageDTO.getRetailerName(),
                    messageDTO.getRetailerKey(),
                    messageDTO.getMinPrice(),
                    messageDTO.getMaxPrice(),
                    messageDTO.getL1CategoryName(),
                    messageDTO.getL2CategoryName()
            );
            EtlQueueStatusData.EtlStep etlStep = EtlQueueStatusData.EtlStep.PRODUCT_SEARCH;
            Optional<EtlQueueStatusData> existing =
                    etlQueueStatusDataRepository.findByEtlIDAndRetailerSearchAndEtlStepAndMessageId(
                            messageDTO.getReferenceId(),
                            ProductData.FeedSource.amazon.name(),
                            etlStep,
                            messageId
                    );
            EtlQueueStatusData etlQueueStatusData;
            if (existing.isPresent()) {
                etlQueueStatusData = existing.get();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
                log.info("Amazon PAP Catalog sync {} for message ID {} category {}",status.toString(), messageId, messageDTO.getCategoryKey());
            } else {
                etlQueueStatusData = new EtlQueueStatusData();
                etlQueueStatusData.setStatus(status);
                etlQueueStatusData.setEtlStep(etlStep);
                etlQueueStatusData.setEtlID(messageDTO.getReferenceId());
                etlQueueStatusData.setMessageId(messageId);
                etlQueueStatusData.setSqsMessageData(messageData);
                etlQueueStatusData.setRetailerSearch(ProductData.FeedSource.amazon.name());
                if (status == EtlQueueStatusData.Status.STARTED) {
                    etlQueueStatusData.setEtlStart(Timestamp.from(Instant.now()));
                }
                etlQueueStatusData.setLastUpdate(Timestamp.from(Instant.now()));
            }
            etlQueueStatusDataRepository.save(etlQueueStatusData);
            etlQueueStatusDataRepository.flush();
            log.info("Queue status saved successfully for message ID {} import id {} to {}", messageId,messageDTO.getReferenceId(),status);        }catch (Exception e) {
            log.error("Error updating queue status for {} referenceId {}",messageId,messageDTO.getReferenceId());
            throw e;
        }
    }


    @Transactional
    public void populateProductData(List<JSONObject> responseData, ETLMessageDTO messageDTO, List<String> productIds) {
        try {
            List<ProductData> productDataList = new ArrayList<>();
            List<AdditionalProductData> additionalProductData = new ArrayList<>();
            List<PriceData> priceDataList = new ArrayList<>();
            List<ImageData> imageDataList = new ArrayList<>();
            List<String> disabledProductIds = new ArrayList<>();
            List<ProductDataDisabled> productDataDisabledList = productDataDisabledRepository.findProductsByFeedSource(messageDTO.getSearchTerm(),messageDTO.getL1CategoryName(), messageDTO.getL2CategoryName(),ProductData.FeedSource.amazon);
            if(!productDataDisabledList.isEmpty()){
                disabledProductIds = productDataDisabledList.stream().map(ProductDataDisabled :: getId).collect(Collectors.toList());
            }
            for (JSONObject item : responseData) {
                String productId = item.optString("ASIN");
                if(!disabledProductIds.contains(productId)){
                    productDataList.add(
                            extractProductData(item,
                                    messageDTO)
                    );
                    Optional.of(extractAdditionalProductData(item)).ifPresent(additionalProductData::add);
                    Optional.ofNullable(extractPriceData(item)).ifPresent(priceDataList::add);
                    Optional.of(extractImageData(item)).ifPresent(imageDataList::addAll);
                }
            }

            if (!CollectionUtils.isEmpty(productDataList)) {
                productDataRepository.saveAll(productDataList);
            }
            if (!CollectionUtils.isEmpty(priceDataList)) {
                priceDataRepository.saveAll(priceDataList);
            }
            if (!CollectionUtils.isEmpty(imageDataList)) {
                imageDataRepository.saveAll(imageDataList);
            }
            if(!CollectionUtils.isEmpty(additionalProductData)) {
                additionalProductDataRepository.saveAll(additionalProductData);
            }
            productIds.removeAll(productDataList.stream().map(ProductData::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(productIds) && awsRequestConfig.isDeleteProducts()) {
                productDataRepository.markForDeletion(productIds, Timestamp.valueOf(LocalDateTime.now().plusDays(awsRequestConfig.getGracePeriodDays())));
                log.info("Product marked for deletion, productIds :{}", productIds);
            }else{
                log.info("No products there marked for deletion");
            }
            log.info("Adding products {} for category {} search term {}",productDataList.size(),messageDTO.getCategoryKey(),messageDTO.getSearchTerm());
        } catch (Exception e) {
            log.error("Error saving data to DB for category {} searchTerms {}",messageDTO.getCategoryKey(),messageDTO.getSearchTerm(),e);
            throw new RuntimeException(e);
        }
    }

    private ProductData extractProductData(JSONObject item , ETLMessageDTO messageDTO) {
        ProductData newProduct = buildProductDataFromItem(item, messageDTO);
        Optional<ProductData> existingOpt = productDataRepository.findById(newProduct.getId());
        if (existingOpt.isPresent()) {
            ProductData existing = existingOpt.get();
            if (hasProductChanged(existing, newProduct)) {
                updateProduct(existing, newProduct);
                return existing;
            }
        }
        return newProduct;
    }

    private AdditionalProductData extractAdditionalProductData(JSONObject item) {
        AdditionalProductData newProduct = buildAdditionalProductDataFromItem(item);
        Optional<AdditionalProductData> existingOpt = additionalProductDataRepository.findById(newProduct.getId());
        if (existingOpt.isPresent()) {
            AdditionalProductData existing = existingOpt.get();
            if (hasAdditionalProductDataChanged(existing, newProduct)) {
                updateAdditionalProduct(existing, newProduct);
                return existing;
            }
        }
        return newProduct;
    }

    private AdditionalProductData buildAdditionalProductDataFromItem(JSONObject item) {
        String asin = item.optString("ASIN");
        JSONObject itemInfo = item.optJSONObject("ItemInfo");
        AdditionalProductData additionalProductData = new AdditionalProductData();
        JSONObject productInfo = itemInfo.optJSONObject("ProductInfo");
        additionalProductData.setId(asin);
        if (productInfo != null) {
            JSONObject color = productInfo.optJSONObject("Color");
            if (color != null) {
                String colorStr =  color.optString("DisplayValue", "");
                additionalProductData.setColor(colorStr);
            }
            JSONObject size = productInfo.optJSONObject("Size");
            if (size != null) {
                String sizeStr =  size.optString("DisplayValue", "");
                additionalProductData.setSize(sizeStr);
            }
        }
        JSONObject manufactureInfo = itemInfo.optJSONObject("ManufactureInfo");
        if (manufactureInfo != null) {
            JSONObject itemPartNumber = manufactureInfo.optJSONObject("ItemPartNumber");
            if (itemPartNumber != null) {
                String itemPartNumStr =  itemPartNumber.optString("DisplayValue", "");
                additionalProductData.setItemPartNumber(itemPartNumStr);
            }
            JSONObject model = manufactureInfo.optJSONObject("Model");
            if (model != null) {
                String modelStr =  model.optString("DisplayValue", "");
                additionalProductData.setModel(modelStr);
            }
        }
        return additionalProductData;
    }

    private ProductData buildProductDataFromItem(JSONObject item,  ETLMessageDTO messageDTO) {
        String asin = item.optString("ASIN");
        JSONObject itemInfo = item.optJSONObject("ItemInfo");

        ProductData productData = new ProductData();
        productData.setId(asin);
        productData.setName(extractName(itemInfo));
        productData.setDescription(extractName(itemInfo));
        productData.setLongDescription(extractDescription(itemInfo));
        productData.setRetailerName(messageDTO.getRetailerName());
        //productData.setRetailerKey(retailerName.toLowerCase().replaceAll("[^a-z0-9]", ""));
        productData.setRetailerKey(messageDTO.getRetailerKey());
        productData.setBrand(extractBrand(itemInfo));
        productData.setRatingAverage(extractRatingAverage(itemInfo));
        productData.setRatingCount(extractRatingCount(itemInfo));
        String url = item.optString("DetailPageURL", "");
        productData.setProductTrackingUrl(url);
        productData.setAffiliateAddToCartUrl(null);
        productData.setLastModified(Timestamp.from(Instant.now()));
        productData.setLastSentToCT(null);
        productData.setCategoryKey(messageDTO.getCategoryKey());
        productData.setSearchTerm(messageDTO.getSearchTerm());
        productData.setL1CategoryName(messageDTO.getL1CategoryName());
        productData.setL2CategoryName(messageDTO.getL2CategoryName());
        productData.setFeedSource(ProductData.FeedSource.amazon);
        productData.setLeasable(false);
        return productData;
    }

    private boolean hasProductChanged(ProductData existing, ProductData incoming) {
        return !Objects.equals(trimToNull(existing.getName()), trimToNull(incoming.getName())) ||
                !Objects.equals(trimToNull(existing.getDescription()), trimToNull(incoming.getDescription())) ||
                !Objects.equals(trimToNull(existing.getLongDescription()), trimToNull(incoming.getLongDescription())) ||
                !Objects.equals(trimToNull(existing.getRetailerName()), trimToNull(incoming.getRetailerName())) ||
                !Objects.equals(trimToNull(existing.getRetailerKey()), trimToNull(incoming.getRetailerKey())) ||
                !Objects.equals(trimToNull(existing.getBrand()), trimToNull(incoming.getBrand())) ||
                existing.getRatingAverage() == null ? incoming.getRatingAverage() != null : existing.getRatingAverage().compareTo(incoming.getRatingAverage()) != 0 ||
                existing.getRatingCount() != incoming.getRatingCount() ||
                !Objects.equals(trimToNull(existing.getProductTrackingUrl()), trimToNull(incoming.getProductTrackingUrl())) ||
                !Objects.equals(trimToNull(existing.getAffiliateAddToCartUrl()), trimToNull(incoming.getAffiliateAddToCartUrl()));
    }

    private boolean hasAdditionalProductDataChanged(AdditionalProductData existing, AdditionalProductData incoming) {
        return !Objects.equals(trimToNull(existing.getColor()), trimToNull(incoming.getColor())) ||
                !Objects.equals(trimToNull(existing.getSize()), trimToNull(incoming.getSize())) ||
                !Objects.equals(trimToNull(existing.getItemPartNumber()), trimToNull(incoming.getItemPartNumber())) ||
                !Objects.equals(trimToNull(existing.getModel()), trimToNull(incoming.getModel()));
    }

    private String trimToNull(String s) {
        return (s == null || s.trim().isEmpty()) ? null : s.trim();
    }


    private void updateProduct(ProductData existing, ProductData updated) {
        existing.setName(updated.getName());
        existing.setDescription(updated.getDescription());
        existing.setLongDescription(updated.getLongDescription());
        existing.setRetailerName(updated.getRetailerName());
        existing.setRetailerKey(updated.getRetailerKey());
        existing.setBrand(updated.getBrand());
        existing.setRatingAverage(updated.getRatingAverage());
        existing.setRatingCount(updated.getRatingCount());
        existing.setProductTrackingUrl(updated.getProductTrackingUrl());
        existing.setAffiliateAddToCartUrl(updated.getAffiliateAddToCartUrl());
        existing.setCategoryKey(String.join(",", new HashSet<>(List.of(existing.getCategoryKey().split(",")))) + "," + updated.getCategoryKey());
        existing.setSearchTerm(String.join(",", new HashSet<>(List.of(existing.getSearchTerm().split(",")))) + "," + updated.getSearchTerm());
        existing.setLastModified(Timestamp.from(Instant.now()));
    }

    private void updateAdditionalProduct(AdditionalProductData existing, AdditionalProductData updated) {
        existing.setColor(updated.getColor());
        existing.setSize(updated.getSize());
        existing.setModel(updated.getModel());
        existing.setItemPartNumber(updated.getItemPartNumber());
        existing.setLastModified(Timestamp.from(Instant.now()));
    }


    private PriceData extractPriceData(JSONObject item ) {
        Optional<PriceData> newPriceDataOpt = buildPriceDataFromItem(item);
        if (newPriceDataOpt.isPresent()) {
            PriceData newPriceData = newPriceDataOpt.get();
            Optional<PriceData> existingOpt = priceDataRepository.findById(newPriceData.getId());
            if (existingOpt.isPresent()) {
                PriceData existing = existingOpt.get();
                if (hasPriceChanged(existing, newPriceData)) {
                    existing.setPrice(newPriceData.getPrice());
                    existing.setLastModified(Timestamp.from(Instant.now()));
                    return existing;
                }
            }
            return newPriceData;
        }
        return null;
    }

    private Optional<PriceData> buildPriceDataFromItem(JSONObject item) {
        String asin = item.optString("ASIN");
        JSONObject offers = item.optJSONObject("Offers");
        if (offers != null) {
            JSONArray listings = offers.optJSONArray("Listings");
            if (listings != null && listings.length() > 0) {
                JSONObject priceNode = listings.getJSONObject(0).optJSONObject("Price");
                if (priceNode != null) {
                    BigDecimal price = new BigDecimal(priceNode.optString("Amount", "0.00"));
                    PriceData priceData = new PriceData();
                    priceData.setId(asin);
                    priceData.setPrice(price);
                    priceData.setLastModified(Timestamp.from(Instant.now()));
                    return Optional.of(priceData);
                }
            }
        }
        return Optional.empty();
    }


    private boolean hasPriceChanged(PriceData existing, PriceData incoming) {
        return existing.getPrice() == null || incoming.getPrice() == null ||
                existing.getPrice().compareTo(incoming.getPrice()) != 0;
    }

    private List<ImageData> extractImageData(JSONObject item ) {
        String asin = item.optString("ASIN");
        JSONObject imagesNode = item.optJSONObject("Images");
        List<ImageData> images = new ArrayList<>();
        if (imagesNode != null) {
            JSONObject primary = imagesNode.optJSONObject("Primary");
            if (primary != null) {
                for (String size : List.of("medium", "Large")) {
                    if (primary.has(size)) {
                        JSONObject image = primary.getJSONObject(size);
                        String imageUrl = image.optString("URL", "");
                        if (!imageUrl.isEmpty()) {
                            ImageData imageData = new ImageData();
                            imageData.setId(asin);
                            imageData.setImageUrl(imageUrl);
                            imageData.setLastModified(Timestamp.from(Instant.now()));
                            images.add(imageData);
                        }
                    }
                }
            }
        }
        return images;
    }


    private String extractName(JSONObject itemInfo) {
        if (itemInfo != null && itemInfo.optJSONObject("Title") != null) {
            return itemInfo.getJSONObject("Title").optString("DisplayValue", "");
        }
        return "";
    }

    private String extractDescription(JSONObject itemInfo) {
        if (itemInfo != null && itemInfo.optJSONObject("Features") != null) {
            JSONArray featuresArray = itemInfo.getJSONObject("Features").optJSONArray("DisplayValues");
            if (featuresArray != null) {
                return String.join(", ", featuresArray.toList().stream().map(Object::toString).collect(Collectors.toList()));
            }
        }
        return "";
    }


    private String extractBrand(JSONObject itemInfo) {
        if (itemInfo != null) {
            JSONObject byLineInfo = itemInfo.optJSONObject("ByLineInfo");
            if (byLineInfo != null) {
                JSONObject brand = byLineInfo.optJSONObject("Brand");
                if (brand != null) {
                    return brand.optString("DisplayValue", "");
                }
            }
        }
        return "";
    }


    private BigDecimal extractRatingAverage(JSONObject itemInfo) {
        if (itemInfo != null && itemInfo.optJSONObject("CustomerReviews") != null) {
            return BigDecimal.valueOf(itemInfo.getJSONObject("CustomerReviews").optDouble("Rating", 0.0));
        }
        return BigDecimal.ZERO;
    }

    private int extractRatingCount(JSONObject itemInfo) {
        if (itemInfo != null && itemInfo.optJSONObject("CustomerReviews") != null) {
            return itemInfo.getJSONObject("CustomerReviews").optInt("TotalReviewCount", 0);
        }
        return 0;
    }

}
