package com.progleasing.marketplace.etl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.progleasing.marketplace.etl.auth.AWSV4Auth;
import com.progleasing.marketplace.etl.config.AWSRequestConfig;
import com.progleasing.marketplace.etl.dto.ETLMessageDTO;
import com.progleasing.marketplace.etl.entity.*;
import com.progleasing.marketplace.etl.repositories.*;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.progleasing.marketplace.etl.constants.PLConstants.*;

@Service
@Slf4j
@AllArgsConstructor
public class AmazonCatalogSyncService {


    private AWSRequestConfig awsRequestConfig;
    private ObjectMapper objectMapper;
    private ProductDataRepository productDataRepository;
    private AmazonDataService amazonDataService;
    private RestTemplate restTemplate;


    public void syncCatalogItems(String messagePayload, String messageId) throws Exception {
        ETLMessageDTO messageDTO = objectMapper.readValue(messagePayload, ETLMessageDTO.class);
        log.info("Processing Amazon PAA for category {} searchTerm {} retailerName {}", messageDTO.getCategoryKey(),
                messageDTO.getSearchTerm(), messageDTO.getRetailerName());
        amazonDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.STARTED);
        List<ProductData> searchTermProductDataList = productDataRepository.findProductsBySearchTerm(
                messageDTO.getSearchTerm(),ProductData.FeedSource.amazon);
        List<String> productIds = searchTermProductDataList.stream().map(ProductData::getId).collect(Collectors.toList());

        try {
            List<JSONObject> allItems = callAmazonPapApiWithRetry(messageDTO);
            if(!CollectionUtils.isEmpty(allItems)) {
                amazonDataService.populateProductData(allItems, messageDTO, productIds);
            }
            amazonDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.COMPLETED);
        } catch (Exception e) {
            log.error("Catalog item sync failed for category ID {}: {}", messageDTO.getCategoryKey(), e.getMessage(), e);
            amazonDataService.updateStatusQueue(messageDTO, messageId, EtlQueueStatusData.Status.ERROR_RESTARTING);
            throw e;
        }
    }

    private List<JSONObject> callAmazonPapApiWithRetry(ETLMessageDTO messageDTO) {
        int retries = 0;
        int maxRetries = 5;
        long delay = 1000;
        List<JSONObject> allItems = new ArrayList<>();
        while (retries < maxRetries) {
            try {
                int currentPage = 1;
                int totalPages = 1;
                do {
                    ObjectNode payload = createPayload(messageDTO, currentPage);
                    String payloadJson = objectMapper.writeValueAsString(payload);
                    TreeMap<String, String> headersMap = createHeaders(payload);
                    HttpHeaders headers = new HttpHeaders();
                    headersMap.forEach(headers::set);
                    String url = "https://" + awsRequestConfig.getHost() + PATH;
                    HttpEntity<String> requestEntity = new HttpEntity<>(payloadJson, headers);
                    ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
                    if (responseEntity.getStatusCode().value() == 200) {
                        log.debug("Received response for search term {} on page {}", messageDTO.getSearchTerm(), currentPage);
                        String jsonResponse = responseEntity.getBody();
                        JSONObject responseData = new JSONObject(jsonResponse);
                        if (currentPage == 1 && responseData.has("SearchResult")) {
                            JSONObject searchResult = responseData.getJSONObject("SearchResult");
                            if (searchResult.has("TotalResultCount")) {
                                int totalResults = searchResult.getInt("TotalResultCount");
                                totalPages = (int) Math.ceil((double) totalResults / awsRequestConfig.getMaxProductsToImport());
                            }
                        }
                        JSONArray items = responseData.optJSONObject("SearchResult").optJSONArray("Items");
                        if (items != null) {
                            for (int i = 0; i < items.length(); i++) {
                                allItems.add(items.getJSONObject(i));
                            }
                        }
                    } else {
                        logError(responseEntity, currentPage);
                        break;
                    }
                    currentPage++;
                } while ((currentPage <= totalPages && currentPage <= 10) && allItems.size() < awsRequestConfig.getMaxProductsToImport());
                return allItems;
            } catch (HttpClientErrorException.TooManyRequests e) {
                log.warn("Throttled on Amazon PAP call for category {}, retrying after {}ms", messageDTO.getCategoryKey(), delay);
                try {
                    TimeUnit.MILLISECONDS.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return allItems;
                }
                delay *= 2;
                retries++;
            } catch (Exception ex) {
                log.error("Amazon PAP API call failed: {}", ex.getMessage(), ex);
                throw new RuntimeException("API call failed", ex);
            }
        }
        log.error("Amazon PAP API failed for category {} after {} retries", messageDTO.getCategoryKey(), maxRetries);
        throw new RuntimeException("API failed after retries");
    }

    private TreeMap<String, String> createHeaders(ObjectNode payload) throws Exception {
        TreeMap<String, String> headers = new TreeMap<>();
        headers.put("host", awsRequestConfig.getHost());
        headers.put("content-type", "application/json; charset=utf-8");
        headers.put("x-amz-target", "com.amazon.paapi5.v1.ProductAdvertisingAPIv1.SearchItems");
        headers.put("content-encoding", "amz-1.0");

        AWSV4Auth awsv4Auth = new AWSV4Auth.Builder(awsRequestConfig.getAccessKey(), awsRequestConfig.getSecretKey())
                .path(PATH)
                .region(awsRequestConfig.getRegion())
                .service("ProductAdvertisingAPI")
                .httpMethodName("POST")
                .headers(headers)
                .payload(objectMapper.writeValueAsString(payload))
                .build();

        headers.putAll(awsv4Auth.getHeaders());
        return headers;
    }

    private ObjectNode createPayload(ETLMessageDTO messageDTO, int currentPage) {
        ObjectNode payload = objectMapper.createObjectNode();
        payload.put("Marketplace", MARKET_PLACE);
        payload.put("PartnerType", PARTNER_TYPE);
        payload.put("PartnerTag", awsRequestConfig.getPartnerTag());
        payload.put("Keywords", messageDTO.getSearchTerm());
        if (messageDTO.getMinPrice() != null && messageDTO.getMinPrice().compareTo(BigDecimal.ZERO) > 0) {
            int minPriceCents = messageDTO.getMinPrice().multiply(BigDecimal.valueOf(100)).intValue();
            payload.put("MinPrice", minPriceCents);
        }
        if (messageDTO.getMaxPrice() != null && messageDTO.getMaxPrice().compareTo(BigDecimal.ZERO) > 0) {
            int maxPriceCents = messageDTO.getMaxPrice().multiply(BigDecimal.valueOf(100)).intValue();
            payload.put("MaxPrice", maxPriceCents);
        }
        payload.put("SearchIndex", SEARCH_INDEX);
        payload.put("ItemCount", awsRequestConfig.getMaxProductsToImport());
        payload.put("ItemPage", currentPage);
        payload.put("SortBy", SORT_INDEX);

        ArrayNode resources = objectMapper.createArrayNode();
        for (String resource : RESOURCES) {
            resources.add(resource);
        }
        payload.set("Resources", resources);

        return payload;
    }

    private void logError(ResponseEntity<String> response, int currentPage) {
        try {
            String jsonResponse = response.getBody();
            JSONObject json = new JSONObject(jsonResponse);

            if (json.has("Errors")) {
                JSONArray errorArray = json.getJSONArray("Errors");
                for (int i = 0; i < errorArray.length(); i++) {
                    JSONObject errorObject = errorArray.getJSONObject(i);
                    log.error("Error Code: {}, Message: {}",
                            errorObject.optString("Code", "UnknownCode"),
                            errorObject.optString("Message", "Unknown error message"));
                }
            } else {
                log.error("Error Code: InternalFailure, Message: Unknown processing error on page {}", currentPage);
            }
        } catch (Exception e) {
            log.error("Failed to log error for page {}: {}", currentPage, e.getMessage(), e);
        }
    }


}
