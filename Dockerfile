FROM amazoncorretto:21-alpine3.18
WORKDIR /app

# Install tools
RUN apk add --no-cache curl ca-certificates bash dos2unix

# Copy project files and make gradlew executable
COPY . .
RUN chmod +x ./gradlew && dos2unix ./gradlew
RUN ./gradlew bootJar -x test
RUN ls -l /app/build/libs/



# Expose app port
EXPOSE 8082

# Add RDS root certs
RUN curl "https://s3.amazonaws.com/rds-downloads/rds-combined-ca-bundle.pem" -o /usr/local/share/ca-certificates/rds-combined-ca-bundle.crt
RUN update-ca-certificates

## Install summon
RUN curl -sSL https://raw.githubusercontent.com/cyberark/summon/main/install.sh | bash && rm -rf /tmp/*
#
## Install summon-conjur provider
RUN curl -sSL https://raw.githubusercontent.com/cyberark/summon-conjur/main/install.sh | bash && rm -rf /tmp/*


# Set up truststore
RUN cp /usr/lib/jvm/java-21-amazon-corretto/lib/security/cacerts /app/truststore.jks

# Set Conjur env var
COPY secrets.yml /app/secrets.yml

EXPOSE 8080

ENV CONJUR_AUTHN_TOKEN_FILE=/run/conjur/access-token

# Copy and prepare entry point script
COPY entryPoint.sh /app/entryPoint.sh
RUN chmod +x /app/entryPoint.sh && dos2unix /app/entryPoint.sh
ENTRYPOINT ["/app/entryPoint.sh"]
